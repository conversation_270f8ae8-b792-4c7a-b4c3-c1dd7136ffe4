package com.paic.ncbs.claim.model.vo.investigate;


import com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@SuppressWarnings("serial")
@ApiModel(description = "审核记录表VO")
public class InvestigateTaskAuditVO extends InvestigateTaskAuditDTO {

	@ApiModelProperty(value = "审核说明/退回补充")
    private String rejectReasonName;

	@ApiModelProperty(value = "用户真实姓名-用户ID（UM账号）审核人")
    private String reviewUserUmName;

	@ApiModelProperty(value = "用户真实姓名审核人")
	private String reviewUserName;

	@ApiModelProperty(value = "用户真实姓名-用户ID（UM账号）发起人")
    private String initiatorUmName;

    @ApiModelProperty(value = "是否包含公估费(Y/N)")
    private String isHasAdjustingFee;

	@ApiModelProperty(value = "公估费")
	private BigDecimal commonEstimateFee;

	@ApiModelProperty(value = "用户真实姓名发起人")
	private String initiatorName;


	public String getInitiatorName() {
		return initiatorName;
	}

	public void setInitiatorName(String initiatorName) {
		this.initiatorName = initiatorName;
	}

	@ApiModelProperty("审批通过记录")
	private InvestigateScoreDTO investigateScoreDTO ;

	public InvestigateScoreDTO getInvestigateScoreDTO() {
		return investigateScoreDTO;
	}

	public void setInvestigateScoreDTO(InvestigateScoreDTO investigateScoreDTO) {
		this.investigateScoreDTO = investigateScoreDTO;
	}

	public String getReviewUserName() {
		return reviewUserName;
	}

	public void setReviewUserName(String reviewUserName) {
		this.reviewUserName = reviewUserName;
	}

	public String getIsHasAdjustingFee() {
		return isHasAdjustingFee;
	}

	public void setIsHasAdjustingFee(String isHasAdjustingFee) {
		this.isHasAdjustingFee = isHasAdjustingFee;
	}
    
    
	public String getRejectReasonName() {
		return rejectReasonName;
	}

	public void setRejectReasonName(String rejectReasonName) {
		this.rejectReasonName = rejectReasonName;
	}

	public String getReviewUserUmName() {
		return reviewUserUmName;
	}

	public void setReviewUserUmName(String reviewUserUmName) {
		this.reviewUserUmName = reviewUserUmName;
	}

	public String getInitiatorUmName() {
		return initiatorUmName;
	}

	public void setInitiatorUmName(String initiatorUmName) {
		this.initiatorUmName = initiatorUmName;
	}


	@Override
	public BigDecimal getCommonEstimateFee() {
		return commonEstimateFee;
	}

	@Override
	public void setCommonEstimateFee(BigDecimal commonEstimateFee) {
		this.commonEstimateFee = commonEstimateFee;
	}
}