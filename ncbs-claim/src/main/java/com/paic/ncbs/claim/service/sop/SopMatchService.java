package com.paic.ncbs.claim.service.sop;

import com.paic.ncbs.claim.model.vo.sop.SopMainVO;

import java.util.List;

/**
 * SOP匹配服务接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface SopMatchService {

    /**
     * 根据案件信息匹配SOP规则
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param taskDefinitionBpmKey 当前环节代码
     * @return 匹配的SOP列表
     */
    List<SopMainVO> matchSopByCase(String reportNo, Integer caseTimes, String taskDefinitionBpmKey);

}
