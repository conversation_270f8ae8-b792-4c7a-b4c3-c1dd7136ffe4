package com.paic.ncbs.claim.service.risk.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OmpRequest;
import com.paic.ncbs.claim.model.dto.estimate.InvoiceJsonParamDTO;
import com.paic.ncbs.claim.model.dto.estimate.InvoiceJsonResponseDTO;
import com.paic.ncbs.claim.model.dto.estimate.RepeatReceiptDTO;
import com.paic.ncbs.claim.model.vo.estimate.RepeatReceiptVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.risk.RiskControlService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-07-03 10:11
 */
@RefreshScope
@Service
@Slf4j
public class RiskControlServiceImpl implements RiskControlService {

    @Autowired
    private OmpRequest ompRequest;

    @Autowired
    private OcasMapper ocasMapper;

    @Autowired
    private MedicalBillService medicalBillService;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Value("${switch.checkRepeatReceipt:N}")
    private String switchCheckRepeatReceipt;

    @Value("${switch.checkRepeatReceiptProduct:N}")
    private String checkRepeatReceiptProduct;


    @Override
    public ResponseResult<Object> checkRepeatReceipt(RepeatReceiptVO vo) {
        LogUtil.info("checkRepeatReceipt是否存在重复发票接口：" + JSON.toJSONString(vo));
        // 配置开关
        if (ConstValues.NO.equals(switchCheckRepeatReceipt)) {
            return ResponseResult.success();
        }

        List<RepeatReceiptDTO> list = new ArrayList<>();
        this.builddto(list, vo);
        if (list.size() < 1) {
            return ResponseResult.success();
        }
        String res = "";
        try {
            LogUtil.info("checkRepeatReceipt是否存在重复发票接口,请求参数：{}",JSON.toJSONString(list));
            res = ompRequest.checkRepeatReceipt(list);
            LogUtil.info("checkRepeatReceipt是否存在重复发票接口,响应参数：{}",JSON.toJSONString(res));
        } catch (Exception e) {
            log.error("调用是否存在重复发票接口是出错", e);
        }
        try {
            if (StringUtils.isNotEmpty(res)) {
                Map<String, Object> map = this.solveResult(res);
                return ResponseResult.success(map);
            } else {
                return ResponseResult.success();
            }
        } catch (Exception e) {
            log.error("是否存在重复发票接口结果封装出错", e);
            return ResponseResult.success();
        }
    }

    private void builddto(List<RepeatReceiptDTO> list, RepeatReceiptVO vo) {
        List<MedicalBillInfoVO> billInfoVOS = medicalBillService.getMedicalBillInfoForPrint(vo.getReportNo(), vo.getCaseTimes());
        LogUtil.info("筛选出类型为住院（THE_0302）的数据before：{}",JSON.toJSONString(billInfoVOS));
        //筛选出类型为住院（THE_0302）的数据
        billInfoVOS = billInfoVOS.stream()
                    .filter(voo -> "THE_0302".equals(voo.getTherapyType()))
                    .collect(Collectors.toList());
        LogUtil.info("筛选出类型为住院（THE_0302）的数据after：{}",JSON.toJSONString(billInfoVOS));
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(vo.getReportNo());
        if (customerInfo == null) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL,"被保人信息为空报错");
        }
        List<String> policyNoByReportNo = ahcsPolicyInfoMapper.getPolicyNoByReportNo(vo.getReportNo());
        if(policyNoByReportNo == null || policyNoByReportNo.size() < 1){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL,"保单号码为空报错");
        }

        String productCode;
        if (ConstValues.NO.equals(checkRepeatReceiptProduct)) {
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("reportNo",vo.getReportNo());
            paramMap.put("isMain","1");
            List<Map<String,String>> mapList = ahcsPolicyInfoMapper.getProductInfo(paramMap);
            if (ListUtils.isEmptyList(mapList)) {
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL,"条款编码为空报错");
            } else {
                productCode = mapList.get(0).get("termCode");
            }
        } else {
            Map<String,String> map = ocasMapper.getPlyBaseInfo(policyNoByReportNo.get(0));
            if (map == null) {
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL,"产品代码为空报错");
            }
            productCode = map.get("productCode");
        }
        billInfoVOS.forEach(r -> {
            RepeatReceiptDTO dto = new RepeatReceiptDTO();
            dto.setReportNo(vo.getReportNo());
            dto.setReceiptCode(r.getBillNo());
            dto.setHospitalCode(r.getHospitalCode());
            dto.setCredentialsType(customerInfo.getCertificateType());
            dto.setCredentialsName(customerInfo.getName());
            dto.setCredentialsCode(customerInfo.getCertificateNo());
            //产品编码or条款编码
            dto.setProductCode(productCode);
            list.add(dto);
        });
    }

    private Map<String, Object> solveResult(String res) {
        JSONObject jsonObject = JSONObject.parseObject(res);
        JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("riskData");
        List<InvoiceJsonParamDTO> list = jsonArray.toJavaList(InvoiceJsonParamDTO.class);
        Map<String, Object> result = new HashMap<>();
        List<InvoiceJsonResponseDTO> invoiceJsonResponseDTOList = new ArrayList<>();
        AtomicBoolean flag = new AtomicBoolean(false);
        list.forEach(r -> {
            if (BaseConstant.UPPER_CASE_Y.equals(r.getReceiptRepetitions())) {
                flag.set(true);
            }
            boolean isreepat = false;
            InvoiceJsonResponseDTO invoiceJsonResponseDTO = new InvoiceJsonResponseDTO();
            invoiceJsonResponseDTO.setHospitalCode(r.getHospitalCode());
            invoiceJsonResponseDTO.setReceiptCode(r.getReceiptCode());
            if (BaseConstant.UPPER_CASE_Y.equals(r.getPaymented()) || BaseConstant.UPPER_CASE_Y.equals(r.getIfSplitlist())) {
                isreepat = true;
            }
            invoiceJsonResponseDTO.setIsRepeat(isreepat ? BaseConstant.UPPER_CASE_Y : BaseConstant.UPPER_CASE_N);
            invoiceJsonResponseDTOList.add(invoiceJsonResponseDTO);
        });
        result.put("receiptRepetitions", flag.get() ? BaseConstant.UPPER_CASE_Y : BaseConstant.UPPER_CASE_N);
        result.put("data",invoiceJsonResponseDTOList);
        return result;
    }

    /**
     * 获取CBIT（中保信）重复票据信息
     *
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public String getCBITSameInfo(String reportNo, Integer caseTimes) {
        RepeatReceiptVO repeatReceiptVO = new RepeatReceiptVO();
        repeatReceiptVO.setReportNo(reportNo);
        repeatReceiptVO.setCaseTimes(caseTimes);
        ResponseResult<Object> responseResult = checkRepeatReceipt(repeatReceiptVO);
        if (null == responseResult
                || !GlobalResultStatus.SUCCESS.getCode().equals(responseResult.getCode())
                || null == responseResult.getData()) {
            return "";
        }
        LogUtil.info("getCBITSameInfo重复发票校验,响应参数1：{}",JSON.toJSONString(responseResult));
        Map<String, Object> data = (Map<String, Object>) responseResult.getData();
        StringBuilder result = new StringBuilder();
        if (ConstValues.YES.equals(data.get("receiptRepetitions"))) {
            result.append("客户既往存在重复发票;");
        }

        List<InvoiceJsonResponseDTO> invoiceDTOList = (List<InvoiceJsonResponseDTO>) data.get("data");
        String tempRepeat =
                invoiceDTOList.stream().filter(item -> ConstValues.YES.equals(item.getIsRepeat())).map(item -> "票据" + item.getReceiptCode())
                        .distinct().collect(Collectors.joining("、"));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(tempRepeat)) {
            result.append(tempRepeat).append("为重复发票;");
        }
        LogUtil.info("getCBITSameInfo重复发票校验,响应参数2：{}",result.toString());
        return result.toString();
    }
}
