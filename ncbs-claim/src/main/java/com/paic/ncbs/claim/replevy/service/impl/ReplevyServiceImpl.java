package com.paic.ncbs.claim.replevy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.constant.ReplevyConstant;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.PaymentInfoTypeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentDutyMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.pay.SendPaymentRecordMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.replevy.dao.*;
import com.paic.ncbs.claim.replevy.dto.*;
import com.paic.ncbs.claim.replevy.entity.*;
import com.paic.ncbs.claim.replevy.service.ReplevyChargeService;
import com.paic.ncbs.claim.replevy.service.ReplevyService;
import com.paic.ncbs.claim.replevy.vo.*;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.common.ClaimCommonQueryFileInfoService;
import com.paic.ncbs.claim.service.common.ClaimWorkFlowService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 追偿明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
@Service
@RefreshScope
public class ReplevyServiceImpl implements ReplevyService {

    @Autowired
    private ClmsReplevyMainMapper clmsReplevyMainMapper;//主表
    @Autowired
    private ClmsReplevyTextMapper clmsReplevyTextMapper;//追偿审核意见
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private ClaimWorkFlowService claimWorkFlowService;
    @Autowired
    private ClmsReplevyDetailMapper clmsReplevyDetailMapper;//追偿详情信息
    @Autowired
    private ClmsReplevyLossMapper clmsReplevyLossMapper;//追偿损失信息
    @Autowired
    private ClmsReplevyChargeMapper clmsReplevyChargeMapper;//追偿费用信息
    @Autowired
    private PaymentInfoMapper paymentInfoMapper;//领款人信息
    @Autowired
    private FeePayMapper feePayMapper;
    @Value("${ncbs.pay.passWord:testPassword}")
    private String passWord;
    @Value("${ncbs.pay.userCode:test}")
    private String userCode;
    @Value("${ncbs.pay.url:http://pointwise-fin-fin.lb.sssit.com:48913/fin/v1/interface/transData/transToPayment}")
    private String payUrl;
    @Autowired
    ClmsRelatedActualReceiptMapper clmsRelatedActualReceiptMapper;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private ClmsPaymentDutyMapper clmsPaymentDutyMapper;
    @Autowired
    private CaseBaseMapper caseBaseMapper;
    @Autowired
    private ClmsEstimateDutyRecordMapper clmsEstimateDutyRecordMapper;
    @Autowired
    private ReplevyChargeService replevyChargeService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private CoinsureService coinsureService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private DutyPayMapper dutyPayMapper;
    @Autowired
    private TaskListService taskListService;
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Autowired
    private FileCommonService fileCommonService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    CommonService commonService;
    /**
     * 功能描述：暂存，提交追偿主页面信息
     * 调用点：追偿主页面暂存，提交
     *
     * @param replevyApiVo
     * @return
     * <AUTHOR> 20170611
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> saveOrSubmitReplevy(ReplevyApiVo replevyApiVo) {
        log.info("追偿主页面保存提交开始：" + JsonUtils.toJsonString(replevyApiVo));
        String submitFlag = replevyApiVo.getSubmitFlag();//0代表暂存，1代表提交
        ClmsReplevyMainVo replevyMainVo = replevyApiVo.getReplevyMainVo();
        String reportNo = replevyApiVo.getReportNo();
        UserInfoDTO userInfoDTO = WebServletContext.getUser();//获取当前登录用户信息
        try {
            //追偿信息主表信息
            ClmsReplevyMain clmsReplevyMain = saveOrUpdateReplevyMian(replevyApiVo,userInfoDTO,"1");
            replevyApiVo.setClmsReplevyMain(clmsReplevyMain);
            if (!StringUtils.isEmptyStr(submitFlag) && "1".equals(submitFlag)) {//1代表提交
                //提交追偿任务校验
                this.checkReplevySubmit(replevyApiVo);
                //追偿审核信息生成
                this.saveOrExamineReplevyText(replevyApiVo, "1",ReplevyConstant.OPINION_TYPE_Z);
                //操作记录
                operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_REPLEVY_REVIEW, "发起", replevyMainVo.getReplevyText());
                //完成报案跟踪工作流 原先的事件改成直接调用创建任务和完成任务
                claimWorkFlowService.dealReplevyWorkFlowData(reportNo, 1, "Z",null,null,replevyMainVo.getSumRealReplevy());
                //构建支付项
                buildPaymemt(clmsReplevyMain);
                //追偿提交冻结费用
                this.sendPayThaw(reportNo,clmsReplevyMain.getReplevyTimes(),clmsReplevyMain.getReplevyNo(), ReplevyConstant.FREEZE_STATUS_F,ReplevyConstant.RECEIPT_TYPE_RELEVY);
            }
            return ResponseResult.success("追偿任务保存提交成功");
        }catch (GlobalBusinessException e) {
            throw new GlobalBusinessException(e.getCode(),e.getMessage());
        }catch (Exception e){
            log.error("追偿任务保存提交异常：",e);
            throw new GlobalBusinessException("追偿任务保存提交失败");
        }

    }

    /**
     * 追偿子页面保存提交
     *
     * @param replevyApiVo 追偿API对象
     * @return 追偿API对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> saveReplevyDetail(ReplevyApiVo replevyApiVo) {
        log.info("追偿子页面保存提交开始："+ JsonUtils.toJsonString(replevyApiVo));
        ReplevyApiVo responseResultVo = new ReplevyApiVo();
        UserInfoDTO userInfoDTO = WebServletContext.getUser();//获取当前登录用户信息
        String reportNo = replevyApiVo.getReportNo();
        try {
            //提交校验方法
            this.replevyCheckSubmit(replevyApiVo,"2");
            //追偿信息主表信息
            ClmsReplevyMain clmsReplevyMainPo = saveOrUpdateReplevyMian(replevyApiVo,userInfoDTO,"2");
            responseResultVo.setReplevyNo(clmsReplevyMainPo.getReplevyNo());
            replevyApiVo.setClmsReplevyMain(clmsReplevyMainPo);
            //追偿明细表信息
            ClmsReplevyDetail clmsReplevyDetailPo = saveOrUpdateReplevyDetail(replevyApiVo,userInfoDTO);
            responseResultVo.setReplevyDetailId(clmsReplevyDetailPo.getId());
            replevyApiVo.setReplevyDetailId(clmsReplevyDetailPo.getId());
            //追偿损失表信息
            saveOrUpdateReplevyLoss(replevyApiVo,userInfoDTO);
            //关联实收表信息
            saveOrUpdateReplevyActualReceipt(replevyApiVo.getRelatedActualReceiptVoList(),reportNo,clmsReplevyMainPo.getReplevyTimes(),clmsReplevyDetailPo.getId(),"1") ;
            //计算更新总追偿收入金额、追偿费用
            updateTotalRepleviedMoney(replevyApiVo,"1");

            responseResultVo.setReportNo(reportNo);
            responseResultVo.setInitFlag("3");
            return ResponseResult.success(responseResultVo);
        }catch (GlobalBusinessException e) {
            if("941000".equals(e.getCode())){
                return ResponseResult.success(e.getMessage());
            }else{
                throw new GlobalBusinessException(e.getCode(),e.getMessage());
            }

        }catch (Exception e) {
            log.error("追偿子页面保存提交失败：",e);
            throw new GlobalBusinessException("追偿子页面保存提交失败");
        }
    }

    /**
     * 直接理赔费用提交
     *
     * @param replevyApiVo
     * @return
     */
    @Override
    public ResponseResult<Object> saveOrSubmitReplevyFee(ReplevyApiVo replevyApiVo) {
        log.info("直接理赔费用提交开始：" + JsonUtils.toJsonString(replevyApiVo));
        ReplevyApiVo responseResultVo = new ReplevyApiVo();
        String submitFlag = replevyApiVo.getSubmitFlag();//0代表暂存，1代表提交
        try {
            //提交校验方法
            this.replevyCheckSubmit(replevyApiVo,"3");
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            String reportNo = replevyApiVo.getReportNo();//报案号
            //追偿信息主表信息
            ClmsReplevyMain clmsReplevyMainPo = saveOrUpdateReplevyMian(replevyApiVo,userInfoDTO,"3");
            replevyApiVo.setReplevyNo(clmsReplevyMainPo.getReplevyNo());
            replevyApiVo.setClmsReplevyMain(clmsReplevyMainPo);
            //领款人信息
            String idClmPaytinfo = null;
            if(replevyApiVo.getPaymentInfoVo() != null){
                idClmPaytinfo = replevyApiVo.getPaymentInfoVo().getIdClmPaymentInfo();
            }
            //查询报案号下费用赔付次数
            Integer serialNo = clmsReplevyChargeMapper.getMaxSerialNoByReportNo(reportNo);
            if(serialNo== null){
                serialNo=0;
            }
            //保存追偿费用明细表
            ClmsReplevyCharge clmsReplevyCharge = saveOrUpdateReplevyCharge(replevyApiVo,userInfoDTO,idClmPaytinfo,serialNo);
            responseResultVo.setReplevyChargeId(clmsReplevyCharge.getId());
            replevyApiVo.setReplevyChargeId(clmsReplevyCharge.getId());
            //发票信息
            saveOrUpdateInvoiceInfo(replevyApiVo,userInfoDTO);
            //计算更新总追偿收入金额、追偿费用
            updateTotalRepleviedMoney(replevyApiVo,"2");

            if (!StringUtils.isEmptyStr(submitFlag) && "1".equals(submitFlag)) {//1代表提交
                this.saveOrExamineReplevyText(replevyApiVo, "1",ReplevyConstant.OPINION_TYPE_F);
                //操作记录
                operationRecordService.insertOperationRecordByLabour(replevyApiVo.getReportNo(), BpmConstants.OC_REPLEVY_FEE_REVIEW, "发起", clmsReplevyCharge.getApplyReason());
                //完成报案跟踪工作流 原先的事件改成直接调用创建任务和完成任务
                claimWorkFlowService.dealReplevyWorkFlowData(replevyApiVo.getReportNo(), 1, ReplevyConstant.FREEZE_STATUS_F,null, clmsReplevyCharge.getId(),clmsReplevyCharge.getChargeMoney());
                //构建支付项
                PaymentItemDTO p = replevyChargeService.buildFeePaymemtItem(clmsReplevyCharge, clmsReplevyMainPo);
                //费用表关联支付项
                clmsReplevyChargeMapper.updateIdPaymentItemByPrimaryKey(clmsReplevyCharge.getId(),p.getIdClmPaymentItem());
                //存支付表
                paymentItemMapper.addPaymentItem(p);
                //存支付项对应的条款责任
                verifyService.splitPlanAndDutyFee(p,clmsReplevyCharge.getDutyCode(),clmsReplevyCharge.getPlanCode());
            }
            responseResultVo.setReplevyNo(clmsReplevyMainPo.getReplevyNo());
            responseResultVo.setReportNo(replevyApiVo.getReportNo());
            responseResultVo.setInitFlag("2");
            return ResponseResult.success(responseResultVo);
        }catch (GlobalBusinessException e) {
            throw new GlobalBusinessException(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("追偿费用保存提交失败：" , e);
            throw new GlobalBusinessException("追偿费用保存提交失败");
        }
    }

    /**
     * 关联实收调用收付流水查询
     *
     * @param sendCashFlowSearchVo
     * @return
     */
    @Override
    public ResponseResult<Object> sendCashFlowSearch(SendCashFlowSearchVo sendCashFlowSearchVo) {
        PayInfoNotice<PayThawBody> payInfoNotice = new PayInfoNotice<PayThawBody>();
        ResponseResult<Object> responseResult = new ResponseResult<Object>();
        List<ClmsRelatedActualReceiptVo> body = new ArrayList<ClmsRelatedActualReceiptVo>();
        List<ClmsRelatedActualReceiptVo> relatedActualReceiptVoList = new ArrayList<ClmsRelatedActualReceiptVo>();
        try {
            PayInfoHead payInfoHead = new PayInfoHead();
            payInfoHead.setPassWord(passWord);//密码
            payInfoHead.setRequestType(PayInfoHead.TYPE_C01);//请求类型
            payInfoHead.setUserCode(userCode);//用户
            payInfoNotice.setHead(payInfoHead);//请求头
            PayThawBody payThawBody = new PayThawBody();
            CashFlowSearchVo cashFlowSearchVo = new CashFlowSearchVo();
            BeanUtils.copyProperties(sendCashFlowSearchVo, cashFlowSearchVo);
            payThawBody.setCashflowSearch(cashFlowSearchVo);
            payInfoNotice.setBody(payThawBody);
            log.info("关联实收调用收付流水查询接口开始：", JsonUtils.toJsonString(payInfoNotice));
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            String result = MeshSendUtils.post(payUrl + "?", JSON.toJSONString(payInfoNotice), headers);

            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("关联实收调用收付流水查询返回报文:" + jsonObject);
                PayResult payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                if (payResult.isSuccess(payResult.getResponseCode())) {
                    body = jsonObject.getJSONObject("body").getJSONArray("result").toJavaList(ClmsRelatedActualReceiptVo.class);
                    if (body != null && !body.isEmpty()) {
                        Iterator<ClmsRelatedActualReceiptVo> iterator = body.iterator();
                        while (iterator.hasNext()) {
                            ClmsRelatedActualReceiptVo vo = iterator.next();
                            if(sendCashFlowSearchVo.getPostScript()!=null&& !sendCashFlowSearchVo.getPostScript().equals(vo.getPostScript())){
                                iterator.remove();
                                continue;
                            }
                            if(new BigDecimal(BigInteger.ZERO).compareTo(vo.getWriteOffRemainAmount()) >= 0){
                                iterator.remove();
                                continue;
                            }
                            if(sendCashFlowSearchVo.getTransAmount() != null){
                                if(sendCashFlowSearchVo.getTransAmount().compareTo(vo.getTransAmount()) != 0){
                                    iterator.remove();
                                    continue;
                                }
                            }
                            if ("collection".equals(vo.getDirectionType())) {
                                vo.setDirectionType("0");
                            } else if ("payment".equals(vo.getDirectionType())) {
                                vo.setDirectionType("1");
                            }
                        }
                    }
                } else {
                    throw new GlobalBusinessException(payResult.getErrorMessage());
                }
            }
        } catch (Exception e) {
            throw new GlobalBusinessException("关联实收调用收付流水查询失败：" + e);
        }
        responseResult.setCode("000000");
        responseResult.setMsg("调用收付流水查询成功");
        responseResult.setData(body);//返回银行流水详情
        return responseResult;
    }

    /**
     * 追偿页面初始化
     *
     * @param replevyApiVo
     * @return
     */
    @Override
    public ResponseResult<Object> initReplevy(ReplevyApiVo replevyApiVo) {
        log.info("追偿页面初始化开始：{}", JsonUtils.toJsonString(replevyApiVo));
        ResponseResult<Object> responseResult = new ResponseResult<Object>();
        ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
        clmsReplevyMainVo.setReportNo(replevyApiVo.getReportNo());
        if (replevyApiVo.getReplevyTimes() != null) {
            clmsReplevyMainVo.setReplevyTimes(replevyApiVo.getReplevyTimes());
        } else {
            //初始化未完成的追偿
            clmsReplevyMainVo.setFlag("0");
        }
        clmsReplevyMainVo = clmsReplevyMainMapper.selectReplevyMain(replevyApiVo.getReportNo(),replevyApiVo.getReplevyNo(),null);
        String initFlag = replevyApiVo.getInitFlag();//追偿主页面，子页面，费用页面初始化标志
        //直接理赔费用信息集合
        List<ClmsReplevyChargeVo> replevyChargeVoList = new ArrayList<ClmsReplevyChargeVo>();
        //追偿明细信息
        List<ClmsReplevyDetailVo> replevyDetailVoList = new ArrayList<ClmsReplevyDetailVo>();
        //追偿审核信息
        List<ClmsReplevyTextVo> clmsReplevyTextVoList = new ArrayList<ClmsReplevyTextVo>();
        //追偿损失信息
        List<ClmsReplevyLossVo> replevyLossVoList = new ArrayList<ClmsReplevyLossVo>();
        List<ClmsRelatedActualReceiptVo> relatedActualReceiptVoList = new ArrayList<ClmsRelatedActualReceiptVo>();
        ClmsReplevyChargeVo clmsReplevyChargeVo = new ClmsReplevyChargeVo();//费用对象
        ClmsReplevyDetailVo clmsReplevyDetailVo = new ClmsReplevyDetailVo();//追偿详细对象
        PaymentInfoVO paymentInfoVO = new PaymentInfoVO();//领款人信息vo
        InvoiceInfoDTO invoiceInfoDTO = new InvoiceInfoDTO();//发票信息
        try {
            if (!StringUtils.isEmptyStr(initFlag) && "1".equals(initFlag)) {//1代表追偿主页面信息初始数据
                if (clmsReplevyMainVo != null) {
                    if("2".equals(clmsReplevyMainVo.getFlag()) && !"V".equals(replevyApiVo.getOperateFlag())){
                        return ResponseResult.success();// 追偿已完成,生成新追偿
                    }
                    replevyApiVo.setReplevyId(clmsReplevyMainVo.getId());
                    clmsReplevyChargeVo.setReplevyNo(clmsReplevyMainVo.getReplevyNo());
                    //直接理赔费用信息
                    replevyChargeVoList = clmsReplevyChargeMapper.selectReplevyChargeHistory(null,clmsReplevyMainVo.getReplevyNo());
                    //追偿列表信息
                    replevyDetailVoList = clmsReplevyDetailMapper.selectClmsReplevyDetail(null,clmsReplevyMainVo.getReplevyNo());
                    //查询历史审核信息
                    List<ClmsReplevyText> clmsReplevyTextList = clmsReplevyTextMapper.getReplevyTextList(clmsReplevyMainVo.getReplevyNo(),null,"1");
                    for (ClmsReplevyText clmsReplevyText : clmsReplevyTextList) {
                        ClmsReplevyTextVo clmsReplevyTextVo = new ClmsReplevyTextVo();
                        BeanUtils.copyProperties(clmsReplevyText, clmsReplevyTextVo);
                        clmsReplevyTextVoList.add(clmsReplevyTextVo);
                    }
                }
                replevyApiVo.setReplevyChargeVoList(replevyChargeVoList);//直接理赔费用
                replevyApiVo.setReplevyDetailVoList(replevyDetailVoList);//追偿明细列表
                replevyApiVo.setReplevyTextVoList(clmsReplevyTextVoList);//历史追偿审批记录
            } else if (!StringUtils.isEmptyStr(initFlag) && "2".equals(initFlag)) {//2代表追偿费用信息页面信息初始数据
                if (replevyApiVo.getReplevyChargeId() != null) {
                    ClmsReplevyCharge clmsReplevyCharge = clmsReplevyChargeMapper.selectById(replevyApiVo.getReplevyChargeId());
                    if (clmsReplevyCharge != null) {
                        //费用信息
                        BeanUtils.copyProperties(clmsReplevyCharge, clmsReplevyChargeVo);
                        //根据主键查询领款人信息
                        PaymentInfoDTO paymentInfoDTO = paymentInfoMapper.getPaymentInfoById(clmsReplevyCharge.getPaymentInfoId());
                        if(paymentInfoDTO != null){
                            BeanUtils.copyProperties(paymentInfoDTO, paymentInfoVO);
                        }
                        //根据主键查询发票信息数据
                        invoiceInfoDTO = feePayMapper.getClmsInvoiceInfoById(clmsReplevyCharge.getId());
                        if (invoiceInfoDTO != null) {
                            this.getFileUrlOrFormat(invoiceInfoDTO,replevyApiVo.getReportNo());
                        }
                        //查询历史审核费用信息
                        List<ClmsReplevyText> clmsReplevyTextList = clmsReplevyTextMapper.getReplevyTextList(null,replevyApiVo.getReplevyChargeId(),"2");
                        for (ClmsReplevyText clmsReplevyText : clmsReplevyTextList) {
                            ClmsReplevyTextVo clmsReplevyTextVo = new ClmsReplevyTextVo();
                            BeanUtils.copyProperties(clmsReplevyText, clmsReplevyTextVo);
                            clmsReplevyTextVoList.add(clmsReplevyTextVo);
                        }
                    }
                }
                replevyApiVo.setReplevyChargeVo(clmsReplevyChargeVo);//直接理赔费用信息
                replevyApiVo.setPaymentInfoVo(paymentInfoVO);//领款人信息vo
                replevyApiVo.setInvoiceInfoDTO(invoiceInfoDTO);//发票信息
                replevyApiVo.setReplevyTextVoList(clmsReplevyTextVoList);//历史追偿审批记录
            } else if (!StringUtils.isEmptyStr(initFlag) && "3".equals(initFlag)) {//追偿子页面初始化数据
                //被追偿方信息，追偿信息
                ClmsReplevyDetail clmsReplevyDetail = clmsReplevyDetailMapper.selectById(replevyApiVo.getReplevyDetailId());
                if (clmsReplevyDetail != null) {
                    BeanUtils.copyProperties(clmsReplevyDetail, clmsReplevyDetailVo);//把实体里面的数据，copy到vo里面
                    replevyApiVo.setReplevyDetailVo(clmsReplevyDetailVo);//追偿明细返回到前端

                    //查询追偿损失信息
                    List<ClmsReplevyLoss> clmsReplevyLossList = clmsReplevyLossMapper.selectByReplevyDetailId(replevyApiVo.getReplevyDetailId());
                    if (!CollectionUtils.isEmpty(clmsReplevyLossList)) {
                        for (ClmsReplevyLoss replevyLoss : clmsReplevyLossList) {
                            ClmsReplevyLossVo replevyLossVo = new ClmsReplevyLossVo();
                            BeanUtils.copyProperties(replevyLoss, replevyLossVo);//把实体里面的数据，copy到vo里面
                            replevyLossVoList.add(replevyLossVo);
                        }
                    }
                    replevyApiVo.setReplevyLossVoList(replevyLossVoList);//把损失信息数据返回到前端
                    //关联实收信息
                    ClmsRelatedActualReceipt clmsRelatedActualReceipt = new ClmsRelatedActualReceipt();
                    clmsRelatedActualReceipt.setBusinessId(replevyApiVo.getReplevyDetailId());
                    List<ClmsRelatedActualReceipt> relatedActualReceiptList = clmsRelatedActualReceiptMapper.getRelatedActualReceiptByEntity(clmsRelatedActualReceipt);
                    if (!CollectionUtils.isEmpty(relatedActualReceiptList)) {
                        for (ClmsRelatedActualReceipt relatedActualReceipt : relatedActualReceiptList) {
                            ClmsRelatedActualReceiptVo relatedActualReceiptVo = new ClmsRelatedActualReceiptVo();
                            BeanUtils.copyProperties(relatedActualReceipt, relatedActualReceiptVo);//把实体里面的数据，copy到vo里面
                            relatedActualReceiptVoList.add(relatedActualReceiptVo);
                        }
                    }
                    replevyApiVo.setRelatedActualReceiptVoList(relatedActualReceiptVoList);
                }
            }
            replevyApiVo.setReplevyMainVo(clmsReplevyMainVo);//追偿主信息
            responseResult.setCode("000000");
            responseResult.setMsg("追偿页面信息初始化成功");
            responseResult.setData(replevyApiVo);//返回追偿案件号
        } catch (Exception e) {
            log.error("追偿页面信息初始化失败", e);
            throw new GlobalBusinessException("追偿页面信息初始化失败");
        }
        return responseResult;
    }

    /**
     * 追偿审核页面初始化--追偿页面
     *
     * @param replevyApiVo
     * @return
     */
    @Override
    public ResponseResult<Object> initReplevyApprove(ReplevyApiVo replevyApiVo) {
        ResponseResult<Object> responseResult = new ResponseResult<Object>();
        //查询追偿主表信息数据
        ClmsReplevyMainVo clmsReplevyMainVo = clmsReplevyMainMapper.selectReplevyMain(replevyApiVo.getReportNo(),replevyApiVo.getReplevyNo(),replevyApiVo.getReplevyId());
        String reportNo = clmsReplevyMainVo.getReportNo();
        String replevyNo = clmsReplevyMainVo.getReplevyNo();
        List<ClmsReplevyTextVo> clmsReplevyTextVoList = new ArrayList<ClmsReplevyTextVo>();//审批记录
        String initFlag = replevyApiVo.getInitFlag();//F代表费用审批，Z代表追偿审批
        try {
            if (clmsReplevyMainVo != null) {
                replevyApiVo.setReplevyId(clmsReplevyMainVo.getId());//主表id
                //追偿明细列表--报案号历史追偿列表
//                List<ClmsReplevyDetailVo> HistoryReplevyDetailVoList = clmsReplevyDetailMapper.selectClmsReplevyDetail(replevyApiVo.getReportNo(),null);
//                replevyApiVo.setHistoryReplevyDetailVoList(HistoryReplevyDetailVoList);
                List<ClmsReplevyMainVo> HistoryReplevyMainVoList = clmsReplevyMainMapper.selectClmsReplevyMainList(reportNo,null);
                replevyApiVo.setHistoryReplevyMainVoList(HistoryReplevyMainVoList);
                //直接理赔费用信息--报案号历史费用列表
                List<ClmsReplevyChargeVo> HistoryReplevyChargeVoList =  clmsReplevyChargeMapper.selectReplevyChargeHistory(reportNo,null);
                replevyApiVo.setHistoryReplevyChargeVoList(HistoryReplevyChargeVoList);
                if (!StringUtils.isEmptyStr(initFlag) && "Z".equals(initFlag)) {//F代表费用审批，Z代表追偿审批
                    //直接理赔费用信息--本次费用列表
//                    List<ClmsReplevyChargeVo> replevyChargeVoList = clmsReplevyChargeMapper.selectClmsReplevyCharge(replevyApiVo.getReportNo(),replevyApiVo.getReplevyNo());
//                    replevyApiVo.setReplevyChargeVoList(replevyChargeVoList);
                    //待审批追偿 - 追偿明细列表
                    List<ClmsReplevyDetailVo> replevyDetailVoList = clmsReplevyDetailMapper.selectClmsReplevyDetail(reportNo,replevyNo);
                    replevyApiVo.setReplevyDetailVoList(replevyDetailVoList);
                    List<ClmsReplevyText> clmsReplevyTextList = clmsReplevyTextMapper.getReplevyTextList(replevyNo,null,ReplevyConstant.OPINION_TYPE_Z);
                    for (ClmsReplevyText clmsReplevyText : clmsReplevyTextList) {
                        ClmsReplevyTextVo clmsReplevyTextVo = new ClmsReplevyTextVo();
                        BeanUtils.copyProperties(clmsReplevyText, clmsReplevyTextVo);
                        clmsReplevyTextVoList.add(clmsReplevyTextVo);
                    }
                    replevyApiVo.setReplevyTextVoList(clmsReplevyTextVoList);//历史追偿审批记录
                } else if (!StringUtils.isEmptyStr(initFlag) && ReplevyConstant.FREEZE_STATUS_F.equals(initFlag)) {//F代表费用审批，Z代表追偿审批
                    //本次审核的费用信息模块
                    ClmsReplevyCharge clmsReplevyCharge = clmsReplevyChargeMapper.selectById(replevyApiVo.getReplevyChargeId());
                    ClmsReplevyChargeVo clmsReplevyChargeVo = new ClmsReplevyChargeVo();
                    BeanUtils.copyProperties(clmsReplevyCharge, clmsReplevyChargeVo);
                    replevyApiVo.setReplevyChargeVo(clmsReplevyChargeVo);
                    if(clmsReplevyCharge != null){
                        //本次领款人信息
                        if (StringUtils.isNotEmpty(clmsReplevyCharge.getPaymentInfoId())) {
                            PaymentInfoDTO paymentInfoDTO = paymentInfoMapper.getPaymentInfoById(clmsReplevyCharge.getPaymentInfoId());
                            PaymentInfoVO paymentInfoVO = new PaymentInfoVO();
                            BeanUtils.copyProperties(paymentInfoDTO, paymentInfoVO);
                            replevyApiVo.setPaymentInfoVo(paymentInfoVO);
                        }
                        //本次的相关发票信息
                        if (StringUtils.isNotEmpty(clmsReplevyCharge.getId())) {
                            InvoiceInfoDTO invoiceInfoDTO = feePayMapper.getClmsInvoiceInfoById(clmsReplevyCharge.getId());
                            this.getFileUrlOrFormat(invoiceInfoDTO,replevyApiVo.getReportNo());
                            replevyApiVo.setInvoiceInfoDTO(invoiceInfoDTO);
                        }
                    }
                    //根据主键查询当前案子的费用审批记录
                    List<ClmsReplevyText> clmsReplevyTextList = clmsReplevyTextMapper.getReplevyTextList(null,replevyApiVo.getReplevyChargeId(),ReplevyConstant.OPINION_TYPE_F);
                    for (ClmsReplevyText clmsReplevyText : clmsReplevyTextList) {
                        ClmsReplevyTextVo clmsReplevyTextVo = new ClmsReplevyTextVo();
                        BeanUtils.copyProperties(clmsReplevyText, clmsReplevyTextVo);
                        clmsReplevyTextVoList.add(clmsReplevyTextVo);
                    }
                    replevyApiVo.setReplevyTextVoList(clmsReplevyTextVoList);//历史追偿审批记录
                }
            }
            replevyApiVo.setReplevyMainVo(clmsReplevyMainVo);//追偿主信息
            responseResult.setCode("000000");
            responseResult.setMsg("追偿审批/费用审批页面信息初始化成功");
            responseResult.setData(replevyApiVo);//返回追偿案件号
        } catch (Exception e) {
            log.error("追偿审批/费用审批页面信息初始化失败", e);
            throw new GlobalBusinessException("追偿审批/费用审批页面信息初始化失败");
        }
        return responseResult;
    }

    private void getFileUrlOrFormat(InvoiceInfoDTO invoiceInfoDTO,String reportNo) {
        try{
            //查询影像表获取文件url和format值
            FileInfoDTO fileInfoDTO = new FileInfoDTO();
            fileInfoDTO.setFileId(invoiceInfoDTO.getFileId());
            fileInfoDTO.setReportNo(reportNo);
            List<FileDocumentDTO> fileDtoList = fileInfoMapper.getDocumentName(fileInfoDTO);
            if(ObjectUtil.isNotEmpty(fileDtoList)){
                UserInfoDTO userDTO = WebServletContext.getUser();
                String url = fileCommonService.getPreviewUrl(invoiceInfoDTO.getFileId(),userDTO.getUserName());
                invoiceInfoDTO.setDocumentFormat(fileDtoList.get(0).getDocumentFormat());
                invoiceInfoDTO.setFileUrl(url);
            }
        } catch (Exception e) {
            throw new RuntimeException("影像url获取失败："+e.getMessage());
        }

    }

    /**
     * 查看按钮页面初始化
     *
     * @param replevyApiVo
     * @return
     */
    @Override
    public ResponseResult<Object> initReplevyView(ReplevyApiVo replevyApiVo) {
        ResponseResult<Object> responseResult = new ResponseResult<Object>();
        //主信息
        try {
            ClmsReplevyMain clmsReplevyMain = clmsReplevyMainMapper.selectByPrimaryKey(replevyApiVo.getReplevyId());
            if (replevyApiVo.getReplevyChargeId() != null) {//判断费用id不能为空
                //费用信息
                ClmsReplevyCharge replevyCharge = clmsReplevyChargeMapper.selectByPrimaryKey(replevyApiVo.getReplevyChargeId());
                List<PaymentInfoVO> paymentInfoVOList = new ArrayList<PaymentInfoVO>();//领款人
                List<InvoiceInfoDTO> invoiceInfoDTOList = new ArrayList<InvoiceInfoDTO>();//发票信息
                List<ClmsReplevyTextVo> clmsReplevyTextVoList = new ArrayList<ClmsReplevyTextVo>();//审批记录
                if (replevyCharge != null) {
                    //本次领款人信息
                    PaymentInfoDTO paymentInfoDTO = paymentInfoMapper.getPaymentInfoById(replevyCharge.getPaymentInfoId());
                    PaymentInfoVO paymentInfoVO = new PaymentInfoVO();
                    BeanUtils.copyProperties(paymentInfoDTO, paymentInfoVO);//把实体里面的数据，copy到vo里面
                    paymentInfoVOList.add(paymentInfoVO);
                    //replevyApiVo.setPaymentInfoVOList(paymentInfoVOList);//本次领款人信息返回给前端
                    //本次的相关发票信息
                    InvoiceInfoDTO invoiceInfoDTO = feePayMapper.getClmsInvoiceInfoById(replevyCharge.getId());
                    this.getFileUrlOrFormat(invoiceInfoDTO,replevyApiVo.getReportNo());
                    invoiceInfoDTOList.add(invoiceInfoDTO);
                    //replevyApiVo.setInvoiceInfoDTOList(invoiceInfoDTOList);//发票信息返回到前端
                    //根据主键查询当前案子的费用审批记录
                    ClmsReplevyTextVo replevyTextVo = new ClmsReplevyTextVo();
                    replevyTextVo.setReplevyChargeId(replevyCharge.getId());
                    clmsReplevyTextVoList = clmsReplevyTextMapper.selectClmsReplevyTextVo(replevyTextVo);
                    replevyApiVo.setReplevyTextVoList(clmsReplevyTextVoList);//历史追偿审批记录

                }
            } else if (replevyApiVo.getReplevyDetailId() != null) {//追偿明细信息,判断明细id不能为空
                ClmsReplevyDetail clmsReplevyDetail = clmsReplevyDetailMapper.selectByPrimaryKey(replevyApiVo.getReplevyDetailId());
                if (clmsReplevyDetail != null) {//追偿明细
                    ClmsReplevyDetailVo clmsReplevyDetailVo = new ClmsReplevyDetailVo();
                    BeanUtils.copyProperties(clmsReplevyDetail, clmsReplevyDetailVo);
                    replevyApiVo.setReplevyDetailVo(clmsReplevyDetailVo);
                }

            }
            //追偿主信息
            if (clmsReplevyMain != null) {
                ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
                BeanUtils.copyProperties(clmsReplevyMain, clmsReplevyMainVo);
                replevyApiVo.setReplevyMainVo(clmsReplevyMainVo);
            }
            responseResult.setCode("000000");
            responseResult.setMsg("查看按钮页面初始化成功");
            responseResult.setData(replevyApiVo);//返回追偿案件号
        } catch (Exception e) {
            log.error("查看按钮页面初始化失败", e);
            throw new GlobalBusinessException("查看按钮页面初始化失败");
        }

        return responseResult;
    }
    public void sendPayThaw(String reportNo,Integer subTimes, String originBusinessNo , String freezeFlag,String receiptType){

        //接口调用成功标志
        String failFlag = "N";
        //查询关联实收信息
        ClmsRelatedActualReceipt relatedActualReceipt = new ClmsRelatedActualReceipt();
        relatedActualReceipt.setSubTimes(subTimes);
        relatedActualReceipt.setReportNo(reportNo);
        relatedActualReceipt.setReceiptType(receiptType);
        List<ClmsRelatedActualReceipt> clmsRelatedActualReceiptList =
                clmsRelatedActualReceiptMapper.getListGroupByBankTransFlowNo(relatedActualReceipt);
        //调用接口成功数据list
        List<ClmsRelatedActualReceipt> returnList = new ArrayList<ClmsRelatedActualReceipt>();
        String errorMessage =null;
        if (!CollectionUtils.isEmpty(clmsRelatedActualReceiptList)) {
            for (ClmsRelatedActualReceipt clmsRelatedActualReceipt1 : clmsRelatedActualReceiptList) {
                if (clmsRelatedActualReceipt1.getWriteOffAmount() == null || BigDecimal.ZERO.compareTo(clmsRelatedActualReceipt1.getWriteOffAmount()) == 0) {
                    continue;
                }
                PayResult payResult = payInfoNoticeThirdPartyCoreSAO.sendPayThaw(reportNo,subTimes,clmsRelatedActualReceipt1.getBankTransFlowNo(),
                        clmsRelatedActualReceipt1.getWriteOffAmount(),originBusinessNo,freezeFlag);
                if (payResult.isSuccess(payResult.getResponseCode())) {
                   /* clmsRelatedActualReceipt1.setFreezeFlag(freezeFlag);
                    clmsRelatedActualReceipt1.setUpdatedBy(WebServletContext.getUserId());
                    clmsRelatedActualReceipt1.setSysUtime(new Date());
                    clmsRelatedActualReceiptMapper.updateSelectiveByPrimaryKey(clmsRelatedActualReceipt1);*/
                    returnList.add(clmsRelatedActualReceipt1);
                }else {
                    failFlag = "Y";
                    errorMessage=payResult.getErrorMessage();
                    break;
                }
                log.info("调用收付冻结/解冻接口结束:" + payResult);
            }
        }
        if("Y".equals(failFlag) && !CollectionUtils.isEmpty(returnList)){
            freezeFlag = ReplevyConstant.FREEZE_STATUS_R.equals(freezeFlag)?ReplevyConstant.FREEZE_STATUS_F:ReplevyConstant.FREEZE_STATUS_R;
            for (ClmsRelatedActualReceipt clmsRelatedActualReceipt1 : returnList) {
                    payInfoNoticeThirdPartyCoreSAO.sendPayThaw(reportNo,subTimes,clmsRelatedActualReceipt1.getBankTransFlowNo(),
                            clmsRelatedActualReceipt1.getWriteOffAmount(),originBusinessNo, freezeFlag);
            }
            throw new GlobalBusinessException(errorMessage);
        }else if("Y".equals(failFlag)){
            throw new GlobalBusinessException(errorMessage);
        }
        else{
            //根据报案号、次数、类型更新冻结状态
            clmsRelatedActualReceiptMapper.updateFreezeFlagByReportNo(reportNo,subTimes,receiptType,WebServletContext.getUserId(),freezeFlag);
        }
    }


    /**
     * 统一删除追偿相关数据
     * 根据initFlag区分删除类型：1-删除费用信息，2-删除追偿明细信息，3-删除关联实收数据
     * @param replevyApiVo 删除请求参数
     * @return
     */
    @Override
    public ResponseResult<Object> deleteReplevyData(ReplevyApiVo replevyApiVo) {
        String initFlag = replevyApiVo.getInitFlag();
        try {
            if ("1".equals(initFlag)) {
                // 删除费用信息
                return deleteReplevyFeeInternal(replevyApiVo);
            } else if ("2".equals(initFlag)) {
                // 删除追偿明细信息
                return deleteReplevyDetailInternal(replevyApiVo);
            } else if ("3".equals(initFlag)) {
                // 删除关联实收数据
                return deleteRelatedActualReceiptInternal(replevyApiVo);
            } else {
                throw new GlobalBusinessException("不支持的删除类型: " + initFlag);
            }
        }catch (Exception e) {
            throw new GlobalBusinessException("删除追偿相关数据失败");
        }
    }

    /**
     * 生成追偿案件号
     *
     * @param reportNo
     * @return
     */
    public String getReplevyNo(String reportNo) {
        String replevyNo = "";
        //查询序号
        ClmsReplevyMain clmsReplevyMain = clmsReplevyMainMapper.selectSerialNo(reportNo);
        //生成单号规则
        replevyNo = "ZC" + reportNo;
        int serialNo = 1;
        if (clmsReplevyMain != null && clmsReplevyMain.getSerialNo() != null) {
            serialNo = clmsReplevyMain.getSerialNo() + 1;
        }
        if (serialNo < 10) {
            replevyNo = replevyNo + "0" + serialNo;
        } else {
            replevyNo = replevyNo + serialNo;
        }
        return replevyNo;
    }

    /**
     * 追偿主表入库
     * @param replevyApiVo
     * @param userInfoDTO
     * @param flag 1-主页面 2-子页面 3-费用页
     * @returntas
     */
    @Transactional(rollbackFor = Exception.class)
    public ClmsReplevyMain saveOrUpdateReplevyMian(ReplevyApiVo replevyApiVo,UserInfoDTO userInfoDTO,String flag) {
        ClmsReplevyMainVo replevyMainVo = replevyApiVo.getReplevyMainVo();
        ClmsReplevyMain clmsReplevyMainPo = new ClmsReplevyMain();
        String reportNo = replevyApiVo.getReportNo();
        ClmsReplevyMain oldReplevyMain = clmsReplevyMainMapper.selectSerialNo(reportNo);
        try{
            if (replevyMainVo != null && replevyMainVo.getId() != null) {
                BeanUtils.copyProperties(replevyMainVo, clmsReplevyMainPo);
                if("1".equals(replevyApiVo.getSubmitFlag())){
                    clmsReplevyMainPo.setFlag(replevyApiVo.getSubmitFlag());//0-追偿处理中 1-追偿待审核 2-追偿已完成
                }
                clmsReplevyMainPo.setUpdatedBy(userInfoDTO.getUserCode());//修改人
                clmsReplevyMainPo.setSysCtime(new Date());
                clmsReplevyMainMapper.updateByPrimaryKeySelective(clmsReplevyMainPo);
                replevyApiVo.setReplevyId(clmsReplevyMainPo.getId());
            } else {
                // 幂等 报案号有正在处理中的追偿案件,不新增 2-追偿已完成
                if (oldReplevyMain != null ) {
                    if(!"2".equals(oldReplevyMain.getFlag())){
                        replevyApiVo.setReplevyId(oldReplevyMain.getId());
                        return oldReplevyMain;
                    }
                }
                if(replevyMainVo != null && replevyMainVo.getReplevyNo() != null){
                    clmsReplevyMainPo.setReplevyNo(replevyMainVo.getReplevyNo());//追偿案件号
                }else {
                    clmsReplevyMainPo.setReplevyNo(getReplevyNo(reportNo));//新追偿案件号
                }
                //赔案号
                List<CaseBaseEntity> caseBaseList = caseBaseMapper.getCaseBaseInfoByReportNo(replevyApiVo.getReportNo());
                if(!caseBaseList.isEmpty()){
                    clmsReplevyMainPo.setCaseNo(caseBaseList.get(0).getCaseNo());
                    clmsReplevyMainPo.setPolicyNo(caseBaseList.get(0).getPolicyNo());
                }
                //查询立案号
                WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, 1);
                if(wholeCaseBaseDTO!=null){
                    clmsReplevyMainPo.setClaimNo(wholeCaseBaseDTO.getRegistNo());//立案号
                }
                clmsReplevyMainPo.setId(UuidUtil.getUUID());//id
                clmsReplevyMainPo.setReportNo(reportNo);//报案号
                clmsReplevyMainPo.setCaseTimes(1);// 赔付次数 默认为1
                clmsReplevyMainPo.setSysCtime(new Date());//创建时间
                clmsReplevyMainPo.setCreatedBy(userInfoDTO.getUserCode());//创建人
                clmsReplevyMainPo.setHandlerCode(userInfoDTO.getUserCode());//处理人
                clmsReplevyMainPo.setMakeCom(userInfoDTO.getComCode());//操作机构
                clmsReplevyMainPo.setReplevyCurrency(Constants.CURRENCY_CNY);//币别
                clmsReplevyMainPo.setFlag("0");//0-追偿处理中 1-追偿待审核 2-追偿已完成
                clmsReplevyMainPo.setApproveFlag("0");
                clmsReplevyMainPo.setValidFlag("Y");//有效标志，Y有效,N无效
                if (oldReplevyMain == null || oldReplevyMain.getReplevyTimes() == null) {
                    clmsReplevyMainPo.setSerialNo(1);
                    clmsReplevyMainPo.setReplevyTimes(1);
                } else {
                    clmsReplevyMainPo.setSerialNo(oldReplevyMain.getSerialNo() + 1);//序号
                    clmsReplevyMainPo.setReplevyTimes(oldReplevyMain.getReplevyTimes() + 1);//追偿次数
                }
                clmsReplevyMainMapper.insertSelective(clmsReplevyMainPo);
                replevyApiVo.setReplevyId(clmsReplevyMainPo.getId());
                claimWorkFlowService.dealReplevyWorkFlowData(reportNo,1, "S",null, null, null);
            }
        }catch (Exception e) {
            throw new GlobalBusinessException("追偿主表入库失败");
        }
        return clmsReplevyMainPo;
    }

    /**
     * 追偿明细表入库
     * @param replevyApiVo
     * @param userInfoDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ClmsReplevyDetail saveOrUpdateReplevyDetail(ReplevyApiVo replevyApiVo,UserInfoDTO userInfoDTO) {
        ClmsReplevyMain clmsReplevyMainPo = replevyApiVo.getClmsReplevyMain();
        List<ClmsReplevyLossVo> replevyLossVoList = replevyApiVo.getReplevyLossVoList();
        String reportNo = replevyApiVo.getReportNo();
        ClmsReplevyDetailVo replevyDetailVo = replevyApiVo.getReplevyDetailVo();
        ClmsReplevyDetail replevyDetailPo = new ClmsReplevyDetail();
        try {
            if(replevyDetailVo != null){
                BeanUtils.copyProperties(replevyDetailVo, replevyDetailPo);
                // 追偿总金额
                BigDecimal sumAmount = BigDecimal.ZERO;
                if (replevyLossVoList != null && replevyLossVoList.size() > 0) {
                    for (ClmsReplevyLossVo lossList : replevyLossVoList) {
                        BigDecimal money = lossList.getRepleviedMoney() == null ? BigDecimal.ZERO : lossList.getRepleviedMoney();
                        sumAmount = sumAmount.add(money);//本次累计实际追回金额
                    }
                }
                replevyDetailPo.setRealReplevy(sumAmount);
                if (replevyDetailVo.getId() != null) {
                    replevyDetailPo.setFlag(replevyApiVo.getSubmitFlag());//0代表数据不完整，1代表数据不完整或已提交
                    replevyDetailPo.setUpdatedBy(userInfoDTO.getUserCode());//修改人
                    replevyDetailPo.setSysUtime(new Date());//修改时间
                    replevyDetailPo.setCurrency(Constants.CURRENCY_CNY);
                    if(replevyDetailPo.getRepleviedName()!=null
                            &&replevyDetailPo.getRepleviedCertiType()!=null
                            &&replevyDetailPo.getRepleviedType()!=null
                            &&replevyDetailPo.getRepleviedCertiCode()!=null){
                        String bankAccountAttribute = "1".equals(replevyDetailPo.getRepleviedType()) ? "0" : "1";
                        String customerNo =  customerInfoService.getCustomerNo(replevyDetailPo.getRepleviedName(), bankAccountAttribute, replevyDetailPo.getRepleviedCertiType(), replevyDetailPo.getRepleviedCertiCode());
                        replevyDetailPo.setCustomerNo(customerNo);
                    }
                    clmsReplevyDetailMapper.updateClmsReplevyDetailById(replevyDetailPo);
                } else {
                    replevyDetailPo.setId(UuidUtil.getUUID());
                    replevyDetailPo.setCurrency(Constants.CURRENCY_CNY);
                    replevyDetailPo.setReplevyId(replevyApiVo.getReplevyId());
                    replevyDetailPo.setReportNo(reportNo);//报案号
                    replevyDetailPo.setReplevyId(replevyApiVo.getReplevyId());//主表信息id
                    replevyDetailPo.setFlag(replevyApiVo.getSubmitFlag());//0代表数据不完整，1代表数据不完整或已提交
                    replevyDetailPo.setValidFlag("Y");//Y代表有效，N代表无效
                    replevyDetailPo.setReplevyNo(clmsReplevyMainPo.getReplevyNo());//追偿案件号
                    replevyDetailPo.setReplevyTimes(clmsReplevyMainPo.getReplevyTimes());//追偿次数
                    replevyDetailPo.setCaseTimes(clmsReplevyMainPo.getCaseTimes());//赔付次数
                    replevyDetailPo.setCreatedBy(userInfoDTO.getUserCode());//创建人
                    replevyDetailPo.setSysCtime(new Date());//创建时间
                    replevyDetailPo.setSysUtime(new Date());//修改时间
                    if(replevyDetailPo.getRepleviedName()!=null
                            &&replevyDetailPo.getRepleviedCertiType()!=null
                            &&replevyDetailPo.getRepleviedType()!=null
                            &&replevyDetailPo.getRepleviedCertiCode()!=null){
                        String bankAccountAttribute = "1".equals(replevyDetailPo.getRepleviedType()) ? "0" : "1";
                        String customerNo =  customerInfoService.getCustomerNo(replevyDetailPo.getRepleviedName(), bankAccountAttribute, replevyDetailPo.getRepleviedCertiType(), replevyDetailPo.getRepleviedCertiCode());
                        replevyDetailPo.setCustomerNo(customerNo);
                    }
                    clmsReplevyDetailMapper.saveClmsReplevyDetail(replevyDetailPo);
                }
            }
        } catch (Exception e) {
            throw new GlobalBusinessException("追偿明细表入库失败");
        }
        return replevyDetailPo;
    }

    /**
     * 追偿责任表入库
     * @param replevyApiVo
     * @param userInfoDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ClmsReplevyLossVo> saveOrUpdateReplevyLoss(ReplevyApiVo replevyApiVo,UserInfoDTO userInfoDTO) {
        ClmsReplevyMain clmsReplevyMainPo = replevyApiVo.getClmsReplevyMain();
        String reportNo = replevyApiVo.getReportNo();
        List<ClmsReplevyLossVo> replevyLossVoList = replevyApiVo.getReplevyLossVoList();
        try {
            if(StringUtils.isNotEmpty(replevyApiVo.getReplevyDetailId())){
                clmsReplevyLossMapper.deleteByReplevyDetailId(replevyApiVo.getReplevyDetailId());
            }
            if (!CollectionUtils.isEmpty(replevyLossVoList)) {
                for (ClmsReplevyLossVo replevyLossVo : replevyLossVoList) {
                    ClmsReplevyLoss replevyLossPo = new ClmsReplevyLoss();
                    BeanUtils.copyProperties(replevyLossVo, replevyLossPo);
                    replevyLossPo.setId(UuidUtil.getUUID());
                    replevyLossPo.setReportNo(reportNo);//报案号
                    replevyLossPo.setReplevyDetailId(replevyApiVo.getReplevyDetailId());//追偿明细主键id
                    replevyLossPo.setCreatedBy(userInfoDTO.getUserCode());//创建人
                    replevyLossPo.setSysCtime(new Date());//创建时间
                    replevyLossPo.setSysUtime(new Date());//修改时间
                    replevyLossPo.setReplevyNo(clmsReplevyMainPo.getReplevyNo());//追偿案件号
                    replevyLossPo.setReplevyTimes(clmsReplevyMainPo.getReplevyTimes());//追偿次数
                    replevyLossPo.setCaseTimes(clmsReplevyMainPo.getCaseTimes());//赔付次数
                    replevyLossPo.setFlag(replevyApiVo.getSubmitFlag());//0代表数据不完整，1代表数据不完整或已提交
                    replevyLossPo.setValidFlag("Y");//Y代表有效，N代表无效
                    clmsReplevyLossMapper.insertSelective(replevyLossPo);
                }
            }
        }catch (Exception e) {
            throw new GlobalBusinessException("追偿责任表入库失败");
        }
        return replevyLossVoList;
    }

    /**
     * 关联实收表入库
     * @param relatedActualReceiptList 关联实收列表
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateReplevyActualReceipt(List<ClmsRelatedActualReceiptVo> relatedActualReceiptList,String reportNo,int caseTimes,String businessId,String receiptType) {
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        try{
            if(StringUtils.isNotEmpty(businessId)){
                clmsRelatedActualReceiptMapper.deleteByBusinessId(businessId);
            }
            if (!CollectionUtils.isEmpty(relatedActualReceiptList)) {
                clmsRelatedActualReceiptMapper.deleteByBusinessId(businessId);
                for (ClmsRelatedActualReceiptVo relatedActualReceiptVo : relatedActualReceiptList) {
                    ClmsRelatedActualReceipt relatedActualReceiptPo = new ClmsRelatedActualReceipt();
                    BeanUtils.copyProperties(relatedActualReceiptVo, relatedActualReceiptPo);
                    relatedActualReceiptPo.setId(UuidUtil.getUUID());
                    relatedActualReceiptPo.setBusinessId(businessId);//追偿明细主键id
                    relatedActualReceiptPo.setReportNo(reportNo);//报案号
                    relatedActualReceiptPo.setCreatedBy(userInfoDTO.getUserCode());//创建人
                    relatedActualReceiptPo.setSysCtime(new Date());//创建时间
                    relatedActualReceiptPo.setSysUtime(new Date());//修改时间
                    relatedActualReceiptPo.setFlag("0");// 0-待核销，31-核销成功，32-核销失败
                    relatedActualReceiptPo.setValidFlag("Y");//Y代表有效，N代表无效
                    if(ReplevyConstant.RECEIPT_TYPE_RELEVY.equals(receiptType)){//追偿
                        relatedActualReceiptPo.setCaseTimes(1);//赔付次数
                        relatedActualReceiptPo.setSubTimes(caseTimes);//追偿次数
                    }else{
                        relatedActualReceiptPo.setCaseTimes(caseTimes);//赔付次数
                        relatedActualReceiptPo.setBatchNo(relatedActualReceiptVo.getBatchNO());//批次号

                    }
                    relatedActualReceiptPo.setReceiptType(receiptType);
                    clmsRelatedActualReceiptMapper.insertSelective(relatedActualReceiptPo);
                }
            }
        }catch (Exception e) {
            throw new GlobalBusinessException("关联实收表入库失败");
        }
    }

    /**
     * 发票信息入库
     * @param replevyApiVo
     * @param userInfoDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateInvoiceInfo(ReplevyApiVo replevyApiVo,UserInfoDTO userInfoDTO) {
        InvoiceInfoDTO invoiceInfoDTO = replevyApiVo.getInvoiceInfoDTO();
        String idAhcsInvoiceInfo = UuidUtil.getUUID();
        try {
            if (invoiceInfoDTO != null) {
                //数据库必录字段,页面暂存时给值
                if (StringUtils.isEmptyStr(invoiceInfoDTO.getInvoiceCode())) {
                    invoiceInfoDTO.setInvoiceCode("");
                }
                if (StringUtils.isEmptyStr(invoiceInfoDTO.getInvoiceNo())) {
                    invoiceInfoDTO.setInvoiceNo("");
                }
                if (StringUtils.isEmptyStr(invoiceInfoDTO.getInvoiceType())) {
                    invoiceInfoDTO.setInvoiceType("");
                }
                if (!StringUtils.isEmptyStr(invoiceInfoDTO.getIdAhcsInvoiceInfo())) {
                    invoiceInfoDTO.setUpdatedBy(userInfoDTO.getUserCode());//修改人
                    invoiceInfoDTO.setUpdatedDate(new Date());//修改时间
                    feePayMapper.updateInvoiceInfoById(invoiceInfoDTO);
                } else {
                    invoiceInfoDTO.setIdAhcsFeePay(replevyApiVo.getReplevyChargeId());//理赔费用id
                    invoiceInfoDTO.setCreatedBy(userInfoDTO.getUserCode());//创建人
                    invoiceInfoDTO.setIsModifiedFlag("0");
                    invoiceInfoDTO.setCreatedDate(new Date());//创建时间
                    invoiceInfoDTO.setUpdatedBy(userInfoDTO.getUserCode());//修改人
                    invoiceInfoDTO.setUpdatedDate(new Date());//修改时间
                    invoiceInfoDTO.setIdAhcsInvoiceInfo(idAhcsInvoiceInfo);
                    feePayMapper.addInvoiceInfo(invoiceInfoDTO);
                }
            }
        }catch (Exception e) {
            throw new GlobalBusinessException("发票信息入库失败");
        }
    }

    /**
     * 追偿费用明细信息入库
     * @param replevyApiVo
     * @param userInfoDTO
     * @param idClmPaytinfo 领款人主键
     * @param serialNo 序号
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ClmsReplevyCharge saveOrUpdateReplevyCharge(ReplevyApiVo replevyApiVo,UserInfoDTO userInfoDTO,String idClmPaytinfo,Integer serialNo) {
        ClmsReplevyChargeVo clmsReplevyChargeVo = replevyApiVo.getReplevyChargeVo();
        PaymentInfoVO paymentInfoVO = replevyApiVo.getPaymentInfoVo();
        ClmsReplevyCharge clmsReplevyCharge = new ClmsReplevyCharge();
        String idAhcsFeePay = UuidUtil.getUUID();// 费用主键
        try {
            if (clmsReplevyChargeVo !=null) {
                BeanUtils.copyProperties(clmsReplevyChargeVo, clmsReplevyCharge);
                clmsReplevyCharge.setClientName(paymentInfoVO.getClientName());//收款人名称
                clmsReplevyCharge.setClientAccount(paymentInfoVO.getClientBankAccount());//收款人账号
                clmsReplevyCharge.setPaymentInfoId(idClmPaytinfo);//领款人信息主键
                clmsReplevyCharge.setCurrency(Constants.CURRENCY_CNY);
                if (clmsReplevyChargeVo.getId() != null) {
                    clmsReplevyCharge.setApproveFlag("0".equals(replevyApiVo.getSubmitFlag())?"1":"2");// ApproveFlag 1-已申请，2-待审核，3-审核通过，4-退回 submitFlag 0保存 1提交
                    clmsReplevyCharge.setUpdatedBy(userInfoDTO.getUserCode());//修改人
                    clmsReplevyCharge.setSysUtime(new Date());//修改时间
                    clmsReplevyCharge.setReplevyNo(replevyApiVo.getClmsReplevyMain().getReplevyNo());//追偿案件号
                    clmsReplevyCharge.setCaseTimes(replevyApiVo.getClmsReplevyMain().getCaseTimes());//赔付次数
                    clmsReplevyCharge.setReplevyTimes(replevyApiVo.getClmsReplevyMain().getReplevyTimes());//追偿次数
                    clmsReplevyChargeMapper.updateByPrimaryKey(clmsReplevyCharge);
                } else {
                    clmsReplevyCharge.setId(idAhcsFeePay);
                    clmsReplevyCharge.setApplyLink("追偿");//申请环节 固定为追偿
                    clmsReplevyCharge.setReportNo(replevyApiVo.getReportNo());//报案号
                    clmsReplevyCharge.setCreatedBy(userInfoDTO.getUserCode());//操作人
                    clmsReplevyCharge.setSysCtime(new Date());//创建时间
                    clmsReplevyCharge.setSysUtime(new Date());//修改时间
                    clmsReplevyCharge.setValidFlag("Y");//Y代表有效，N代表无效
                    clmsReplevyCharge.setFlag("0");//0--代表追偿中,1代表追偿完成
                    clmsReplevyCharge.setApproveFlag("0".equals(replevyApiVo.getSubmitFlag())?"1":"2");// ApproveFlag 1-已申请，2-待审核，3-审核通过，4-退回 submitFlag 0保存 1提交
//                    clmsReplevyCharge.setInvoiceInfoId(idAhcsInvoiceInfo);//发票信息主键
                    clmsReplevyCharge.setReplevyNo(replevyApiVo.getClmsReplevyMain().getReplevyNo());//追偿案件号
                    clmsReplevyCharge.setCaseTimes(replevyApiVo.getClmsReplevyMain().getCaseTimes());//赔付次数
                    clmsReplevyCharge.setReplevyTimes(replevyApiVo.getClmsReplevyMain().getReplevyTimes());//追偿次数
                    clmsReplevyCharge.setSerialNo(serialNo+1);
                    clmsReplevyCharge.setApplyTime(new Date());
                    clmsReplevyChargeMapper.insertSelective(clmsReplevyCharge);
                }
            }
        }catch (Exception e) {
            throw new GlobalBusinessException("追偿费用明细信息入库失败");
        }
        return clmsReplevyCharge;
    }

    /**
     * 删除追偿费用信息
     */
    public ResponseResult<Object> deleteReplevyFeeInternal(ReplevyApiVo replevyApiVo) {
        try {
            // 获取当前用户信息
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            // 参数校验
            String replevyChargeId = replevyApiVo.getReplevyChargeId();
            ClmsReplevyCharge replevyCharge = clmsReplevyChargeMapper.selectById(replevyChargeId);
            if (replevyCharge == null) {
                throw new GlobalBusinessException("费用信息不存在");
            }

            // 删除追偿费用表中的数据
            ClmsReplevyCharge updateCharge = new ClmsReplevyCharge();
            updateCharge.setId(replevyChargeId);
            updateCharge.setValidFlag("N"); // 设置为无效
            updateCharge.setUpdatedBy(userInfoDTO.getUserCode());
            updateCharge.setSysUtime(new Date());
            clmsReplevyChargeMapper.updateSelectiveByPrimaryKey(updateCharge);

            // 获取追偿主表信息并更新总费用金额
            ClmsReplevyMainVo oldReplevyMain = clmsReplevyMainMapper.selectReplevyMain(replevyCharge.getReportNo(),null,null);
            if (oldReplevyMain != null && replevyCharge.getChargeMoney() != null) {
                // 直接减少总追偿费用金额，避免缓存问题
                clmsReplevyMainMapper.decreaseSumReplevyFee(oldReplevyMain.getId(), replevyCharge.getChargeMoney());
            }
            //删除发票信息
            feePayMapper.removeInvoiceInfo(replevyChargeId);
            return ResponseResult.success("费用信息删除成功");
        } catch (Exception e) {
            throw new GlobalBusinessException("删除费用信息失败");
        }
    }

    /**
     * 删除追偿明细信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> deleteReplevyDetailInternal(ReplevyApiVo replevyApiVo) {
        try {
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            String replevyDetailId = replevyApiVo.getReplevyDetailId();
            ClmsReplevyDetail replevyDetail = clmsReplevyDetailMapper.selectById(replevyDetailId);
            if (replevyDetail == null) {
                throw new GlobalBusinessException("追偿明细信息不存在");
            }
            // 删除追偿损失表数据
            List<ClmsReplevyLoss> lossList = clmsReplevyLossMapper.selectByReplevyDetailId(replevyDetailId);
            BigDecimal sumRepleviedMoney = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(lossList)) {
                for (ClmsReplevyLoss loss : lossList) {
                    // 累加要减少的追偿收入金额
                    if (loss.getRepleviedMoney() != null) {
                        sumRepleviedMoney = sumRepleviedMoney.add(loss.getRepleviedMoney());
                    }
                    ClmsReplevyLoss updateLoss = new ClmsReplevyLoss();
                    updateLoss.setId(loss.getId());
                    updateLoss.setValidFlag("N"); // 设置为无效
                    updateLoss.setUpdatedBy(userInfoDTO.getUserCode());
                    updateLoss.setSysUtime(new Date());
                    clmsReplevyLossMapper.updateSelectiveByPrimaryKey(updateLoss);
                }
            }
            // 删除关联实收表数据
            clmsRelatedActualReceiptMapper.deleteByBusinessId(replevyDetailId);
            // 删除追偿明细表数据（更新为无效状态）
            ClmsReplevyDetail updateDetail = new ClmsReplevyDetail();
            updateDetail.setId(replevyDetailId);
            updateDetail.setValidFlag("N"); // 设置为无效
            updateDetail.setUpdatedBy(userInfoDTO.getUserCode());
            updateDetail.setSysUtime(new Date());
            clmsReplevyDetailMapper.updateSelectiveByPrimaryKey(updateDetail);

            // 获取追偿主表信息并更新总收入金额
            ClmsReplevyMainVo oldReplevyMain = clmsReplevyMainMapper.selectReplevyMain(replevyDetail.getReportNo(),null,null);
            if (oldReplevyMain != null && sumRepleviedMoney.compareTo(BigDecimal.ZERO) > 0) {
                // 直接减少总追偿收入金额，避免缓存问题
                clmsReplevyMainMapper.decreaseSumRealReplevy(oldReplevyMain.getId(), sumRepleviedMoney);
            }

            return ResponseResult.success("追偿明细信息删除成功");
        } catch (Exception e) {
            throw new GlobalBusinessException("删除追偿明细信息失败");
        }
    }

    /**
     * 删除关联实收数据内部方法
     * initFlag = "3"
     * @param replevyApiVo 删除请求参数
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> deleteRelatedActualReceiptInternal(ReplevyApiVo replevyApiVo) {
        try {
            // 参数校验
            String relatedActualReceiptId = replevyApiVo.getRelatedActualReceiptId();
            if (relatedActualReceiptId == null) {
                throw new GlobalBusinessException("关联实收ID不能为空");
            }
            ClmsRelatedActualReceipt existingRecord = clmsRelatedActualReceiptMapper.selectByPrimaryKey(relatedActualReceiptId);
            if (existingRecord == null) {
                throw new GlobalBusinessException("关联实收数据信息不存在");
            }
            clmsRelatedActualReceiptMapper.deleteByPrimaryKey(relatedActualReceiptId);
            return ResponseResult.success("删除关联实收数据失败成功");
        } catch (Exception e) {
            throw new GlobalBusinessException("删除关联实收数据失败");
        }
    }

    /**
     * 查询险种和责任信息
     * @return 包含险种和责任信息的PlanAndDutyQueryDTO
     */
    @Override
    public ResponseResult<Object> queryPlanAndDutyInfo(ReplevyApiVo replevyApiVo) {
        try {
            // 参数校验
            if (replevyApiVo == null || replevyApiVo.getReportNo() == null || replevyApiVo.getReportNo().trim().isEmpty()) {
                throw new GlobalBusinessException("报案号不能为空");
            }
            // 创建返回对象
            PlanAndDutyQueryDTO responseResult = new PlanAndDutyQueryDTO();
            // 根据initFlag区分查询逻辑
            String initFlag = replevyApiVo.getInitFlag();
            if ("2".equals(initFlag)) { // 费用页面条款 责任查询
                //优先带出已赔付责任，没有带出立案责任
                List<PlanAndDutyQueryDTO.EstimateDutyInfoDTO> estimateDutyInfoList = new ArrayList<>();
                // 查询已赔付责任
                List<PlanDutyRemainingDTO> planDutyRemainingList = clmsPaymentDutyMapper.getPlanDutyRemainingByReportNo(replevyApiVo.getReportNo());
                if (!CollectionUtils.isEmpty(planDutyRemainingList)) {
                    for (PlanDutyRemainingDTO planDutyRemaining : planDutyRemainingList) {
                        PlanAndDutyQueryDTO.EstimateDutyInfoDTO estimateDutyInfo = new PlanAndDutyQueryDTO.EstimateDutyInfoDTO();
                        estimateDutyInfo.setDutyCode(planDutyRemaining.getDutyCode());
                        estimateDutyInfo.setDutyName(planDutyRemaining.getDutyName());
                        estimateDutyInfo.setPlanCode(planDutyRemaining.getPlanCode());
                        estimateDutyInfoList.add(estimateDutyInfo);
                    }
                }else {
                    // 查询立案责任
                    List<EstimateDutyRecordQueryDTO> estimateDutyRecordList = clmsEstimateDutyRecordMapper.getEstimateDutyRecordByReportNo(replevyApiVo.getReportNo());
                    if (!CollectionUtils.isEmpty(estimateDutyRecordList)) {
                        for (EstimateDutyRecordQueryDTO record : estimateDutyRecordList) {
                            PlanAndDutyQueryDTO.EstimateDutyInfoDTO estimateDutyInfo = new PlanAndDutyQueryDTO.EstimateDutyInfoDTO();
                            estimateDutyInfo.setDutyCode(record.getDutyCode());
                            estimateDutyInfo.setDutyName(record.getDutyName());
                            estimateDutyInfo.setPlanCode(record.getPlanCode());
                            estimateDutyInfoList.add(estimateDutyInfo);
                        }
                    }
                }
                responseResult.setEstimateDutyRecordList(estimateDutyInfoList);
            } else if ("1".equals(initFlag)) { // 追偿子页面条款 责任查询
                // 查询已赔付责任，以及剩余金额信息
                List<PlanDutyRemainingDTO> planDutyRemainingList = clmsPaymentDutyMapper.getPlanDutyRemainingByReportNo(replevyApiVo.getReportNo());

                if (!CollectionUtils.isEmpty(planDutyRemainingList)) {
                    // 按险种代码分组
                    Map<String, List<PlanDutyRemainingDTO>> planGroupMap = planDutyRemainingList.stream()
                        .collect(Collectors.groupingBy(PlanDutyRemainingDTO::getPlanCode));
                    
                    List<PlanAndDutyQueryDTO.PlanInfoDTO> planInfoList = new ArrayList<>();
                    
                    for (Map.Entry<String, List<PlanDutyRemainingDTO>> entry : planGroupMap.entrySet()) {
                        String planCode = entry.getKey();
                        List<PlanDutyRemainingDTO> dutyList = entry.getValue();
                        
                        // 创建险种信息
                        PlanAndDutyQueryDTO.PlanInfoDTO planInfo = new PlanAndDutyQueryDTO.PlanInfoDTO();
                        planInfo.setPlanCode(planCode);
                        planInfo.setPlanName(dutyList.get(0).getPlanName()); // 同一险种的名称相同
                        
                        // 创建责任信息列表
                        List<PlanAndDutyQueryDTO.DutyInfoDTO> dutyInfoList = new ArrayList<>();
                        for (PlanDutyRemainingDTO dutyRemaining : dutyList) {
                            PlanAndDutyQueryDTO.DutyInfoDTO dutyInfo = new PlanAndDutyQueryDTO.DutyInfoDTO();
                            dutyInfo.setDutyCode(dutyRemaining.getDutyCode());
                            dutyInfo.setDutyName(dutyRemaining.getDutyName());
                            dutyInfo.setRemainingAmount(dutyRemaining.getRemainingAmount());
                            dutyInfoList.add(dutyInfo);
                        }
                        
                        planInfo.setDutyList(dutyInfoList);
                        planInfoList.add(planInfo);
                    }
                    
                    responseResult.setPlanPayList(planInfoList);
                }
            }
            return ResponseResult.success(responseResult);
        } catch (Exception e) {
          throw new GlobalBusinessException("查询险种和责任信息失败");
        }
    }

    /**
     * 保存或审核追偿审核信息
     * @param replevyApiVo
     * @param initFlag 1-新增，2-修改
     * @param opinionType 1-追偿审批 2-费用审批
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> saveOrExamineReplevyText(ReplevyApiVo replevyApiVo, String initFlag, String opinionType) {
        try {
            ClmsReplevyChargeVo clmsReplevyChargeVo = replevyApiVo.getReplevyChargeVo();
            UserInfoDTO userInfoDTO = WebServletContext.getUser();
            // 根据initFlag区分操作类型
            if ("1".equals(initFlag)) {// 新增操作
                ClmsReplevyText replevyTextPo = new ClmsReplevyText();
                replevyTextPo.setId(UuidUtil.getUUID());
                replevyTextPo.setOpinionType(opinionType); // 1-追偿审批 2-费用审批
                replevyTextPo.setApplyUm(userInfoDTO.getUserCode());//申请人
                replevyTextPo.setMakeCom(userInfoDTO.getComCode()); // 操作机构
                if(ReplevyConstant.OPINION_TYPE_Z.equals(opinionType)){
                    replevyTextPo.setApplyText(replevyApiVo.getClmsReplevyMain().getReplevyText());// 追偿意见描述
                }else{
                    replevyTextPo.setApplyText(clmsReplevyChargeVo.getApplyReason());
                }
                replevyTextPo.setReplevyId(replevyApiVo.getClmsReplevyMain().getId()); // 追偿主表ID
                replevyTextPo.setReplevyChargeId(replevyApiVo.getReplevyChargeId()); // 追偿费用主表ID
                replevyTextPo.setReportNo(replevyApiVo.getReportNo()); // 报案号
                replevyTextPo.setReplevyNo(replevyApiVo.getClmsReplevyMain().getReplevyNo()); // 追偿号
                replevyTextPo.setReplevyTimes(replevyApiVo.getClmsReplevyMain().getReplevyTimes()); // 追偿次数
                replevyTextPo.setCaseTimes(replevyApiVo.getClmsReplevyMain().getCaseTimes()); // 赔付次数
                Date currentTime = new Date();
                replevyTextPo.setApplyDate(currentTime);//申请时间
                replevyTextPo.setCreatedBy(userInfoDTO.getUserCode());
                replevyTextPo.setSysCtime(currentTime);
                replevyTextPo.setUpdatedBy(userInfoDTO.getUserCode());
                replevyTextPo.setSysUtime(currentTime);
                replevyTextPo.setValidFlag("Y"); // 有效标志
                replevyTextPo.setFlag("0"); // 标志字段：0-审批中，1-审批通过
                clmsReplevyTextMapper.insertSelective(replevyTextPo);
                replevyTextPo.setId(replevyTextPo.getId());
            } else if ("2".equals(initFlag)) {// 审核修改操作

            } else {
                log.error("不支持的操作类型: {}", initFlag);
            }
            return ResponseResult.success("追偿审核信息保存成功");
        } catch (Exception e) {
            throw new GlobalBusinessException("保存或审核追偿审核信息失败");
        }
    }

    /**
     * 计算更新总追偿收入金额
     * @param replevyApiVo 追偿API参数
     * @param amtFlag 1-计算总追偿收入金额，2-计算总追偿费用金额
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTotalRepleviedMoney(ReplevyApiVo replevyApiVo, String amtFlag) {
        try {
            if("1".equals(amtFlag)){
                BigDecimal sumRepleviedMoney = clmsReplevyLossMapper.getTotalRepleviedMoney(replevyApiVo.getClmsReplevyMain().getReplevyNo());
                clmsReplevyMainMapper.updateSumRealReplevy(replevyApiVo.getClmsReplevyMain().getId(), sumRepleviedMoney,null);
            }else if("2".equals(amtFlag)){
                BigDecimal sumChargeMoney = clmsReplevyChargeMapper.getTotalChargeMoney(replevyApiVo.getClmsReplevyMain().getReplevyNo());
                clmsReplevyMainMapper.updateSumRealReplevy(replevyApiVo.getClmsReplevyMain().getId(), null,sumChargeMoney);
            }

        } catch (Exception e) {
           throw new GlobalBusinessException("计算更新总追偿收入金额失败");
        }
    }

    /**
     * 根据报案号查询追偿主表信息
     * @param replevyApiVo
     * @return 追偿主表查询结果集合
     */
    @Override
    public ResponseResult<Object> queryReplevyMainByReportNo(ReplevyApiVo replevyApiVo) {
        try {
            String reportNo = replevyApiVo.getReportNo();
            ResponseResult<Object> responseResult = new ResponseResult<Object>();
            List<ReplevyMainQueryDTO> resultList = new ArrayList<>();
            ReplevyMainQueryResultDTO resultDTO = new ReplevyMainQueryResultDTO();
            responseResult.setCode("000000");
            responseResult.setData(resultDTO);
            // 案件信息
            ReportInfoEntity reportInfo = reportInfoMapper.getReportInfo(reportNo);
            if (reportInfo == null) {
                responseResult.setMsg("未查询到相关信息，请核实!");
                return responseResult;
            }
            // 总公司能查全部，分公司只能查分公司及下级
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
            if(departmentCodes.contains(reportInfo.getAcceptDepartmentCode())){
                resultList = clmsReplevyMainMapper.queryReplevyMainByReportNo(reportNo);
                for (ReplevyMainQueryDTO replevyMainQueryDTO : resultList) {
                    replevyMainQueryDTO.setProcessStatusName(CaseProcessStatus.getName(replevyMainQueryDTO.getProcessStatus()));
                }
                // 所有追偿案件都审核通 或 没有追偿案件 显示按钮，且需要已立案
                Integer displayButton = 0;
                if(registerCaseService.isExistRegisterRecord(reportNo, 1)) {
                    if (!CollectionUtils.isEmpty(resultList)) {
                        boolean allFlag2 = resultList.stream().allMatch(item -> "2".equals(item.getFlag()));
                        displayButton = allFlag2 ? 1 : 0;
                    }else {
                        displayButton = 1;
                    }
                }
                resultDTO.setDisplayButton(displayButton);

                if (resultList.isEmpty()) {
                    responseResult.setMsg("该案件无历史追偿任务");
                    return responseResult;
                }
            }else{
                responseResult.setMsg("未查询到相关信息，请核实!");
                return responseResult;
            }
            resultDTO.setResultList(resultList);
            responseResult.setMsg("成功");
            return responseResult;
        } catch (GlobalBusinessException e) {
            throw new GlobalBusinessException(e.getMessage());
        }catch (Exception e) {
            throw new GlobalBusinessException("根据报案号查询追偿主表信息失败" );
        }
    }

    /**
     * 追偿提交校验
     * @param replevyApiVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean checkReplevySubmit(ReplevyApiVo replevyApiVo) {
        ClmsReplevyMain clmsReplevyMain = replevyApiVo.getClmsReplevyMain();
        String reportNo = clmsReplevyMain.getReportNo();
        String replevyNo = clmsReplevyMain.getReplevyNo();
        
        // 校验追偿收入明细是否存在
        List<ClmsReplevyDetailVo> replevyDetailList = clmsReplevyDetailMapper.selectClmsReplevyDetail(reportNo, replevyNo);
        if (CollectionUtils.isEmpty(replevyDetailList)) {
            throw new GlobalBusinessException("请录入追偿收入明细");
        }
        
        // 校验追偿收入明细是否都已提交
        boolean hasUnsubmitted = replevyDetailList.stream()
                .anyMatch(detail -> !"1".equals(detail.getFlag()));
        if (hasUnsubmitted) {
            throw new GlobalBusinessException("追偿收入明细存在必录项未录入");
        }
        
        // 费用都审核通过后才允许提交
        List<ClmsReplevyChargeVo> chargeList = clmsReplevyChargeMapper.selectClmsReplevyCharge(reportNo, replevyNo);
        if (!CollectionUtils.isEmpty(chargeList)) {
            boolean allApproved = chargeList.stream()
                    .allMatch(charge -> "3".equals(charge.getApproveFlag()));
            if (!allApproved) {
                throw new GlobalBusinessException("存在未审核通过的费用信息，不允许提交");
            }
        }
        
        return true;
    }



    /**
     * 追偿子页面提交校验
     * @param replevyApiVo
     * @param flag 1-主页面 2-子页面 3-费用页
     * @return
     */
    private void replevyCheckSubmit(ReplevyApiVo replevyApiVo,String flag) {
        if("2".equals(flag)){
            // 开始校验》》》 历史追偿金额 + 本次追偿金额 <= 历史责任赔付金额
            List<ClmsReplevyLossVo> replevyLossVoList = replevyApiVo.getReplevyLossVoList();

            // 查询历史追偿金额总和
            ClmsReplevyMain oldReplevyMain = clmsReplevyMainMapper.selectSerialNo(replevyApiVo.getReportNo());
            List<DutyPlanAmountDTO> historyRepleviedList;
            Map<String, BigDecimal> historyRepleviedMap = new HashMap<>();
            if (oldReplevyMain != null) {
                historyRepleviedList = clmsReplevyLossMapper.getHistoryRepleviedAmountByReplevyNo(oldReplevyMain.getReportNo(),replevyApiVo.getReplevyDetailVo().getId());
                if (historyRepleviedList != null && !historyRepleviedList.isEmpty() && historyRepleviedList.get(0) != null) {
                    historyRepleviedMap = historyRepleviedList.stream()
                            .filter(item -> item.getDutyCode() != null && !item.getDutyCode().isEmpty())
                            .filter(item -> item.getPlanCode() != null && !item.getPlanCode().isEmpty())
                            .filter(item -> item.getTotalAmount() != null)
                            .collect(Collectors.toMap(
                                    item -> item.getDutyCode() + "_" + item.getPlanCode(),
                                    DutyPlanAmountDTO::getTotalAmount,
                                    (existing, replacement) -> existing
                            ));
                }
            }

            // 查询历史责任赔付金额总和
            List<PlanDutyRemainingDTO> historyDutyPayList = clmsPaymentDutyMapper.getPlanDutyRemainingByReportNo(replevyApiVo.getReportNo());
            Map<String, BigDecimal> historyDutyPayMap = new HashMap<>();
            if (historyDutyPayList != null && !historyDutyPayList.isEmpty() && historyDutyPayList.get(0) != null) {
                historyDutyPayMap = historyDutyPayList.stream()
                        .filter(item -> item.getDutyCode() != null && !item.getDutyCode().isEmpty())
                        .filter(item -> item.getPlanCode() != null && !item.getPlanCode().isEmpty())
                        .filter(item -> item.getDutyPayAmount() != null)
                        .collect(Collectors.toMap(
                                item -> item.getDutyCode() + "_" + item.getPlanCode(),
                                PlanDutyRemainingDTO::getDutyPayAmount,
                                (existing, replacement) -> existing
                        ));
            }

            // 按责任代码和险种分组本次新增的追偿金额
            Map<String, BigDecimal> currentRepleviedMap = new HashMap<>();
            for (ClmsReplevyLossVo replevyLossVo : replevyLossVoList) {
                String key = replevyLossVo.getDutyCode() + "_" + replevyLossVo.getPlanCode();
                BigDecimal currentAmount = replevyLossVo.getRepleviedMoney() == null ? BigDecimal.ZERO : replevyLossVo.getRepleviedMoney();
                currentRepleviedMap.merge(key, currentAmount, BigDecimal::add);
            }

            List<String> errorMessages = new ArrayList<>();
            for (Map.Entry<String, BigDecimal> entry : currentRepleviedMap.entrySet()) {
                String key = entry.getKey();
                BigDecimal currentAmount = entry.getValue();

                // 历史追偿金额
                BigDecimal historyReplevied = historyRepleviedMap.getOrDefault(key, BigDecimal.ZERO);
                // 历史责任赔付金额
                BigDecimal historyDutyPay = historyDutyPayMap.getOrDefault(key, BigDecimal.ZERO);
                // 历史追偿金额 + 本次追偿金额
                BigDecimal totalReplevied = historyReplevied.add(currentAmount);
                if (totalReplevied.compareTo(historyDutyPay) > 0) {
                    String[] keyParts = key.split("_");
                    String dutyCode = keyParts[0];
                    String planCode = keyParts[1];

                    // 获取责任名称和险种名称
                    String dutyName = historyDutyPayList.stream()
                            .filter(item -> dutyCode.equals(item.getDutyCode()) && planCode.equals(item.getPlanCode()))
                            .findFirst()
                            .map(PlanDutyRemainingDTO::getDutyName)
                            .orElse(dutyCode);

                    String errorMessage = String.format("责任名称:[%s(%s)] 实际追回金额总和[%s]不得超过该责任历史赔付金额[%s]",
                            dutyName, dutyCode, totalReplevied, historyDutyPay);
                    errorMessages.add(errorMessage);
                }
            }

            if (!errorMessages.isEmpty()) {
                throw new GlobalBusinessException(String.join(";", errorMessages));
            }

            // 开始校验》》》 校验核销金额总和 = 实际追回金额总和
            // 实际追回金额总和
            BigDecimal totalRepleviedMoney = replevyApiVo.getReplevyLossVoList().stream().
                    map(ClmsReplevyLossVo::getRepleviedMoney)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 核销金额总和
            BigDecimal totalWriteOffAmount = replevyApiVo.getRelatedActualReceiptVoList().stream()
                    .map(ClmsRelatedActualReceiptVo::getWriteOffAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalRepleviedMoney.compareTo(totalWriteOffAmount) != 0) {
                throw new GlobalBusinessException(String.format("核销金额总和需要等于实际追回金额总和，实际追回金额：%s，核销金额：%s",
                        totalRepleviedMoney, totalWriteOffAmount));
            }
        }else if("3".equals(flag)) {
            // 开始校验》》》 费用申请金额大于100W时，领款人银行账户明细需要填写
            if (replevyApiVo.getReplevyChargeVo() != null) {
                BigDecimal chargeMoney = replevyApiVo.getReplevyChargeVo().getChargeMoney();
                if (chargeMoney != null && chargeMoney.compareTo(new BigDecimal("1000000")) > 0) {
                    if(replevyApiVo.getPaymentInfoVo() != null && StringUtils.isNotEmpty(replevyApiVo.getPaymentInfoVo().getIdClmPaymentInfo())){
                        PaymentInfoDTO paymentInfoDTO = paymentInfoMapper.getPaymentInfoById(replevyApiVo.getPaymentInfoVo().getIdClmPaymentInfo());
                        if (!(paymentInfoDTO != null && StringUtils.isNotEmpty(paymentInfoDTO.getBankDetail()))) {
                            throw new GlobalBusinessException("费用申请金额大于100万时，领款人银行账户明细必须填写");
                        }
                    }else {
                        throw new GlobalBusinessException("费用申请金额大于100万时，领款人银行账户明细必须填写");
                    }
                }
            }

            // 开始校验》》》 若案件为主共保案件，且费用发票类型为专票，则需要校验所关联责任非免税责任
//            if(replevyApiVo.getReplevyChargeVo() != null){
//                List<CaseBaseEntity> caseBaseList = caseBaseMapper.getCaseBaseInfoByReportNo(replevyApiVo.getReportNo());
//                if (!caseBaseList.isEmpty()) {
//                    List<CoinsureDTO> coinsureDTOList = coinsureService.getCoinsureByPolicyNo(caseBaseList.get(0).getPolicyNo());
//                    if (!CollectionUtils.isEmpty(coinsureDTOList) && "2".equals(coinsureDTOList.get(0).getCoinsuranceType())
//                            && replevyApiVo.getInvoiceInfoDTO() != null &&
//                            ("004".equals(replevyApiVo.getInvoiceInfoDTO().getInvoiceType()) ||
//                             "028".equals(replevyApiVo.getInvoiceInfoDTO().getInvoiceType()) ||
//                             "030".equals(replevyApiVo.getInvoiceInfoDTO().getInvoiceType()))) {
//                            // 关联责任非免税责任
//                        Boolean isNonTax = ocasMapper.checkNonTaxExemptDuty(replevyApiVo.getReplevyChargeVo().getDutyCode(),
//                                replevyApiVo.getReplevyChargeVo().getPlanCode(), caseBaseList.get(0).getPolicyNo());
//                        if (!isNonTax) {
//                            DutyPayDTO dutyName = dutyPayMapper.getDutyName(replevyApiVo.getReportNo(),replevyApiVo.getReplevyChargeVo().getDutyCode(),replevyApiVo.getReplevyChargeVo().getPlanCode());
//                            throw new GlobalBusinessException("责任 " + dutyName.getDutyName() + " 为免税责任，请重新选择责任！");
//                        }
//                    }
//                }
//            }
        }
    }
    //构建支付项及条款责任
    public void buildPaymemt(ClmsReplevyMain clmsReplevyMain) {
        String reportNo = clmsReplevyMain.getReportNo();
        if(BigDecimalUtils.isEqual(clmsReplevyMain.getSumRealReplevy(), BigDecimal.ZERO)){//追偿总金额为0，不送收付
            return;
        }
        String batchNo = replevyChargeService.generateBatchNumber();
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(clmsReplevyMain.getPolicyNo());
        String departmentCode = MapUtils.getString(productMap,"departmentCode");
        String generateNo = commonService.generateNo(NoConstants.PLAN_BOOK_NO, VoucherTypeEnum.PLAN_BOOK_NO, departmentCode);
        clmsReplevyMain.setCompensateNo(generateNo);
        //查询追偿明细
        List<PaymentItemDTO> paymentItemDTOList = new ArrayList<>();
        List<ClmsReplevyDetailVo> clmsReplevyDetailList = clmsReplevyDetailMapper.selectClmsReplevyDetail(reportNo,clmsReplevyMain.getReplevyNo());
        for(ClmsReplevyDetailVo clmsReplevyDetailVo : clmsReplevyDetailList){
            List<ClmsRelatedActualReceipt> clmsRelatedActualReceiptList = clmsRelatedActualReceiptMapper.getListByBusinessId(clmsReplevyDetailVo.getId());
            for(ClmsRelatedActualReceipt clmsRelatedActualReceipt : clmsRelatedActualReceiptList){
                PaymentItemDTO p = replevyChargeService.buildPaymemtItem(clmsRelatedActualReceipt, clmsReplevyMain);
                String bankAccountAttribute = "1".equals(clmsReplevyDetailVo.getRepleviedType()) ? "0" : "1";
                p.setBankAccountAttribute(bankAccountAttribute);
                if("0".equals(bankAccountAttribute)&&(p.getBankDetail()==null||p.getBankDetail().isEmpty())){
                    throw new GlobalBusinessException("关联实收的打款银行账户开户行名称不能为空！");
                }
                p.setClientName(clmsReplevyDetailVo.getRepleviedName());
                p.setClientCertificateType(clmsReplevyDetailVo.getRepleviedCertiType());
                p.setClientCertificateNo(clmsReplevyDetailVo.getRepleviedCertiCode());
                p.setCustomerNo(clmsReplevyDetailVo.getCustomerNo());
                p.setBatchNo(batchNo);
                paymentItemDTOList.add(p);
                clmsRelatedActualReceiptMapper.updateSelectiveByPrimaryKey(clmsRelatedActualReceipt);
            }
        }
        clmsRelatedActualReceiptMapper.updateBatchNo(batchNo,reportNo,null,clmsReplevyMain.getReplevyTimes());
        paymentItemService.addPaymentItemList(paymentItemDTOList);
        List<PolicyPayDTO> policyPayList =new ArrayList<>();
        PolicyPayDTO policyPay = new PolicyPayDTO();
        policyPay.setPolicyNo(clmsReplevyMain.getPolicyNo());
        policyPay.setSettleAmount(clmsReplevyMain.getSumRealReplevy().multiply(new BigDecimal("-1")));
        List<PlanPayDTO> planPayArr = new ArrayList<>();
        //查询追偿条款责任
        List<ClmsReplevyLoss> clmsPlan = clmsReplevyLossMapper.selectPlanByReplevyNo(clmsReplevyMain.getReplevyNo());
        for(ClmsReplevyLoss clmsReplevyLoss : clmsPlan) {
            PlanPayDTO planPay = new PlanPayDTO();
            planPay.setPlanCode(clmsReplevyLoss.getPlanCode());
            planPay.setSettleAmount(clmsReplevyLoss.getRepleviedMoney().multiply(new BigDecimal("-1")));
            List<DutyPayDTO> dutyPayArr = new ArrayList<>();
            List<ClmsReplevyLoss> clmsReplevydutys = clmsReplevyLossMapper.selectByReplevyNoAndPlanCode(clmsReplevyMain.getReplevyNo(),clmsReplevyLoss.getPlanCode());
            for(ClmsReplevyLoss replevyduty:clmsReplevydutys){
                DutyPayDTO dutyPay = new DutyPayDTO();
                dutyPay.setDutyCode(replevyduty.getDutyCode());
                dutyPay.setSettleAmount(replevyduty.getRepleviedMoney().multiply(new BigDecimal("-1")));
                dutyPayArr.add(dutyPay);
            }
            planPay.setDutyPayArr(dutyPayArr);
            planPayArr.add(planPay);
        }
        policyPay.setPlanPayArr(planPayArr);
        policyPayList.add(policyPay);
        verifyService.splitPlanAndDuty(policyPayList,paymentItemDTOList);
    }
    /**
     * 追偿发起前校验
     *
     * @param replevyReqApiVo
     */
    @Override
    public ResponseResult<Object> checkReplevy(ReplevyReqApiVo replevyReqApiVo) {
        String reportNo = replevyReqApiVo.getReportNo();
        if (StringUtils.isEmptyStr(reportNo)) {
            throw new GlobalBusinessException(GlobalResultStatus.VALIDATE_PARAM_ERROR);
        }
        if (!checkUserPower( replevyReqApiVo.getReportNo(), BpmConstants.OC_REPLEVY)) {
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"该账号没有追偿权限");
        }
        if (!registerCaseService.isExistRegisterRecord(reportNo, 1)) {
            throw new GlobalBusinessException(GlobalResultStatus.FAIL.getCode(),"案件未立案不允许发起追偿");
        }

        boolean isExist = true;
        List<ReplevyMainQueryDTO> replevyMainList = clmsReplevyMainMapper.queryReplevyMainByReportNo(reportNo);
        if (!CollectionUtils.isEmpty(replevyMainList)) {
            ReplevyMainQueryDTO latestReplevy = replevyMainList.get(0);
            String flag = latestReplevy.getFlag();
            if ("1".equals(flag)) {// 追偿待审核，只读页面
                isExist = false;
            } else if ("0".equals(flag)) {
                UserInfoDTO userInfoDTO = WebServletContext.getUser();
                String userCode = userInfoDTO.getUserCode();
                String assigner = latestReplevy.getAssigner();
                if (assigner != null && !assigner.equals(userCode)) { // 追偿处理中，登录人和处理人不一致
                    isExist = false;
                }
            }
        }
        return ResponseResult.success(isExist);
    }
    //人员追偿权限
    public boolean checkUserPower(String reportNo,String bpmKey) {
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        Boolean flag = false;
        String gradeName = NcbsConstant.GRADE_MAP.get(bpmKey);
        //根据报案号查询机构
        String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
        List<String> gradeNames = Collections.emptyList();
        try {
            if (StringUtils.isNotEmpty(userInfoDTO.getUserCode())) {
                // 获取当前用户拥有的 角色 集合
                List<UserGradeInfoDTO> userGradeInfos = cacheService.queryUserGradeList(userInfoDTO.getUserCode(), acceptDepartmentCode);
                gradeNames = userGradeInfos.stream().map(UserGradeInfoDTO::getGradeName).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取岗位信息异常", e);
        }

        if (!gradeNames.isEmpty() && org.apache.commons.collections4.CollectionUtils.containsAny(gradeNames, Arrays.asList(gradeName.split(",")))) {
            flag = true;
        }
        return flag;
    }
}
