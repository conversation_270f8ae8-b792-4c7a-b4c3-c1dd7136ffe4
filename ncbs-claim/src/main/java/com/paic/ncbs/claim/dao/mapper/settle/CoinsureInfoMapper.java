package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CoinsureInfoMapper extends BaseDao<CoinsureInfoDTO> {

    int getCoinsureCountByReportNo(@Param("reportNo") String reportNo);

    List<CoinsureDTO> getCoinsureDescByReportNo(@Param("reportNo") String reportNo);

    List<CoinsureDTO> getCoinsureByPolicyNo(@Param("policyNo") String policyNo);

    List<CoinsureDTO> getCoinsureListByReportNo(@Param("reportNo") String reportNo);

}
