package com.paic.ncbs.claim.dao.mapper.notice;

import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.vo.notice.NoticesVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 消息提醒表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface NoticesMapper extends BaseMapper<NoticesDTO> {
    List<NoticesVO> getNoticeList(NoticesVO noticesVO);

    void updateNoticesStatus(@Param("noticesId") String noticesId, @Param("status") String status,@Param("userId") String userId);

    List<NoticesVO> getDefaultList(@Param("recipient")String recipient);

    void deleteNoticesByDate(@Param("dateTimeD") LocalDate dateTimeD);

    /**
     * 查询人伤跟踪消息提醒数据
     * @param recipient
     * @return
     */
    List<NoticesVO> getPersonTraceDefaultList(NoticesVO noticesVO);
}
