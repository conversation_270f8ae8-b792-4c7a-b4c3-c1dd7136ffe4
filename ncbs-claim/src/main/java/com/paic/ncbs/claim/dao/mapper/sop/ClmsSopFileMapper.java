package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP文件表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopFileMapper {

    /**
     * 根据SOP主键查询文件列表
     *
     * @param idSopMain SOP主键
     * @return 文件列表
     */
    List<ClmsSopFile> selectByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 根据ID查询文件
     *
     * @param idSopFile 文件ID
     * @return 文件信息
     */
    ClmsSopFile selectByPrimaryKey(@Param("idSopFile") String idSopFile);

    /**
     * 根据文件ID查询文件
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    ClmsSopFile selectByFileId(@Param("fileId") String fileId);

    /**
     * 插入文件
     *
     * @param record 文件信息
     * @return 影响行数
     */
    int insert(ClmsSopFile record);

    /**
     * 选择性插入文件
     *
     * @param record 文件信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopFile record);

    /**
     * 选择性更新文件
     *
     * @param record 文件信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ClmsSopFile record);

    /**
     * 更新文件
     *
     * @param record 文件信息
     * @return 影响行数
     */
    int updateByPrimaryKey(ClmsSopFile record);

    /**
     * 根据SOP主键删除文件
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 根据SOP主键批量插入文件
     *
     * @param fileList 文件列表
     * @return 影响行数
     */
    int batchInsert(@Param("fileList") List<ClmsSopFile> fileList);

    /**
     * 根据SOP主键更新文件有效标识
     *
     * @param idSopMain SOP主键
     * @param validFlag 有效标识
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int updateValidFlagByIdSopMain(@Param("idSopMain") String idSopMain,
                                   @Param("validFlag") String validFlag,
                                   @Param("updatedBy") String updatedBy);

    /**
     * 根据ID删除文件
     *
     * @param idSopFile 文件ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("idSopFile") String idSopFile);

}
