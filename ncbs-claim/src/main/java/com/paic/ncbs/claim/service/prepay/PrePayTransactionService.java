package com.paic.ncbs.claim.service.prepay;

import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PreApproveVO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePayCaseVO;

import java.util.List;

public interface PrePayTransactionService {

	void savePrePayApplyInfo(PrePayCaseVO preCaseVO, PrePayInfoDTO preDTO, List<DutyPrepayInfoDTO> dutyList,
							 List<PaymentItemDTO> itemList);

	void savePrePayApprove(PreApproveVO preApproveVO,Boolean flag);

	void savePrePayFeeInfo(String reportNo, Integer caseTimes, String userId , Integer subTimes , List<FeeInfoDTO> feeList, List<InvoiceInfoDTO> invoiceList);

	void delPrePayFeeInfo(String reportNo, Integer caseTimes, String userId , Integer subTimes);
}
