package com.paic.ncbs.claim.sao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.vo.policy.OCASPolicyVO;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.settle.PolicyInfoInitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Service
@Slf4j
@RefreshScope
public class CustomerInfoStoreSAOImpl implements CustomerInfoStoreSAO {

    @Autowired
    private PolicyInfoInitService policyInfoInitService;

    @Autowired
    private OcasRequest ocasRequest;

    @Autowired
    @Qualifier("batchPool")
    private Executor executor;

    @Value("${pos.api.enable:true}")
    private boolean posApiEnable;

    @Value("${pos.crm.enable:true}")
    private boolean posCrmEnable;

    @Value("${switch.mesh}")
    private Boolean switchMesh;


    @Override
    public Set<String> getPolicyByCertificationNo(String certificateNo) {
        JSONObject result = getPolicyByCertNo(certificateNo);
        JSONArray edrApplyList = result.getJSONArray("edrApplyList");
        if (edrApplyList == null || edrApplyList.isEmpty()) {
            return new HashSet<>();
        }
        Set<String> policyNoSet = new TreeSet<>();
        for (int i = 0; i < edrApplyList.size(); i++) {
            String policyNo = edrApplyList.getJSONObject(i).getString("policyNo");
            if (StringUtils.isNotEmpty(policyNo)) {
                policyNoSet.add(policyNo);
            }
        }
        return policyNoSet;
    }

    @Override
    public List<AhcsPolicyDomainDTO> getPolicyDomainInfo(Map<String, String> param) {
        String res = getPolicyInfoByPolicyNo(param);
        AhcsPolicyDomainDTO policyDto = new AhcsPolicyDomainDTO();

       /* JSONObject js = MockJsonUtil.getJsonObjectFromFile("qianxin.json");
        String res = js.toJSONString();*/

      /*  JSONObject js = null;
        String policyNo = param.get("policyNo");
        if ("1232010200000000439".equals(policyNo)){
            js = MockJsonUtil.getJsonObjectFromFile("1.json");
        }else if ("1232010200000000444".equals(policyNo)){
            js = MockJsonUtil.getJsonObjectFromFile("2.json");
        }else if("1232010200000000445".equals(policyNo)){
            js = MockJsonUtil.getJsonObjectFromFile("3.json");
        }

        String res = js.toJSONString();*/

        String idPlyRiskPerson = MapUtils.getString(param, "idPlyRiskPerson");
        // 一个保单可能有多个被保人信息，根据idPlyRiskPerson筛选被保人
        return policyInfoInitService.buildPolicyDomain(policyDto, res, idPlyRiskPerson);
    }

    @Override
    public AhcsPolicyDomainDTO getPolicyDomainInfoByPolicyNo(Map<String, String> param) {
        AhcsPolicyDomainDTO policyDto = new AhcsPolicyDomainDTO();
        String res = getPolicyInfoByPolicyNo(param);
        // null代表被保人信息不做筛选，查全部被保人
        policyInfoInitService.buildPolicyDomain(policyDto, res, null);
        return policyDto;
    }

    @Override
    public List<ReportCustomerInfoVO> getApplicantInfoList(Map<String, String> param) {

        JSONArray jarr = null;

        if (posApiEnable) {
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(param.get("policyNo"), null);
            String res = copyPolicy(queryVO);
            JSONObject j = JSONObject.parseObject(res);
            jarr = j.getJSONObject("data").getJSONObject("contractDTO").getJSONArray("applicantInfoList");
        } else {
            String res = getPolicyInfoByPolicyNo(param);
            JSONObject j = JSONObject.parseObject(res);
            jarr = j.getJSONObject("contractDTO").getJSONArray("applicantInfoList");
        }
        if (jarr == null) {
            return new ArrayList<>();
        }
        return jarr.toJavaList(ReportCustomerInfoVO.class);
    }

    @Override
    public List<ReportCustomerInfoVO> getInsuredList(Map<String, String> param) {

        JSONArray jarr1 = null;
        if (posApiEnable) {
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(param.get("policyNo"), null);
            String res = copyPolicy(queryVO);
            JSONObject j = JSONObject.parseObject(res);
            jarr1 = j.getJSONObject("data").getJSONObject("contractDTO").getJSONArray("riskGroupInfoList");
        } else {
            String res = getPolicyInfoByPolicyNo(param);
            JSONObject j = JSONObject.parseObject(res);
            jarr1 = j.getJSONObject("contractDTO").getJSONArray("riskGroupInfoList");
        }
        if (jarr1 == null) {
            return new ArrayList<>();
        }
        List<ReportCustomerInfoVO> list = new ArrayList<>();
        for (int i = 0; i < jarr1.size(); i++) {
            JSONArray jarr2 = jarr1.getJSONObject(i).getJSONArray("riskPersonInfoList");
            if (jarr2 != null) {
                for (int k = 0; k < jarr2.size(); k++) {
                    ReportCustomerInfoVO vo = jarr2.getJSONObject(k).toJavaObject(ReportCustomerInfoVO.class);
                    vo.setCertificateTypeName(CertificateTypeEnum.getName(vo.getCertificateType()));
                    list.add(vo);
                }
            }

        }
        return list;
    }

    @Override
    public List<OCASPolicyVO> getPolicyListByCertificationNo(String certificateNo) {
        JSONObject jo = getPolicyByCertNo(certificateNo);
        if (jo == null) {
            return new ArrayList<>();
        }
        JSONArray jarr = jo.getJSONArray("edrApplyList");
        if (jarr == null || jarr.isEmpty()) {
            return new ArrayList<>();
        }
        return jarr.toJavaList(OCASPolicyVO.class);
    }

    private String getPolicyInfoByPolicyNo(Map<String, String> param) {
        param.put("productKind", "0");
        param.put("isElecSubPolicyNo", "0");
        param.put("umsDisplayFlag", "claim");
        LogUtil.audit("抄单接口入参={}", JSON.toJSONString(param));
        long start = System.currentTimeMillis();
        String res = ocasRequest.getPolicyInfoByPolicyNo(param);
        LogUtil.audit("抄单接口耗时={}", System.currentTimeMillis() - start);
        LogUtil.audit("抄单接口出参={}", res);
        return res;

    }

    private JSONObject getPolicyByCertNo(String certificateNo) {
        /**
         * 必传字段
         * {
         * "insuredCertificateType":"02",
         * "insuredCertificateNo":"310115199209042214",
         * "pageNum":1,
         * "pageSize":10,
         * "productClass":"03",
         * "queryFrom":"claim"
         * }
         */
        Map<String, Object> param = new HashMap<>();
        param.put("insuredCertificateNo", certificateNo);
        param.put("insuredCertificateType", "01");
        param.put("pageNum", 1);
        param.put("pageSize", 100);
        param.put("productClass", "03");
        param.put("queryFrom", "claim");
        return JSON.parseObject(JSON.toJSONString(ocasRequest.getEdrApplyList(param)), JSONObject.class);
    }


    private String copyPolicy(CopyPolicyQueryVO queryVO) {
        LogUtil.audit("抄单接口入参={}", JSON.toJSONString(queryVO));
        long start = System.currentTimeMillis();
        String result = ocasRequest.getPolicyInfoByPolicyNoContract(queryVO);
        LogUtil.audit("抄单接口耗时={}", System.currentTimeMillis() - start);
        LogUtil.audit("抄单接口出参={}", result);
        return result;
    }

    @Override
    public AhcsPolicyDomainDTO getPolicyDomainInfo(CopyPolicyQueryVO queryVO) {
        return policyInfoInitService.buildPolicyDomain(copyPolicy(queryVO), queryVO);
    }

    @Override
    public String searchCustomerRating(RatingQueryVO queryVO) {
        if (!posCrmEnable) {
            return "";
        }
        try {
            LogUtil.audit("CRM接口入参={}", JSON.toJSONString(queryVO));
            String rating = ocasRequest.searchCustomerRating(queryVO);
            LogUtil.audit("CRM接口出参={}", rating);
            return rating;

        } catch (Exception e) {
        }
        return "";
    }

    @Override
    @Deprecated
    public String queryCustomerRating(RatingQueryVO queryVO) {
        // 废弃了
//        String rating = searchCustomerRating(queryVO);
//        if(!"5".equals(rating)){
//            rating = "";
//        }
        return "";

    }

    @Override
    public List<String> queryCustomerRating(List<RatingQueryVO> queryVOList) {
        try {
            long time1 = System.currentTimeMillis();
            if (ListUtils.isEmptyList(queryVOList)) {
                return new ArrayList<>();
            }
            List<CompletableFuture<String>> futureList = new ArrayList<>();
            for (RatingQueryVO vo : queryVOList) {
                futureList.add(CompletableFuture.supplyAsync(() -> queryCustomerRating(vo), executor));
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            List<String> resultList = new ArrayList<>();
            futureList.forEach(f -> resultList.add(f.join()));
            LogUtil.audit("批量CRM耗时={}", System.currentTimeMillis() - time1);
            LogUtil.audit("批量CRM出参resultList={}", JSON.toJSONString(resultList));
            return resultList;
        } catch (Exception e) {
        }
        return new ArrayList<>();
    }

    @Override
    public String sendGobal(String url, String xmlParam) {
        try {

            if (Boolean.TRUE.equals(switchMesh)) {
//                Map<String, String> headers = new HashMap<>();
//                headers.put("Content-Type", "text/xml;charset:GBK");
//                headers.put("Accept", "text/xml;charset:GBK");
                return MeshSendUtils.postOfXmlGBK(url, xmlParam);
            } else {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Content-type", "text/xml");
                httpPost.setEntity(new StringEntity(xmlParam, "GBK"));
                CloseableHttpClient client = HttpClients.createDefault();
                log.info("开始调用Gobal sendGobal发送Global入参：httpPost:{}", JSONObject.toJSON(httpPost));
                CloseableHttpResponse response = client.execute(httpPost);
                log.info("结束调用Gobal sendGobal发送Global返参：response:{}", JSONObject.toJSON(response));
                log.info("结束调用Gobal sendGobal发送Global返参：response流:{}", response.getEntity().getContent());
                InputStream inputStream = response.getEntity().getContent();
                BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "GBK"));
                StringBuilder strBuilder = new StringBuilder();
                String str = "";
                while ((str = br.readLine()) != null) {
                    strBuilder.append(str);
                }
                String responseString = strBuilder.toString();
                log.info("结束调用Gobal sendGobal发送Global解析后实际返参：responseString:{}", responseString);

                return response.getStatusLine().getStatusCode() == 200 ? responseString : null;
            }
        } catch (UnsupportedEncodingException e) {
            log.error("发送global 不支持的编码异常", e);
        } catch (ClientProtocolException e) {
            log.error("发送global 客户端协议异常", e);
        } catch (IOException e) {
            log.error("发送global IO异常", e);
        } catch (Exception e) {
            log.error("获取CRM客户号失败", e);
        }
        return null;
    }

    /**
     * 调用CDP生成客户号
     *
     * @param url
     * @param content
     * @return
     */
    @Override
    public String sendCDP(String url, String content) {
        log.info("开始调用CDP,入参内容:{}", JSONObject.toJSON(content));
        try {
            if (Boolean.TRUE.equals(switchMesh)) {
                return MeshSendUtils.post(url, content);
            } else {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Content-type", "application/json");
                httpPost.setEntity(new StringEntity(content, StandardCharsets.UTF_8));
                CloseableHttpClient client = HttpClients.createDefault();
                log.info("开始调用CDP,入参：httpPost:{}", JSONObject.toJSON(httpPost));
                CloseableHttpResponse response = client.execute(httpPost);
                log.info("结束调用CDP,返参：response:{}", JSONObject.toJSON(response));
                InputStream inputStream = response.getEntity().getContent();
                BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                StringBuilder respBuffer = new StringBuilder();
                String str = "";
                while ((str = br.readLine()) != null) {
                    respBuffer.append(str);
                }
                String responseString = respBuffer.toString();
                log.info("结束调用CDP,解析后实际返参：responseString:{}", responseString);

                return response.getStatusLine().getStatusCode() == 200 ? responseString : null;
            }
        } catch (UnsupportedEncodingException e) {
            log.error("发送CDP 不支持的编码异常", e);
        } catch (ClientProtocolException e) {
            log.error("发送CDP 客户端协议异常", e);
        } catch (IOException e) {
            log.error("发送CDP IO异常", e);
        } catch (Exception e) {
            log.error("获取客户号失败", e);
        }
        return null;
    }
}
