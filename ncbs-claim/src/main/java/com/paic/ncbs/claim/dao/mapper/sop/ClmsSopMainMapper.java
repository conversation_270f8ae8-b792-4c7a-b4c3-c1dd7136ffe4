package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import com.paic.ncbs.claim.model.vo.sop.SopHistoryMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP信息表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopMainMapper {

    /**
     * 查询SOP列表
     *
     * @param queryVO 查询条件
     * @return SOP列表
     */
    List<SopMainVO> selectSopList(@Param("queryVO") SopQueryVO queryVO);

    /**
     * 根据ID查询SOP
     *
     * @param idSopMain SOP主键
     * @return SOP信息
     */
    ClmsSopMain selectByPrimaryKey(@Param("idSopMain") String idSopMain);

    /**
     * 插入SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int insert(ClmsSopMain record);

    /**
     * 选择性插入SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopMain record);

    /**
     * 选择性更新SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ClmsSopMain record);

    /**
     * 更新SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int updateByPrimaryKey(ClmsSopMain record);

    /**
     * 根据批次号查询所有版本
     *
     * @param batchNo 批次号
     * @return SOP版本列表
     */
    List<SopHistoryMainVO> selectVersionListByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 根据ID查询SOP详情
     *
     * @param idSopMain SOP主键
     * @return SOP详情
     */
    SopMainVO selectSopDetailById(@Param("idSopMain") String idSopMain);

    /**
     * 根据ID删除SOP
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("idSopMain") String idSopMain);

    /**
     * 根据SOP名称查询数量
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int countBySopName(@Param("sopName") String sopName, @Param("excludeId") String excludeId);


    /**
     * 根据SOP名称查询SOP
     *
     * @param sopName SOP名称
     * @return SOP信息
     */
    ClmsSopMain selectBySopName(@Param("sopName") String sopName);

    /**
     * 根据批次号查询最新一条数据
     *
     * @param batchNo 批次号
     * @return SOP信息
     */
    ClmsSopMain selectLatestByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 根据批次号更新失效日期
     *
     * @param batchNo 批次号
     * @param excludeId 排除的ID
     * @param updatedBy 修改人
     * @param invalidDate 失效日期
     * @return 影响行数
     */
    int updateInvalidDateByBatchNo(@Param("batchNo") String batchNo,
                                   @Param("excludeId") String excludeId,
                                   @Param("updatedBy") String updatedBy,
                                   @Param("invalidDate") java.time.LocalDateTime invalidDate);

    /**
     * 查询险种信息列表
     *
     * @return 险种信息列表（PLAN_CODE, PLAN_CHINESE_NAME）
     */
    List<Object> selectPlanInfoList();

    /**
     * 根据案件信息匹配SOP规则（在SQL中直接查询案件信息）
     *
     * @param reportNo 报案号
     * @param caseTimes 案件次数
     *
     * @return 匹配的SOP规则列表
     */
    List<SopMainVO> selectMatchingSopRulesByCase(@Param("reportNo") String reportNo,
                                                                                   @Param("caseTimes") Integer caseTimes,
                                                                                   @Param("taskBpmKey") String taskBpmKey);

    /**
     * 查询产品与方案的关联关系
     *
     * @return 产品方案关联关系列表（Map结构包含productCode和groupCode）
     */
    List<java.util.Map<String, Object>> selectProductGroupAssociations();

    /**
     * 根据产品代码列表查询产品信息
     *
     * @param productCodes 产品代码列表
     * @return 产品信息列表（Map结构包含productCode和productName）
     */
    List<java.util.Map<String, Object>> selectProductInfoByProductCodes(@Param("productCodes") List<String> productCodes);

    /**
     * 根据方案代码列表查询方案信息
     *
     * @param groupCodes 方案代码列表
     * @return 方案信息列表（Map结构包含groupCode和groupName）
     */
    List<java.util.Map<String, Object>> selectGroupInfoByGroupCodes(@Param("groupCodes") List<String> groupCodes);

    /**
     * 根据险种代码列表查询险种信息
     *
     * @param planCodes 险种代码列表
     * @return 险种信息列表（Map结构包含planCode和planChineseName）
     */
    List<java.util.Map<String, Object>> selectPlanInfoByPlanCodes(@Param("planCodes") List<String> planCodes);

}
