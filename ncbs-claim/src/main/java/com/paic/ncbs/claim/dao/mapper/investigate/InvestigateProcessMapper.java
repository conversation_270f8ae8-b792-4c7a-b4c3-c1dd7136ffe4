package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateProcessVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateProcessDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface InvestigateProcessMapper extends BaseDao<InvestigateProcessDTO> {


    int addInvestigateProcess(InvestigateProcessDTO investigateProcess);


    int addInvestigateProcessList(@Param("list") List<InvestigateProcessDTO> investigateProcessList);


    int modifyInvestigateProcess(InvestigateProcessDTO investigateProcess);


    int deleteInvestigateProcessById(String idAhcsInvestigateProcess);


	List<InvestigateProcessVO> getInvestigateProcessByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);
	

	List<InvestigateProcessVO> getInvestigateProcessAssistByInvestigateId(
            @Param("idAhcsInvestigate") String idAhcsInvestigate);


	String getCustomerNameByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);
	

	String getReportNoByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);
	
	

}