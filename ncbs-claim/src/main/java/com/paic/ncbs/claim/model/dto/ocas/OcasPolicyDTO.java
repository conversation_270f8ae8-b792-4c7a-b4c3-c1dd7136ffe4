package com.paic.ncbs.claim.model.dto.ocas;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.constant.ConstValues;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OcasPolicyDTO {
    private String policyNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceBeginDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceEndDate;
    private BigDecimal policyAmount;
    private String currency;
    private String productCode;
    private String productName;
    private String idPlyRiskPerson;
    private String certificateNo;
    private String applicantName;
    private String insuredName;
    private String departmentCode;
    private String departmentName;
    private String status;
    private String statusName;
    private String active = ConstValues.YES;
    private boolean selected;
    private String  personnelAttribute ;
    private String mobileTelephone;
    private String certificateType;
    //渠道来源
    private String channelSourceName;
    // 方案名称
    private String riskGroupName;

    private Integer caseTimes;
    private List<OcasPlanDTO> planList;
    private List<OcasRiskGroupDTO> riskGroupList;

    /**
     * 追溯期
     */
    private Integer prosecutionPeriod;

    /**
     * 延长报告期
     */
    private Integer extendReportDate;

    /**
     * 业绩归属机构代码
     */
    private String performanceAttributionCode;

    /**
     * 业绩归属机构名称
     */
    private String performanceAttributionName;

    public String getPerformanceAttributionCode() {
        return performanceAttributionCode;
    }

    public void setPerformanceAttributionCode(String performanceAttributionCode) {
        this.performanceAttributionCode = performanceAttributionCode;
    }

    public String getPerformanceAttributionName() {
        return performanceAttributionName;
    }

    public void setPerformanceAttributionName(String performanceAttributionName) {
        this.performanceAttributionName = performanceAttributionName;
    }

    public OcasPolicyDTO() {
    }

    public OcasPolicyDTO(String policyNo, OcasPolicyPlanDutyDTO policy, List<OcasPlanDTO> planList) {
        this.policyNo = policyNo;
        this.insuranceBeginDate = policy.getInsuranceBeginDate();
        this.insuranceEndDate = policy.getInsuranceEndDate();
        this.policyAmount = policy.getPolicyAmount();
        this.productName = policy.getProductName();
        this.applicantName = policy.getApplicantName();
        this.insuredName = policy.getInsuredName();
        this.currency = policy.getCurrency();
        this.idPlyRiskPerson = policy.getIdPlyRiskPerson();
        this.planList = planList;
        this.riskGroupName = policy.getRiskGroupName();
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public Date getInsuranceBeginDate() {
        return insuranceBeginDate;
    }

    public void setInsuranceBeginDate(Date insuranceBeginDate) {
        this.insuranceBeginDate = insuranceBeginDate;
    }

    public Date getInsuranceEndDate() {
        return insuranceEndDate;
    }

    public void setInsuranceEndDate(Date insuranceEndDate) {
        this.insuranceEndDate = insuranceEndDate;
    }

    public BigDecimal getPolicyAmount() {
        return policyAmount;
    }

    public void setPolicyAmount(BigDecimal policyAmount) {
        this.policyAmount = policyAmount;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getIdPlyRiskPerson() {
        return idPlyRiskPerson;
    }

    public void setIdPlyRiskPerson(String idPlyRiskPerson) {
        this.idPlyRiskPerson = idPlyRiskPerson;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public List<OcasPlanDTO> getPlanList() {
        return planList;
    }

    public void setPlanList(List<OcasPlanDTO> planList) {
        this.planList = planList;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getPersonnelAttribute() {
        return personnelAttribute;
    }

    public void setPersonnelAttribute(String personnelAttribute) {
        this.personnelAttribute = personnelAttribute;
    }


    public String getMobileTelephone() {
        return mobileTelephone;
    }

    public void setMobileTelephone(String mobileTelephone) {
        this.mobileTelephone = mobileTelephone;
    }

    public String getChannelSourceName() {
        return channelSourceName;
    }

    public void setChannelSourceName(String channelSourceName) {
        this.channelSourceName = channelSourceName;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public List<OcasRiskGroupDTO> getRiskGroupList() {
        return riskGroupList;
    }

    public void setRiskGroupList(List<OcasRiskGroupDTO> riskGroupList) {
        this.riskGroupList = riskGroupList;
    }

    public Integer getProsecutionPeriod() {
        return prosecutionPeriod;
    }

    public void setProsecutionPeriod(Integer prosecutionPeriod) {
        this.prosecutionPeriod = prosecutionPeriod;
    }

    public Integer getExtendReportDate() {
        return extendReportDate;
    }

    public void setExtendReportDate(Integer extendReportDate) {
        this.extendReportDate = extendReportDate;
    }
}
