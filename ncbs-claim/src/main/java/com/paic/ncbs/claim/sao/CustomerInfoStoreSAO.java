package com.paic.ncbs.claim.sao;

import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.vo.policy.OCASPolicyVO;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CustomerInfoStoreSAO {

    Set<String> getPolicyByCertificationNo(String certificationNo);

    @Deprecated
    List<AhcsPolicyDomainDTO> getPolicyDomainInfo(Map<String,String> param);

    @Deprecated
    AhcsPolicyDomainDTO getPolicyDomainInfoByPolicyNo(Map<String, String> param);

    List<ReportCustomerInfoVO> getApplicantInfoList(Map<String, String> param);

    List<ReportCustomerInfoVO> getInsuredList(Map<String,String> param);

    List<OCASPolicyVO> getPolicyListByCertificationNo(String certificationNo);

    AhcsPolicyDomainDTO getPolicyDomainInfo(CopyPolicyQueryVO queryVO);

    String searchCustomerRating(RatingQueryVO queryVO);

    String queryCustomerRating(RatingQueryVO queryVO);

    List<String> queryCustomerRating(List<RatingQueryVO> queryVOList);

    /**
     * 调用global生成客户号
     * @param url 地址
     * @param xmlParam 请求报文
     * @return
     */
    String sendGobal(String url, String xmlParam);


    /**
     * 调用CDP生成客户号
     * @param url
     * @param content
     * @return
     */
    String sendCDP(String url, String content);
}
