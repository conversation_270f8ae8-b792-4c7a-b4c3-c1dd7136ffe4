package com.paic.ncbs.claim.controller.doc;

import cn.hutool.core.io.FileUtil;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.FileUploadConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.fileupload.DocumentTypeService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Api(tags = "上传单证文件")
@Controller
@RefreshScope
@RequestMapping("/doc/fileUploadController")
public class FileUploadController extends BaseController {

    @Value("${iobs.enable}")
    private boolean iobs;

    @Autowired
    DocumentTypeService documentTypeService;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;

    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    private static final List<String> DOCUMENT_FILE_TYPE_LIST = new ArrayList<>();

    static {
        DOCUMENT_FILE_TYPE_LIST.add("jpg");
        DOCUMENT_FILE_TYPE_LIST.add("jpeg");
        DOCUMENT_FILE_TYPE_LIST.add("png");
        DOCUMENT_FILE_TYPE_LIST.add("bmp");
        DOCUMENT_FILE_TYPE_LIST.add("pdf");
        DOCUMENT_FILE_TYPE_LIST.add("mp3");
        DOCUMENT_FILE_TYPE_LIST.add("wma");
        DOCUMENT_FILE_TYPE_LIST.add("wav");
        DOCUMENT_FILE_TYPE_LIST.add("gif");

        DOCUMENT_FILE_TYPE_LIST.add("mp4");
        DOCUMENT_FILE_TYPE_LIST.add("avi");
        DOCUMENT_FILE_TYPE_LIST.add("mov");
        DOCUMENT_FILE_TYPE_LIST.add("wmv");
        DOCUMENT_FILE_TYPE_LIST.add("mkv");
//        DOCUMENT_FILE_TYPE_LIST.add("flv");
        DOCUMENT_FILE_TYPE_LIST.add("webm");
        DOCUMENT_FILE_TYPE_LIST.add("midi");

        DOCUMENT_FILE_TYPE_LIST.add("zip");
//        DOCUMENT_FILE_TYPE_LIST.add("rar");
//        DOCUMENT_FILE_TYPE_LIST.add("7z");
        DOCUMENT_FILE_TYPE_LIST.add("tar");
        DOCUMENT_FILE_TYPE_LIST.add("gz");

        DOCUMENT_FILE_TYPE_LIST.add("xls");
        DOCUMENT_FILE_TYPE_LIST.add("xlsx");
        DOCUMENT_FILE_TYPE_LIST.add("eml");

        DOCUMENT_FILE_TYPE_LIST.add("tiff");
    }

    @ApiOperation("上传单证")
    @PostMapping(value = "/uploadDocument")
    @ResponseBody
    public ResponseResult uploadDocument(FileInfoDTO fileInfoDTO, HttpServletRequest request) throws GlobalBusinessException, UnsupportedEncodingException {
        LogUtil.audit("#上传单证#入参：reportNo=" + fileInfoDTO.getReportNo() + ",caseTimes=" + fileInfoDTO.getCaseTimes());
        String returnCode = FileUploadConstants.FAIL_CODE;
        UserInfoDTO userDTO = WebServletContext.getUser();
        //校验入参
        String returnMsg = this.validUploadDocumentParams(fileInfoDTO, userDTO);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(returnCode, returnMsg);
        }

        String flowType = StringUtils.getFlowTypeByCaseTimes(fileInfoDTO.getCaseTimes());
        String userId = userDTO.getUserCode();
        String userName = userDTO.getUserName();
        String uploadPersonnel = userName + "-" + userId;
        if (uploadPersonnel.getBytes("GBK").length > 50) {
            uploadPersonnel = userId;
        }
        fileInfoDTO.setUploadPersonnel(uploadPersonnel);
        fileInfoDTO.setCreatedBy(userId);
        fileInfoDTO.setUpdatedBy(userId);
        fileInfoDTO.setFlowType(flowType);
        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            this.getFileInfo(multipartRequest, fileInfoDTO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(returnCode, "获取单证文件内容字节数组异常");
        }

        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
        //校验文件格式和大小
        returnMsg = this.validDocumentFile(fileInfoDTO);
        if (StringUtils.isNotEmpty(returnMsg)) {
            return ResponseResult.fail(returnCode, returnMsg);
        }

        try {
            String seq = fileUploadService.getSeq();
            fileInfoDTO.setRecordNo(seq);
            //神兵stg环境，文件需上传至IOBS
            String fileId = iobs ? this.saveFilePlatform(fileInfoDTO) : this.saveLocalTool(fileInfoDTO);
            LogUtil.audit("单证上传到文档服务器返回的 fileId=" + fileId);
            fileInfoDTO.setFileId(fileId);
            if (StringUtils.isEmptyStr(fileId)) {
                LogUtil.audit("fileId为空，单证上传到文档服务器返回的 fileId=" + fileId);
                returnMsg = "解析文件并保存至文档服务器异常";
                return ResponseResult.fail(returnCode, returnMsg);
            }
        } catch (Exception e) {
            LogUtil.error("上传单证异常", e);
            return ResponseResult.fail(returnCode, "上传单证异常");
        }
        return this.addFilesInfo(fileInfoDTO,userName);
    }

    public String saveFilePlatform(FileInfoDTO fileInfoDTO){
        String name = fileInfoDTO.getReportNo() + getSeek(fileInfoDTO) + fileInfoDTO.getFileFormat();
        return iobsFileUploadService.uploadFileToFilePlatform(name, fileInfoDTO.getFileContentBytes());
    }

    public String saveLocalTool(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        String name = fileInfoDTO.getReportNo()+getSeek(fileInfoDTO)+fileInfoDTO.getFileFormat();
        LogUtil.audit("saveLocal path={}",FileUploadConstants.LOCAL_PATH);
        String path = FileUploadConstants.LOCAL_PATH+name;
        FileUtil.writeBytes(fileInfoDTO.getFileContentBytes(),path);
        return FileUploadConstants.LOCAL_HOST +name;
    }


    private String getSeek(FileInfoDTO dto){
        return "_" + dto.getRecordNo() + ".";
    }


    private  FileInfoDTO getFileInfo(MultipartHttpServletRequest multipartRequest, FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        try {
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
            for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                MultipartFile file = entry.getValue();
                String contentType = file.getContentType();
                LogUtil.audit("contentType------{}", contentType);
                String fileInputName = entry.getKey();
                String fileName = file.getOriginalFilename();
                String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
                fileInfoDTO.setFileName(fileName);
                fileInfoDTO.setFileFormat(fileFormat);
                fileInfoDTO.setFileInputName(fileInputName);
                fileInfoDTO.setFileSize(file.getSize());
                fileInfoDTO.setFileContentBytes(file.getBytes());
                fileInfoDTO.setContentType(contentType);
            }
        } catch (IOException e) {
            LogUtil.error("获取文件内容字节数组异常", e);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_FILE_UPLOAD_CONTENT_BYTES, e);
        }
        return fileInfoDTO;
    }

    /**
     * 参数校验
     * @param fileInfoDTO
     * @param userDTO
     * @return
     */
    private String validUploadDocumentParams(FileInfoDTO fileInfoDTO, UserInfoDTO userDTO) {
        String returnMsg = "";
        String taskCode = fileInfoDTO.getTaskCode();
        if (StringUtils.isEmptyStr(fileInfoDTO.getReportNo())) {
            returnMsg = "报案号不能为空!";
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getCaseTimes())) {
            returnMsg = "赔付次数不能为空!";
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getSmallCode())) {
            returnMsg = "细类代码不能为空!";
        } else if (StringUtils.isEmptyStr(taskCode)) {
            returnMsg = "环节号不能为空!";
        } else if (userDTO == null || StringUtils.isEmptyStr(userDTO.getUserCode()) || StringUtils.isEmptyStr(userDTO.getUserName())) {
            returnMsg = "用户信息不能为空!";
        }
        return returnMsg;
    }

    private ResponseResult<FileInfoDTO> addFilesInfo(FileInfoDTO fileInfoDTO, String userName) throws GlobalBusinessException {
        FileInfoDTO fileInfo = this.addFiles(fileInfoDTO,userName);
        LogUtil.audit("单证上传成功 documentGroupId=" + fileInfo.getDocumentGroupId());
        fileInfo.setFileId(fileInfoDTO.getFileId());
        ReportInfoExEntity reportInfoExEntity = reportInfoExMapper.getAcceptanceNoByReportNo(fileInfoDTO.getReportNo());
        //通知TPA,只有谐筑半包的案件发送MQ消息
        if(Objects.equals(BpmConstants.REPORT_TRACK,fileInfoDTO.getTaskCode()) && reportInfoExEntity != null
                && Objects.equals("2",reportInfoExEntity.getClaimDealWay())  &&  Objects.equals("42300010",reportInfoExEntity.getCompanyId()) ){
            claimSendTpaMqInfoService.sendTpaMq(fileInfoDTO.getReportNo(),fileInfoDTO.getCaseTimes(),"upload");
        }
        return ResponseResult.success(fileInfo);
    }

    private FileInfoDTO addFiles(FileInfoDTO fileInfoDTO, String userName) throws GlobalBusinessException{
        this.validAddFileInfoParams(fileInfoDTO);
        fileInfoDTO.setUploadSource(ConstValues.UPLOAD_BY_PC);
        String userId = fileInfoDTO.getCreatedBy();
        fileInfoDTO.setUploadPersonnel(userName + FileUploadConstants.DOCUMENT_UPLOADPERSONNEL_SEPARATOR + userId);
        fileInfoDTO.setSupplement("N");
        String documentGroupId = null;
        try {
            String uploadPersonnel = userName + "-" + userId;
            if (uploadPersonnel.getBytes("GBK").length > 50) {
                uploadPersonnel = userId;
                fileInfoDTO.setUploadPersonnel(uploadPersonnel);
            }
            documentGroupId = fileUploadService.addFilesInfo(fileInfoDTO);
        } catch (Exception e) {
            LogUtil.error("上传文件出现异常FileUploadAction.addFilesInfo", e);
            throw new GlobalBusinessException(ErrorCode.FileUpload.ERROR_FILE_UPLOAD_FILE_FAIL, e);
        }
        LogUtil.audit("保存文件成功");
        FileInfoDTO fileInfo = new FileInfoDTO();
        fileInfo.setBatchNo(Long.toString(System.currentTimeMillis()));
        fileInfo.setDocumentGroupId(documentGroupId);
        return fileInfo;
    }

    /**
     * 文件内容校验
     * @param fileInfoDTO
     * @throws GlobalBusinessException
     */
    private void validAddFileInfoParams(FileInfoDTO fileInfoDTO) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(fileInfoDTO.getSmallCode())) {
            LogUtil.audit("参数smallCode(细类代码)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "细类代码");
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getFlowType())) {
            LogUtil.audit("参数flowType(流程类型)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "流程类型");
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getTaskCode())) {
            LogUtil.audit("参数taskCode(环节号)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "环节号");
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getDocumentGroupId())) {
            LogUtil.audit("参数documentGroupId(文件组ID)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "文件组ID");
        } else if (StringUtils.isEmptyStr(fileInfoDTO.getFirstUpload())) {
            LogUtil.audit("参数firstUpload(首传标志)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "首传标志");
        }
    }

    private String validDocumentFile(FileInfoDTO fileInfoDTO) {
        String returnMsg = "";
        String fileName = fileInfoDTO.getFileName();
        long fileSize = fileInfoDTO.getFileSize();
        if (StringUtils.isEmptyStr(fileName)) {
            LogUtil.audit("上传的单证文件名不能为空! fileName=" + fileName);
            returnMsg = "上传的单证文件名不能为空!";
        }
        try {
            if (fileName.getBytes("UTF-8").length > 200) {
                LogUtil.audit("文件名称过长");
                returnMsg = "文件名称过长!";
            }
        } catch (UnsupportedEncodingException e) {
            LogUtil.error("文件名称转UTF-8格式失败 ", e);
            returnMsg = "文件名称转UTF-8格式失败 !";
        }
        String fileFormat = fileInfoDTO.getFileFormat().toLowerCase();
        if (!DOCUMENT_FILE_TYPE_LIST.contains(fileFormat)) {
            LogUtil.audit("上传的单证文件格式不正确! fileType=" + fileFormat);
            returnMsg = "上传的单证文件格式不正确!";
        } else if (fileSize > FileUploadConstants.DOCUMENT_FILESIZE_LIMIT) {
            LogUtil.audit("上传单证文件大小超过40M! fileSzie=" + fileSize);
            returnMsg = "上传单证文件大小超过40M!";
        }

        return returnMsg;
    }

}

