package com.paic.ncbs.claim.model.dto.fee;


import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("费用信息")
public class FeeInfoDTO extends EntityDTO {
	private static final long serialVersionUID = -8479509270253045508L;

	private String idAhcsFeePay;
	@ApiModelProperty("报案号")
	private String reportNo;
	@ApiModelProperty("赔付次数")
	private Integer caseTimes;
	@ApiModelProperty("赔案号")
	private String caseNo;
	@ApiModelProperty("保单号")
	private String policyNo;
	@ApiModelProperty("电子保单号")
    private String policyCerNo;
	@ApiModelProperty("赔付类型")
	private String claimType;
    @ApiModelProperty("保单预付金额")
   	private BigDecimal preFeeAmount;
	@ApiModelProperty("费用金额")
	private BigDecimal feeAmount;
	@ApiModelProperty("费用类型")
	private String feeType;
	@ApiModelProperty("支付信息id")
	private String idClmPaymentInfo;
	@ApiModelProperty("支付项目id")
	private String idClmPaymentItem;
	private String clientName;
	@ApiModelProperty("发票号")
	private String invoiceNo;
	@ApiModelProperty("发票信息")
	private InvoiceInfoDTO invoiceInfo;

	private String isCanDel;

	private String isEffective;
	
	private Integer subTimes;
	
	private String lossObjectNo;

	
}
