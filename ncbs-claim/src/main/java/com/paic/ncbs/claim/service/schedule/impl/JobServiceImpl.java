package com.paic.ncbs.claim.service.schedule.impl;

import com.paic.ncbs.claim.common.constant.RedisKeyConstants;
import com.paic.ncbs.claim.common.enums.SmsTypeEnum;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.schedule.JobService;
import com.paic.ncbs.claim.service.supplements.SupplementsMaterialService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

/**
 * 定时任务服务
 */
@Service
@RefreshScope
public class JobServiceImpl implements JobService {
    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private SupplementsMaterialService supplementsMaterialService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Autowired
    private RedissonClient redissonClient;

    @Value("${batch.supplements.firstNotifyDays:5}")
    private Integer firstNotifyDays;
    @Value("${batch.supplements.againNotifyDays:10}")
    private Integer againNotifyDays;
    @Value("${ncbs.pay.paymentBackURL:http://ncbs-claim.lb.ssdev.com:48915/claim}")
    private String paymentBackURL;

    /**
     * 补充材料任务通知
     * 定时任务定义为第天早上9点执行
     */
    @Override
    public void supplementsMaterialTaskNotification() {
        LogUtil.info("定时任务补充材料任务通知开始执行：");
        // 先执行补充材料任务 5天通知
        List<SupplementsMaterialDTO> smsNotificationTask =
                supplementsMaterialService.getSmsNotificationTask(SmsTypeEnum.CUSTOMER_SUPPLEMENTS_FISRT.getType(), firstNotifyDays);
        LogUtil.info("定时任务补充材料任务5日数量：{}", smsNotificationTask.size());
        for (SupplementsMaterialDTO materialDTO : smsNotificationTask) {
            smsInfoService.sendBusinessSms(SmsTypeEnum.CUSTOMER_SUPPLEMENTS_FISRT, materialDTO.getReportNo(),
                    materialDTO.getCaseTimes(),
                    materialDTO.getId());
        }

        // 再执行补充材料伤 10天通知
//        List<SupplementsMaterialDTO> smsNotificationTaskAgain =
//                supplementsMaterialService.getSmsNotificationTask(SmsTypeEnum.CUSTOMER_SUPPLEMENTS_AGAIN.getType(), againNotifyDays);
//        LogUtil.info("定时任务补充材料任务10日数量：{}", smsNotificationTaskAgain.size());
//        for (SupplementsMaterialDTO materialDTO : smsNotificationTaskAgain) {
//            smsInfoService.sendBusinessSms(SmsTypeEnum.CUSTOMER_SUPPLEMENTS_AGAIN, materialDTO.getReportNo(),
//                    materialDTO.getCaseTimes(),
//                    materialDTO.getId());
//        }
//        LogUtil.info("定时任务补充材料任务通知结束执行：");
    }

    /**
     * 合并支付
     *
     */
    @Override
    @Transactional
    public void mergePayment(Date executeDate) {
        LogUtil.info("合并支付开始执行：");
        String key = RedisKeyConstants.MERGE_PAY_LOCK;
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock()) {
                Date yesterday = new Date();
                if(executeDate == null){
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_YEAR,-1);
                    yesterday = calendar.getTime();
                } else {
                    yesterday = executeDate;
                }
                //查询前一天的账户信息
                List<PaymentItemDTO> paymentItemList = paymentItemMapper.getMergePayAccountList(yesterday);
                //根据账户信息查询主信息
                for(PaymentItemDTO paymentItemDTO: paymentItemList){
                    BatchPaymentInfo batchPaymentInfo = new BatchPaymentInfo();
                    String productCode = paymentItemDTO.getProductCode();
                    String clientBankAccount = paymentItemDTO.getClientBankAccount();
                    //明细
                    List<BatchPaymentDetailInfo> batchPaymentDetailInfo =
                            paymentItemMapper.getMergePaymentList(productCode,clientBankAccount,yesterday);
                    for(BatchPaymentDetailInfo info: batchPaymentDetailInfo){
                        info.setCurrency("CNY");
                        info.setTaxFee(BigDecimal.ZERO);
                    }
                    //主信息
                    BatchPaymentMainInfo batchPaymentMainInfo =
                            paymentItemMapper.getPaymentMainInfo(productCode,clientBankAccount,yesterday);
                    String batchNo = generateBatchNumber();
                    batchPaymentMainInfo.setBatchNo(batchNo);
                    batchPaymentMainInfo.setBatchType("3");
                    batchPaymentMainInfo.setSumCount(batchPaymentDetailInfo.size());
                    batchPaymentMainInfo.setIsExistInvoice("0");
                    batchPaymentMainInfo.setPayStatusReturnURL(paymentBackURL + "/public/pay/mergePaymentBackResult");

                    batchPaymentInfo.setBatchPaymentMainInfo(batchPaymentMainInfo);
                    batchPaymentInfo.setBatchPaymentDetailInfo(batchPaymentDetailInfo);
                    //将主信息存数据库
                    MergePaymentDTO mergePaymentDTO = new MergePaymentDTO();
                    bulidMergePaymentDTO(batchPaymentMainInfo,mergePaymentDTO);
                    paymentItemMapper.addClmsMergePayment(mergePaymentDTO);
                    int id = mergePaymentDTO.getId();
                    //更新clm_payment_item表
                    paymentItemMapper.updatePaymentItemId(id,batchPaymentDetailInfo);
                    //送收付
                    if(batchPaymentMainInfo != null){
                        payInfoNoticeThirdPartyCoreSAO.sendMergePayment(batchNo,batchPaymentInfo,batchPaymentDetailInfo);
                    }
                }
                LogUtil.info("合并支付结束执行：");
            } else {
                throw new GlobalBusinessException("合并支付正在处理中！");
            }
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public static String generateBatchNumber() {
        // 获取当前时间并格式化为yyyyMMddHHmmss
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timePart = sdf.format(new Date());

        // 生成6位随机数
        Random random = new Random();
        int randomNum = random.nextInt(900000) + 100000; // 保证是6位数

        return timePart + randomNum;
    }

    private void bulidMergePaymentDTO(BatchPaymentMainInfo batchPaymentMainInfo,MergePaymentDTO mergePaymentDTO) {
        BeanUtils.copyProperties(batchPaymentMainInfo,mergePaymentDTO);
        mergePaymentDTO.setCreatedBy("system");
        mergePaymentDTO.setUpdatedBy("system");
        mergePaymentDTO.setMergePaymentStatus("10");//草稿
    }
}
