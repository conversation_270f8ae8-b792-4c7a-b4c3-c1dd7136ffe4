package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.replevy.dto.EstimateDutyRecordQueryDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface EstimateDutyRecordMapper extends BaseDao<EstimateDutyRecordDTO> {

    public void addEstimateDutyRecordList(@Param("paramList") List<EstimateDutyRecordDTO> paramList);

    public void addDutyRecordList(@Param("paramList") List<EstimateDutyRecordDTO> paramList);

    public List<EstimateDutyRecordDTO> getEstimateDutyRecordList(
            @Param("planCode") String planCode,
            @Param("caseTimes") Integer caseTimes,
            @Param("caseNo") String caseNo);

    public List<EstimateDutyRecordDTO> getDutyRecordListByApplyId(@Param("applyId") String applyId);

    public void modifyBatchEstimateDutyRecord(EstimateDutyRecordDTO dto);

    public EstimateDutyRecordDTO getDutyRecordDTO(@Param("policyNo") String policyNo, @Param("caseTimes") Integer caseTimes,
                                                  @Param("caseNo") String caseNo, @Param("planCode") String planCode, @Param("dutyCode") String dutyCode);

//	public void deleteEstimateDataList(@Param("paramList")List<String> paramList);

    void updateEffectiveByIds(@Param("paramList") List<String> paramList, @Param("updatedBy") String updatedBy);

    public List<EstimateDutyRecordDTO> getRecordsOfRegistCase(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes,
                                                              @Param("estimateType") String estimateType);

    public List<EstimateDutyRecordDTO> getEstimateEutyList(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes,
                                                           @Param("estimateType") String estimateType);

    public List<EstimateDutyRecordDTO> getEstimatePolicyPlanList(@Param("idAhcsPolicyInfo") String idAhcsPolicyInfo, @Param("caseTimes") Integer caseTimes,
                                                                 @Param("caseNo") String caseNo);

    void deleteEstimateRecordList(@Param("caseNoList") List<String> caseNoList, @Param("caseTimes") Integer caseTimes);

    void updateEffectiveByCaseNos(@Param("caseNoList") List<String> caseNoList, @Param("caseTimes") Integer caseTimes, @Param("updatedBy") String updatedBy);

    void delEstimateRecordDataByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

    public List<String> getEstimateTypeByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public List<String> getRegisterAmountCaseNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                @Param("estimateType") String estimateType);

    List<EstimateDutyRecordDTO> getDutyRecordInfoByApplyId(@Param("applyId") String applyId);

    public List<EstimateDutyRecordDTO> getRecordsOfRegisterCaseByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                                          @Param("estimateType") String estimateType);

    public List<EstimateDutyRecordDTO> getTotalRegistAmount(@Param("policyNo") String policyNo, @Param("caseNos") List<String> caseNos );

    List<EstimateDutyRecordDTO> getTotalRegistAmountForRiskProperty(String policyNo);

    public List<EstimateDutyRecordDTO> getEstimateDutyRecordForRestartList(
            @Param("planCode") String planCode,
            @Param("caseTimes") Integer caseTimes,
            @Param("caseNo") String caseNo);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    void updateEffectiveByCaseNo(@Param("caseNoList") List<String> caseNoList,@Param("caseTimes") Integer caseTimes);

    List<EstimateDutyRecordDTO> getTotalRegistAmountByIdRiskProperty(@Param("idPlyRiskProperty") String idPlyRiskProperty,@Param("insuredCode") String insuredCode);

    List<EstimateDutyRecordDTO> getPolicyAndCaseNoByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void batchUpdateEstimateDutyRecord(@Param("paramList") List<EstimateDutyRecordDTO> paramList);

    public EstimateDutyRecordDTO getCaseNoByReportNo(@Param("reportNo") String reportNo);

    void updateEstimateDateByCaseNo(@Param("caseNo") String caseNo);

    /**
     * 获取赔款立案金额
     * @param reportNo
     * @param caseTimes
     * @param caseTimes
     * @return
     */
    public BigDecimal getestimateAmount(@Param("reportNo") String reportNo, @Param("dutyCode") String dutyCode,
                                        @Param("caseTimes") Integer caseTimes);
}