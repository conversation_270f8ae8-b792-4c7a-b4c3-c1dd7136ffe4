package com.paic.ncbs.claim.service.sop;

import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.dto.sop.SopFileDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.sop.SopHistoryMainVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * SOP管理Service接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface SopMainService{

    /**
     * 分页查询SOP列表
     *
     * @param queryVO 查询条件
     * @return 分页结果
     */
    List<SopMainVO> getSopList(SopQueryVO queryVO);

    /**
     * 根据ID获取SOP详情
     *
     * @param idSopMain SOP主键
     * @return SOP详情
     */
    SopMainVO getSopDetail(String idSopMain);

    /**
     * 保存或更新SOP（支持暂存、发布、停用）
     *
     * @param sopMainDTO SOP信息
     * @param request HTTP请求（用于获取文件）
     * @return 操作结果
     */
    String saveOrUpdateSop(SopMainDTO sopMainDTO);

    /**
     * 校验SOP名称是否重复
     *
     * @param sopName SOP名称
     * @return 是否重复
     */
    boolean checkSopNameExists(String sopName);

    /**
     * 获取险种信息列表
     *
     * @return 险种信息列表（PLAN_CODE, PLAN_CHINESE_NAME）
     */
    List<Object> getPlanInfoList();

    /**
     * 根据案件信息匹配SOP规则（在SQL中直接查询案件信息）
     *
     * @param reportNo 报案号
     * @param caseTimes 案件次数
     * @param taskDefinitionBpmKey 环节代码
     * @return 匹配的SOP规则列表
     */
    List<SopMainVO> matchSopRulesByCase(String reportNo, Integer caseTimes, String taskDefinitionBpmKey);
    
    /**
     * 获取环节下拉框数据
     * 
     * @return 环节下拉框列表，包含taskBpmKey和taskBpmKeyName
     */
    List<Map<String, String>> getTaskDropdownList();


    /**
     * 根据机构号和岗位权限查询人员信息列表
     *
     * @return 人员信息列表，包含userCode、userName和department信息
     */
    List<Map<String, String>> getUserInfoBySOP();

    /**
     * 批量上传文件
     *
     * @param request HTTP请求（用于获取多个文件）
     * @return 文件信息列表，包含fileId、fileName、fileFormat
     */
    List<SopFileDTO> batchUploadFiles(HttpServletRequest request);

}