package com.paic.ncbs.claim.service.riskppt.impl;

import cn.hutool.core.text.StrBuilder;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyPayMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.service.common.ResidueAmountService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("riskPropertyPayService")
public class RiskPropertyPayServiceImpl implements RiskPropertyPayService {

    @Autowired
    private RiskPropertyPayMapper riskPropertyPayMapper;
    @Autowired
    private RiskPropertyMapper riskPropertyMapper;
    @Autowired
    private AhcsPolicyInfoMapper policyInfoMapper;

    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private ResidueAmountService residueAmountService;

    @Value("${switch.riskProperty:false}")
    private boolean switchRiskProperty;

    @Override
    public List<RiskPropertyPayDTO> getRiskPropertyPayList(RiskPropertyPayDTO riskPropertyPayDTO) {
        if(!switchRiskProperty){
            return new ArrayList<>();
        }
        return riskPropertyPayMapper.getRiskPropertyPayList(riskPropertyPayDTO);
    }

    @Override
    @Transactional
    public void saveRiskPropertyPayList(List<RiskPropertyPayDTO> riskPropertyPayList) {
        if(ListUtils.isEmptyList(riskPropertyPayList) || StringUtils.isEmptyStr(riskPropertyPayList.get(0))){
            return;
        }

        String reportNo = riskPropertyPayList.get(0).getReportNo();
        String updatedBy = riskPropertyPayList.get(0).getUpdatedBy();
        riskPropertyPayMapper.removeRiskPropertyPay(new RiskPropertyPayDTO(reportNo,updatedBy));
        riskPropertyPayMapper.saveRiskPropertyPayList(riskPropertyPayList);
    }

    @Override
    public void saveRiskPropertyPay(WholeCaseBaseDTO wholeCaseBaseDTO) {
        if(!switchRiskProperty){
            LogUtil.info("switchRiskProperty:关闭");
            return;
        }

        String conclusion = wholeCaseBaseDTO.getIndemnityConclusion();
        if(conclusion == null || !conclusion.startsWith("1")){
            LogUtil.info("案件结论:"+conclusion);
            return;
        }

        String reportNo = wholeCaseBaseDTO.getReportNo();
        Integer caseTimes = wholeCaseBaseDTO.getCaseTimes();

        List<RiskPropertyPayDTO> riskPayList = riskPropertyPayMapper.getDutyDetailPayedList(reportNo,caseTimes);
        if(ListUtils.isEmptyList(riskPayList)){
            LogUtil.info("无定损标的:"+reportNo);
            return;
        }

        String userId = wholeCaseBaseDTO.getUpdatedBy();
        for (RiskPropertyPayDTO riskPay : riskPayList) {
            riskPay.setIdRiskPropertyPay(UuidUtil.getUUID());
            riskPay.setReportNo(reportNo);
            riskPay.setCreatedBy(userId);
            riskPay.setUpdatedBy(userId);
            riskPay.setClaimType(BaseConstant.STRING_1);
        }

        saveRiskPropertyPayList(riskPayList);
    }

    @Override
    public void initRiskPropertyMaxPay(List<PolicyPayDTO> policyPays) {
        if(!switchRiskProperty){
            LogUtil.info("switchRiskProperty:关闭");
            return;
        }
        if (ListUtils.isEmptyList(policyPays)) {
            return;
        }
        String reportNo = policyPays.get(0).getReportNo();

        for (PolicyPayDTO policy: policyPays) {
            List<RiskGroupDTO> riskGroupList = policy.getRiskGroupList();
            if(ListUtils.isEmptyList(riskGroupList)){
                continue;
            }
            String policyNo = policy.getPolicyNo();

            for (RiskGroupDTO group : riskGroupList) {
                List<CaseRiskPropertyDTO> riskList = group.getRiskPropertyInfoList();
                //已赔付
                List<RiskPropertyPayDTO> riskPayList = getRiskPayedList(policyNo,reportNo,riskList);
                //key=标的+险种+责任+责任明细
                Map<String, BigDecimal> detailHisPayMap = getDetailRiskHisPayMap(riskPayList);
                //key=标的+险种+责任
                Map<String, BigDecimal> dutyHisPayMap = getDutyRiskHisPayMap(riskPayList);
                for (CaseRiskPropertyDTO riskDTO : riskList) {
                    for (PlanPayDTO plan : riskDTO.getPlanPayArr()) {
                        if(ListUtils.isEmptyList(plan.getDutyPayArr())){
                            continue;
                        }
                        for (DutyPayDTO duty : plan.getDutyPayArr()) {
                            BigDecimal dutyHisPay = null;
                            if(ListUtils.isEmptyList(duty.getDutyDetailPayArr())){
                                dutyHisPay = dutyHisPayMap.get(riskDTO.getIdPlyRiskProperty()+plan.getPlanCode()+duty.getDutyCode());
                            }else{
                                dutyHisPay = BigDecimal.ZERO;
                                for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                                    BigDecimal detailHisPay = detailHisPayMap.get(riskDTO.getIdPlyRiskProperty()
                                            +plan.getPlanCode()+duty.getDutyCode()+detail.getDutyDetailCode());
                                    detail.setMaxAmountPay(calculateMaxPay(detail.getBaseAmountPay(),detailHisPay));
                                    if(!BigDecimalUtils.isGreaterZero(detailHisPay)){
                                        detailHisPay = BigDecimal.ZERO;
                                    }
                                    dutyHisPay = dutyHisPay.add(detailHisPay);
                                }
                            }
                            duty.setMaxAmountPay(calculateMaxPay(duty.getBaseAmountPay(),dutyHisPay));
                            duty.setPayLimit(getDutyLimitPay(duty.getAttributes(),dutyHisPay));
                        }
                        computeDutyShareMaxPay(plan,dutyHisPayMap);
                    }
                }
            }
            policy.setRiskGroupList(sortRiskGroup(riskGroupList));
        }
        if(policyPays.get(0).isRollback()){
            riskPropertyService.getRiskPropertyPlan(policyPays);
        }
    }

    Function<RiskPropertyPayDTO,String> planDutyDetailKey = dto -> {
            String key = new StrBuilder().append(dto.getIdPlyRiskProperty())
                    .append(dto.getPlanCode()).append(dto.getDutyCode()).append(dto.getDutyDetailCode()).toString();
            return key;
    };

    Function<RiskPropertyPayDTO,String> planDutyKey = dto -> {
        String key = new StrBuilder().append(dto.getIdPlyRiskProperty())
                .append(dto.getPlanCode()).append(dto.getDutyCode()).toString();
        return key;
    };

    private Map<String, BigDecimal> getDetailRiskHisPayMap(List<RiskPropertyPayDTO> riskPropertyPayList){
        Map<String, BigDecimal> detailHisPayMap = new HashMap<>();
        if(ListUtils.isNotEmpty(riskPropertyPayList)){
            //key=方案-标的-险种-责任-责任明细,value=金额
            detailHisPayMap = riskPropertyPayList.stream().collect(Collectors.groupingBy(planDutyDetailKey,
                    Collectors.collectingAndThen(Collectors.mapping(RiskPropertyPayDTO::getDutyDetailPay,
                            Collectors.reducing(BigDecimal::add)),Optional::get)));
        }
        return detailHisPayMap;
    }

    private Map<String, BigDecimal> getDutyRiskHisPayMap(List<RiskPropertyPayDTO> riskPropertyPayList){
        Map<String, BigDecimal> dutyHisPayMap = new HashMap<>();
        if(ListUtils.isNotEmpty(riskPropertyPayList)){
            //key=标的-险种-责任,value=金额
            dutyHisPayMap = riskPropertyPayList.stream().collect(Collectors.groupingBy(planDutyKey,
                    Collectors.collectingAndThen(Collectors.mapping(RiskPropertyPayDTO::getDutyDetailPay,
                            Collectors.reducing(BigDecimal::add)),Optional::get)));
        }
        return dutyHisPayMap;
    }

    private BigDecimal calculateMaxPay(BigDecimal basePay,BigDecimal hisPay){
        basePay = BigDecimalUtils.isGreaterZero(basePay) ? basePay : BigDecimal.ZERO;
        hisPay = BigDecimalUtils.isGreaterZero(hisPay) ? hisPay : BigDecimal.ZERO;
        BigDecimal maxPay = basePay.subtract(hisPay);
        if(BigDecimalUtils.isGreaterZero(maxPay)){
            return maxPay;
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal getDutyLimitPay(List<DutyAttributeDTO> attributeList,BigDecimal dutyHisPay){
        if(ListUtils.isEmptyList(attributeList)){
            return null;
        }
        String attrValue = attributeList.stream().filter(dto -> BaseConstant.STRING_6.equals(dto.getAttrCode())).findFirst().orElse(new DutyAttributeDTO()).getAttrValue();
        if(StringUtils.isEmptyStr(attrValue)){
            return null;
        }

        BigDecimal limitPay = null;
        try {
            limitPay = new BigDecimal(attrValue);
        }catch (Exception e){
            return null;
        }

        return calculateMaxPay(limitPay,dutyHisPay);
    }

    @Override
    public void initEstRiskPropertyMaxPay(List<EstimatePolicyDTO> estimatePolicyList) {

        if(!switchRiskProperty){
            LogUtil.info("switchRiskProperty:关闭");
            return;
        }
        if (ListUtils.isEmptyList(estimatePolicyList)) {
            return;
        }

        for (EstimatePolicyDTO policy : estimatePolicyList) {
            List<RiskGroupDTO> riskGroupList = policy.getRiskGroupList();
            if(ListUtils.isEmptyList(riskGroupList)){
                continue;
            }
            String policyNo = policy.getPolicyNo();
            String reportNo = policy.getReportNo();
            for (RiskGroupDTO riskGroup : riskGroupList) {
                List<CaseRiskPropertyDTO> riskList = riskGroup.getRiskPropertyInfoList();
                Map<String, BigDecimal> dutyHisPayMap = getDutyRiskHisPayMap(getRiskPayedList(policyNo,reportNo,riskList));
                BigDecimal dutyHisPay = null;
                for (CaseRiskPropertyDTO riskppt : riskList) {
                    for (EstimatePlanDTO plan : riskppt.getEstimatePlanList()) {
                        for (EstimateDutyRecordDTO duty : plan.getEstimateDutyRecordList()) {
                            dutyHisPay = dutyHisPayMap.get(riskppt.getIdPlyRiskProperty()+plan.getPlanCode()+duty.getDutyCode());
                            duty.setDutyMaxPay(calculateMaxPay(duty.getBaseAmountPay(),dutyHisPay));
                        }
                        //计算共享保额最大给付额
                        computeEstDutyShareMaxPay(plan,dutyHisPayMap);
                    }
                }
            }
            policy.setRiskGroupList(sortRiskGroup(riskGroupList));
        }
    }

    private List<RiskGroupDTO> sortRiskGroup(List<RiskGroupDTO> riskGroupList){
        if(ListUtils.isEmptyList(riskGroupList)){
            return riskGroupList;
        }
        for (RiskGroupDTO riskGroup : riskGroupList) {
            List<CaseRiskPropertyDTO> riskList = riskGroup.getRiskPropertyInfoList();
            if(ListUtils.isEmptyList(riskList)){
                continue;
            }
            for (CaseRiskPropertyDTO risk : riskList) {
                risk.setEstimatePlanList(sortEstPlanList(risk.getEstimatePlanList()));
                risk.setPlanPayArr(sortPlanList(risk.getPlanPayArr()));
            }
            if(riskList.size() > 1){
                riskGroup.setRiskPropertyInfoList(riskList.stream().sorted(Comparator.comparing(
                        CaseRiskPropertyDTO::getIdPlyRiskProperty)).collect(Collectors.toList()));
            }
        }
        if(riskGroupList.size() > 1){
            return riskGroupList.stream().sorted(Comparator.comparing(RiskGroupDTO::getIdPlyRiskGroup)).collect(Collectors.toList());
        }
        return riskGroupList;
    }

    private List<PlanPayDTO> sortPlanList(List<PlanPayDTO> planList){
        if(ListUtils.isEmptyList(planList)){
            return planList;
        }
        for (PlanPayDTO planPayDTO : planList) {
            List<DutyPayDTO> dutyList = planPayDTO.getDutyPayArr();
            if(ListUtils.isEmptyList(dutyList)){
                continue;
            }
            for (DutyPayDTO dutyPayDTO : dutyList) {
                List<DutyDetailPayDTO> detailList = dutyPayDTO.getDutyDetailPayArr();
                if(ListUtils.isEmptyList(detailList)){
                    continue;
                }
                dutyPayDTO.setDutyDetailPayArr(detailList.stream().sorted(Comparator.comparing(
                        DutyDetailPayDTO::getDutyDetailCode)).collect(Collectors.toList()));
            }
            planPayDTO.setDutyPayArr(dutyList.stream().sorted(Comparator.comparing(DutyPayDTO::getDutyCode)).collect(Collectors.toList()));
        }
        return planList.stream().sorted(Comparator.comparing(PlanPayDTO::getPlanCode)).collect(Collectors.toList());
    }

    private List<EstimatePlanDTO> sortEstPlanList(List<EstimatePlanDTO> estPlanList){
        if(ListUtils.isEmptyList(estPlanList)){
            return estPlanList;
        }
        for (EstimatePlanDTO estPlan : estPlanList) {
            List<EstimateDutyRecordDTO> dutyList = estPlan.getEstimateDutyRecordList();
            if(ListUtils.isEmptyList(dutyList)){
                continue;
            }
            estPlan.setEstimateDutyRecordList(dutyList.stream().sorted(Comparator.comparing(
                    EstimateDutyRecordDTO::getDutyCode)).collect(Collectors.toList()));
        }
        return estPlanList.stream().sorted(Comparator.comparing(EstimatePlanDTO::getPlanCode)).collect(Collectors.toList());
    }

    @Override
    public void checkRiskPropertyPay(List<PolicyPayDTO> policyPays) {
        for (PolicyPayDTO policyPay : policyPays) {
            List<RiskGroupDTO> riskGroupList = policyPay.getRiskGroupList();
            if(ListUtils.isEmptyList(riskGroupList)){
                return;
            }
            BigDecimal totalPayAmt = BigDecimal.ZERO;
            BigDecimal riskPayAmt;
            BigDecimal detailPayAmt;
            Map<String,BigDecimal> riskPayAmtMap = new HashMap<>();
            Map<String,BigDecimal> attrMap = getLimitAmountMap(policyPay.getPolicyNo());
            for (RiskGroupDTO riskGroupDTO : riskGroupList) {
                List<CaseRiskPropertyDTO> riskList = riskGroupDTO.getRiskPropertyInfoList();
                if(ListUtils.isEmptyList(riskList)){
                    throw new GlobalBusinessException("方案下标的不能为空");
                }
                for (CaseRiskPropertyDTO riskDTO : riskList) {
                    riskPayAmt = BigDecimal.ZERO;
                    List<PlanPayDTO> planList = riskDTO.getPlanPayArr();
                    if(ListUtils.isEmptyList(planList)){
                        throw new GlobalBusinessException("标的险种不能为空");
                    }
                    for (PlanPayDTO planDTO : planList) {
                        List<DutyPayDTO> dutyList = planDTO.getDutyPayArr();
                        if(ListUtils.isEmptyList(dutyList)){
                            throw new GlobalBusinessException("标的责任不能为空");
                        }
                        for (DutyPayDTO dutyDTO : dutyList) {
                            List<DutyDetailPayDTO> detailList = dutyDTO.getDutyDetailPayArr();
                            if(ListUtils.isEmptyList(detailList)){
                                throw new GlobalBusinessException("标的责任明细不能为空");
                            }
                            for (DutyDetailPayDTO detailDTO : detailList) {
                                detailPayAmt = BigDecimalUtils.isNullOrZero(detailDTO.getSettleAmount()) ?
                                        detailDTO.getAutoSettleAmount() : detailDTO.getSettleAmount();
                                if(detailPayAmt == null){
                                    detailPayAmt = BigDecimal.ZERO;
                                }
                                riskPayAmt = riskPayAmt.add(detailPayAmt);
                            }
//                            parseDutyAttr(dutyDTO.getAttributes(),attrMap);
                        }
                    }
                    riskPayAmtMap.put(riskDTO.getIdPlyRiskProperty(),riskPayAmt);
                    totalPayAmt = totalPayAmt.add(riskPayAmt);
                }
            }

            final BigDecimal accidentLimitAmt = attrMap.get(BaseConstant.STRING_0);
            if(accidentLimitAmt != null && totalPayAmt.compareTo(accidentLimitAmt) > 0){
                throw new GlobalBusinessException("赔付金额["+totalPayAmt+"]不能大于每次事故责任限额["+accidentLimitAmt+"]");
            }
            final BigDecimal personLimitAmt = attrMap.get(BaseConstant.STRING_1);
            if(personLimitAmt != null){
                riskPayAmtMap.forEach((k,v) ->{
                    if(v != null && v.compareTo(personLimitAmt) > 0){
                        throw new GlobalBusinessException("标的赔付金额["+v+"]不能大于每人每次责任限额["+personLimitAmt+"]");
                    }
                });
            }
        }
    }

    @Override
    public void checkRiskPay(String reportNo, List<PolicyPayDTO> policyPays) {
        if (ListUtils.isEmptyList(policyPays)) {
            return;
        }

        if (!riskPropertyService.displayRiskProperty(null, policyPays.get(0).getPolicyNo())) {
            return;
        }

        Map<String, List<AhcsPolicyInfoEntity>> policyInfoMap = policyInfoMapper.selectByReportNo(reportNo).stream().collect(Collectors.groupingBy(AhcsPolicyInfoEntity::getPolicyNo));
        for (PolicyPayDTO policyPay : policyPays) {
            String policyNo = policyPay.getPolicyNo();
            List<AhcsPolicyInfoEntity> policyInfoList = policyInfoMap.get(policyNo);
            if (ListUtils.isEmptyList(policyInfoList)) {
                continue;
            }
            AhcsPolicyInfoEntity policyInfo = policyInfoList.get(0);

            // 校验保单累计限额
            BigDecimal totalDutyLimit = policyInfo.getTotalDutyLimit();
            if (Objects.nonNull(totalDutyLimit)) {
                BigDecimal policyHistoryPay = residueAmountService.getPolicyHistoryPay(reportNo, policyNo);
                if (policyHistoryPay.add(policyPay.getSettleAmount()).compareTo(totalDutyLimit) > 0) {
                    throw new GlobalBusinessException(String.format("保单[%s]下所有案件赔款不能超过保单累计限额，保单累计限额为： %s", policyNo, totalDutyLimit));
                }
            }
        }
    }

    private void parseDutyAttr(List<DutyAttributeDTO> attributes,Map<String,BigDecimal> attrMap){
        if(ListUtils.isEmptyList(attributes)){
            return;
        }
        for (DutyAttributeDTO attribute : attributes) {
            String attrVal;
            if(BaseConstant.STRING_0.equals(attribute.getAttrCode()) || BaseConstant.STRING_1.equals(attribute.getAttrCode())){
                attrVal = attribute.getAttrValue();
                if(StringUtils.isEmptyStr(attrVal)){
                    continue;
                }
                BigDecimal nextLimitAmt = null;
                try {
                    nextLimitAmt = new BigDecimal(attrVal);
                }catch (Exception e){
                }
                if(nextLimitAmt == null){
                    continue;
                }
                BigDecimal limitAmt = attrMap.get(attribute.getAttrCode());
                if(limitAmt == null){
                    attrMap.put(attribute.getAttrCode(),nextLimitAmt);
                }else{
                    if(nextLimitAmt.compareTo(limitAmt) < 0){
                        attrMap.put(attribute.getAttrCode(),nextLimitAmt);
                    }
                }
            }
        }
    }

    private Map<String,BigDecimal> getLimitAmountMap(String policyNo){
        Map<String,BigDecimal> limitAmtMap = new HashMap<>();
        //todo 待承保提供限额字段
        return limitAmtMap;
    }

    private List<RiskPropertyPayDTO> getRiskPayedList(String policyNo,String reportNo,List<CaseRiskPropertyDTO> riskPropertyList){
        RiskPropertyPayDTO riskPayQuery = new RiskPropertyPayDTO(reportNo,null);
        riskPayQuery.setPolicyNo(policyNo);
        riskPayQuery.setIdRiskPropertyList(riskPropertyList.stream().map(CaseRiskPropertyDTO::getIdPlyRiskProperty).collect(Collectors.toList()));
        //已赔付
        return riskPropertyPayMapper.getRiskPropertyPlanPayList(riskPayQuery);
    }

    private void computeEstDutyShareMaxPay(EstimatePlanDTO plan,Map<String, BigDecimal> dutyHisPayMap){
        /*Map<String, BigDecimal> dutyShareHisPayMap = null;
        for (EstimateDutyRecordDTO duty : plan.getEstimateDutyRecordList()) {
            if(duty.getIsDutyShareAmount() || StringUtils.isNotEmpty(duty.getShareDutyGroup())){
                if(dutyShareHisPayMap.get(duty.getDutyCode()) == null){
                    dutyShareHisPayMap = getDutyShareHisPayMap(duty.getIdPlyRiskProperty(),plan.getPlanCode(),duty.getShareDutyGroup(),dutyHisPayMap);
                }
                duty.setDutyMaxPay(calculateMaxPay(duty.getBaseAmountPay(),dutyShareHisPayMap.get(duty.getDutyCode())));
            }
        }*/
    }

    private void computeDutyShareMaxPay(PlanPayDTO plan,Map<String, BigDecimal> dutyHisPayMap){
        /*Map<String, BigDecimal> dutyShareHisPayMap = null;
        for (DutyPayDTO duty : plan.getDutyPayArr()) {
            if(duty.getIsDutyShareAmount() || StringUtils.isNotEmpty(duty.getShareDutyGroup())){
                if(dutyShareHisPayMap.get(duty.getDutyCode()) == null){
                    dutyShareHisPayMap = getDutyShareHisPayMap(duty.getIdPlyRiskProperty(),plan.getPlanCode(),duty.getShareDutyGroup(),dutyHisPayMap);
                }
                duty.setMaxAmountPay(calculateMaxPay(duty.getBaseAmountPay(),dutyShareHisPayMap.get(duty.getDutyCode())));
            }

            if(ListUtils.isEmptyList(duty.getDutyDetailPayArr())){
                continue;
            }
            for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                detail.setDutyMaxPay(duty.getMaxAmountPay());
                if(detail.getMaxAmountPay() != null && detail.getDutyMaxPay() != null && detail.getMaxAmountPay().compareTo(detail.getDutyMaxPay()) > 0){
                    detail.setMaxAmountPay(detail.getDutyMaxPay());
                }
            }
        }*/
    }

    private Map<String, BigDecimal> getDutyShareHisPayMap(String idRiskProperty,String planCode,String shareDutyGroup,Map<String, BigDecimal> dutyHisPayMap){
        Map<String, BigDecimal> dutyShareHisPayMap = new HashMap<>();
        List<String> shareDutyList = Arrays.asList(shareDutyGroup.split(","));
        BigDecimal shareDutyHisPayAmt = BigDecimal.ZERO;
        for (String shareDutyCode : shareDutyList) {
            shareDutyHisPayAmt = shareDutyHisPayAmt.add(Optional.ofNullable(dutyHisPayMap.get(
                    idRiskProperty+planCode+shareDutyCode)).orElse(BigDecimal.ZERO));
        }
        for (String shareDutyCode : shareDutyList) {
            dutyShareHisPayMap.put(shareDutyCode,shareDutyHisPayAmt);
        }
        return dutyShareHisPayMap;

    }
}
