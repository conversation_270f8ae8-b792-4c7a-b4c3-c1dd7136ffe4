package com.paic.ncbs.claim.service.settle.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.settle.CoinsureInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.CoinsureRecordMapper;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureRecordDTO;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("CoinsureService")
public class CoinsureServiceImpl implements CoinsureService {

    @Autowired
    private CoinsureInfoMapper coinsureInfoMapper;

    @Autowired
    private CoinsureRecordMapper coinsureRecordMapper;
    @Override
    public boolean isCoinsureCase(String reportNo) {
        int count = coinsureInfoMapper.getCoinsureCountByReportNo(reportNo);
        return count > 0;
    }

    @Override
    public Map<String, String> getCoinsureDescByReportNo(String reportNo) {
        Map<String,String> resultMap = new HashMap<>();
        List<CoinsureDTO> coinsureList = coinsureInfoMapper.getCoinsureDescByReportNo(reportNo);
        if(ListUtils.isEmptyList(coinsureList)){
            return resultMap;
        }
        for (CoinsureDTO dto : coinsureList) {
            if("0".equals(dto.getAcceptInsuranceFlag())){
                resultMap.put(dto.getPolicyNo(), "该保单我司为从共保人，请核实！");
            }else {
                resultMap.put(dto.getPolicyNo(), "该保单我司为主承保人，请核实！");
            }
        }
        return resultMap;
    }

    @Override
    public List<CoinsureDTO> getCoinsureByPolicyNo(String policyNo) {
        return Optional.ofNullable(coinsureInfoMapper.getCoinsureByPolicyNo(policyNo)).orElse(new ArrayList<>());
    }

    @Override
    public Map<String, List<CoinsureDTO>> getCoinsureListByReportNo(String reportNo) {
        List<CoinsureDTO> coinsureList = coinsureInfoMapper.getCoinsureListByReportNo(reportNo);
        LogUtil.audit("共保列表reportNo={},{}",reportNo,JSON.toJSONString(coinsureList));
        if(ListUtils.isEmptyList(coinsureList)){
            return new HashMap<>();
        }

        // 从共排除其它保司
        return coinsureList.stream().filter(i -> !("2".equals(i.getCoinsuranceType()) && "0".equals(i.getCompanyFlag()))).collect(Collectors.groupingBy(CoinsureDTO::getPolicyNo));
    }

    @Override
    public Map<String, List<CoinsureRecordDTO>> getCoinsureRecordByReportNo(String reportNo) {
        CoinsureRecordDTO recordDTO = new CoinsureRecordDTO();
        recordDTO.setReportNo(reportNo);
        List<CoinsureRecordDTO> list = coinsureRecordMapper.selectByParam(recordDTO);
        if(ListUtils.isEmptyList(list)){
            return new HashMap<>();
        }

        return list.stream().collect(Collectors.groupingBy(CoinsureRecordDTO::getPolicyNo));
    }
}
