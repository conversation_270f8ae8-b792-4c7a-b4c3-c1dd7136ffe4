package com.paic.ncbs.claim.service.prepay.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.PermissionUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PreApproveVO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePayCaseVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.prepay.ClmsPolicyPrepayDutyDetailService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import com.paic.ncbs.claim.service.prepay.PrePayTransactionService;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("prePayTransactionService")
public class PrePayTransactionServiceImpl implements PrePayTransactionService {
	@Autowired
	private PaymentItemService paymentItemService;
	@Autowired
	private FeePayMapper feePayMapper;
	@Autowired
	private PrePayMapper prePayMapper;
	@Autowired
	private PolicyPayMapper policyPayMapper;
	@Autowired
	private PlanPayMapper planPayMapper;
	@Autowired
	private DutyPayMapper dutyPayMapper;

	@Autowired
	private DutyDetailPayMapper dutyDetailPayMapper;
	@Autowired
	private BpmService bpmService;

	@Autowired
	private CaseProcessService caseProcessService ;
	@Autowired
	private ClmsPolicyPrepayDutyDetailService clmsPolicyPrepayDutyDetailService;

	@Autowired
	private IOperationRecordService operationRecordService;
	@Autowired
	private TaskInfoMapper taskInfoMapper;
	@Autowired
	private PolicyInfoMapper policyInfoMapper;
	@Autowired
	private PermissionService permissionService;
	@Autowired
	private PaymentItemMapper paymentItemMapper;

	@Autowired
	private PrePayService prePayService;
	@Autowired
	private PermissionUserMapper permissionUserMapper;


	@Transactional
	@Override
	public void savePrePayApplyInfo(PrePayCaseVO preCaseVO, PrePayInfoDTO preDTO, List<DutyPrepayInfoDTO> dutyList,
									 List<PaymentItemDTO> itemList) {
		preDTO.setTaskDefinitionBpmKey(preCaseVO.getTaskNode());
		Integer subTimes = prePayService.getApprovedSubTimes(preDTO.getReportNo(),preDTO.getCaseTimes());

		preDTO.setSubTimes(subTimes);
		prePayMapper.savePrePayInfo(preDTO);

		prePayMapper.saveDutyPrepayInfo(dutyList);

        PaymentItemDTO itemDTO = new PaymentItemDTO();
        itemDTO.setReportNo(preCaseVO.getReportNo());
        itemDTO.setCaseTimes(preCaseVO.getCaseTimes());
        itemDTO.setSubTimes(preCaseVO.getSubTimes());
        itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PRE_PAY);
		paymentItemService.delPaymentItem(itemDTO);

        paymentItemService.addPaymentItemList(itemList);

		//保存责任明细数据
		clmsPolicyPrepayDutyDetailService.saveDutyDetail(dutyList);

		// 先挂起再生成新的任务
		bpmService.suspendOrActiveTask_oc(preCaseVO.getReportNo(), preCaseVO.getCaseTimes(),preCaseVO.getTaskNode(),true);
		//操作记录
		operationRecordService.insertOperationRecordByLabour(preCaseVO.getReportNo(), BpmConstants.OC_PREPAY_REVIEW, "发起", null);
		//bpmService.startProcess_oc(preCaseVO.getReportNo(), preCaseVO.getCaseTimes(),BpmConstants.OC_PREPAY_REVIEW);
		startPreReview(preCaseVO.getReportNo(), preCaseVO.getCaseTimes(),preCaseVO.getPreTotalAmount(),
				preCaseVO.getPrePayApproved());
		//
		String caseProcessStatus = Transform.getCaseProcessStatus(BpmConstants.OC_PREPAY_REVIEW);
		caseProcessService.updateCaseProcess(preCaseVO.getReportNo(), preCaseVO.getCaseTimes(), caseProcessStatus);

	}

	private void startPreReview(String reportNo,Integer caseTimes,BigDecimal payAmount,String prePayApproved){
//		String bpmKey = taskInfoMapper.getPendingTaskCount(reportNo, caseTimes);
//		if (com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(bpmKey)){
//			throw new GlobalBusinessException("该案件还有其它待处理的任务！");
//		}
		String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);
		TaskInfoDTO startTask = new TaskInfoDTO();
		startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
		startTask.setTaskId(UuidUtil.getUUID());
		startTask.setReportNo(reportNo);
		startTask.setCaseTimes(caseTimes);
		startTask.setTaskDefinitionBpmKey(BpmConstants.OC_PREPAY_REVIEW);
		startTask.setAssigneeTime(new Date());
		startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
		//费用+赔款
		PaymentItemDTO itemDTO = new PaymentItemDTO();
		itemDTO.setReportNo(reportNo);
		itemDTO.setCaseTimes(caseTimes);
		Date createdDate=new Date();
		startTask.setApplyer(Constants.SYSTEM_USER);
		startTask.setApplyerName(Constants.SYSTEM_USER);
		startTask.setUpdatedBy(Constants.SYSTEM_USER);
		startTask.setCreatedBy(Constants.SYSTEM_USER);
		startTask.setCreatedDate(DateUtil.offsetSecond(createdDate,25));
		BigDecimal sumPreFee = paymentItemMapper.getSumPreFeeByReportNo(itemDTO);
		if(sumPreFee == null){
			sumPreFee = BigDecimal.ZERO;
		}
		BigDecimal totalAmount = payAmount.add(sumPreFee);
		LogUtil.audit("预赔案件总金额={}",totalAmount);
		Integer taskGrade = permissionService.getPermissionGrade(Constants.PERMISSION_VERIFY, ConfigConstValues.HQ_DEPARTMENT,totalAmount);
		if(taskGrade == null){
			//查不到等级，默认给4级
			LogUtil.audit("使用默认案件等级");
			taskGrade = 4;
		}

		PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_VERIFY,departmentCode,1);
		Integer auditGrade = permissionUser.getGrade();
		if(auditGrade == null){
			LogUtil.audit("使用默认审批等级");
			auditGrade = 1;
		}
		String managerUserId = null;
		String managerUserName = null;
		if(!StringUtils.isEmptyStr(prePayApproved)){
			String [] parts = prePayApproved.split("-",2);
			managerUserId = parts[0];
			managerUserName = parts[1];
		}
		String comCode = permissionUserMapper.getComCodeByUserId(managerUserId);
		startTask.setTaskGrade(taskGrade);
		startTask.setAuditGrade(auditGrade);
		if(StringUtils.isEmptyStr(comCode)){
			startTask.setDepartmentCode(permissionUser.getComCode());
		} else {
			startTask.setDepartmentCode(comCode);
		}
		startTask.setAssigner(managerUserId);
		startTask.setAssigneeName(managerUserName);
		taskInfoMapper.addTaskInfo(startTask);
	}

	@Transactional
	@Override
	public void savePrePayApprove(PreApproveVO preApproveVO,Boolean flag) {
		LogUtil.audit("保存预赔pay表vo={}", JSON.toJSONString(preApproveVO));
		PrePayInfoDTO prePayInfoDTO = preApproveVO.getPrePayInfoDTO();
		if(ConstValues.AUDIT_AGREE_CODE.equals(prePayInfoDTO.getVerifyOptions())){
			//同意 写pay表
			if (ListUtils.isNotEmpty(preApproveVO.getUpdatePolicyPayList())) {
				//更新保单信息
				policyPayMapper.updatePolicyPayListForPrePay(preApproveVO.getUpdatePolicyPayList());
			}
			if (ListUtils.isNotEmpty(preApproveVO.getAddPolicyPayList())) {
				//保存保单赔付信息
				policyPayMapper.insertPolicyPayInfoList(preApproveVO.getAddPolicyPayList());
			}
			if (ListUtils.isNotEmpty(preApproveVO.getPlanPayList())) {
				//保存险种赔付信息
				planPayMapper.insertPlanPayInfo(preApproveVO.getPlanPayList());
			}
			if (ListUtils.isNotEmpty(preApproveVO.getDutyPayList())) {
				//保存责任赔付信息
				dutyPayMapper.insertDutyPayInfoList(preApproveVO.getDutyPayList());
				//获取责任明细赔付信息
				List<DutyDetailPayDTO> list = getDutyDetailPay(preApproveVO.getDutyPayList(),prePayInfoDTO,preApproveVO.getSubTimes());
				//保存责任明细赔付信息
				dutyDetailPayMapper.insertDutyDetailPayList(list);

			}
			PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
			paymentItemDTO.setReportNo(prePayInfoDTO.getReportNo());
			paymentItemDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
			paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
			paymentItemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_PAY);
			// 刪除賠款的數據
			paymentItemService.delPaymentItem(paymentItemDTO);
			PaymentItemDTO paymentItemDTO2 = new PaymentItemDTO();
			paymentItemDTO2.setReportNo(prePayInfoDTO.getReportNo());
			paymentItemDTO2.setCaseTimes(prePayInfoDTO.getCaseTimes());
			paymentItemDTO2.setClaimType(SettleConst.CLAIM_TYPE_PAY);
			paymentItemDTO2.setPaymentType(SettleConst.PAYMENT_TYPE_FEE);
			// 刪除費用的數據
			paymentItemService.delPaymentItem(paymentItemDTO2);
			feePayMapper.delFeeByReport(prePayInfoDTO.getReportNo(), prePayInfoDTO.getCaseTimes(), SettleConst.CLAIM_TYPE_PAY);

			PaymentItemDTO itemDTO = new PaymentItemDTO();
			itemDTO.setReportNo(prePayInfoDTO.getReportNo());
			itemDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
			itemDTO.setSubTimes(preApproveVO.getSubTimes());
			itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PRE_PAY);
			itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
			itemDTO.setPaymentType(SettleConst.PAYMENT_TYPE_FEE_PREPAY);
			itemDTO.setUpdatedBy(preApproveVO.getPrePayInfoDTO().getUpdatedBy());
			paymentItemService.updatePaymentItemStatus(itemDTO);

			updatePrePayItem(prePayInfoDTO,preApproveVO.getSubTimes());
			//操作记录
			operationRecordService.insertOperationRecordByLabour(prePayInfoDTO.getReportNo(), BpmConstants.OC_PREPAY_REVIEW, "通过", null);
		}else{
			//不同意 删除支付项、删除费用
			PaymentItemDTO itemDTO = new PaymentItemDTO();
			itemDTO.setReportNo(prePayInfoDTO.getReportNo());
			itemDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
			itemDTO.setSubTimes(preApproveVO.getSubTimes());
			itemDTO.setClaimType(SettleConst.CLAIM_TYPE_PRE_PAY);
			paymentItemService.delPaymentItem(itemDTO);

			FeeInfoDTO feeInfoDTO = new FeeInfoDTO();
			feeInfoDTO.setReportNo(prePayInfoDTO.getReportNo());
			feeInfoDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
			feeInfoDTO.setUpdatedBy(prePayInfoDTO.getUpdatedBy());
			feeInfoDTO.setSubTimes(preApproveVO.getSubTimes());
			feePayMapper.delPreFeeBySubTimes(feeInfoDTO);

			//删除duty_prepay
			prePayMapper.deleteDutyPrepayInfoByReportNo(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),preApproveVO.getSubTimes());
			//操作记录
			operationRecordService.insertOperationRecordByLabour(prePayInfoDTO.getReportNo(), BpmConstants.OC_PREPAY_REVIEW, "不通过", null);
		}

		prePayMapper.updatePrePayApprove(prePayInfoDTO);

		bpmService.completeTask_oc(prePayInfoDTO.getReportNo(), prePayInfoDTO.getCaseTimes(), BpmConstants.OC_PREPAY_REVIEW);
		if(flag){
			if(prePayInfoDTO.getPrepayBigType() != null){
				String taskNode = "2".equals(prePayInfoDTO.getPrepayBigType()) ? BpmConstants.OC_CHECK_DUTY : BpmConstants.OC_MANUAL_SETTLE;
				bpmService.suspendOrActiveTask_oc(prePayInfoDTO.getReportNo(), prePayInfoDTO.getCaseTimes(),taskNode,false);
				//同时更新xlms_case_process表
				String caseProcessStatus = Transform.getCaseProcessStatus(taskNode);
				caseProcessService.updateCaseProcess(prePayInfoDTO.getReportNo(), prePayInfoDTO.getCaseTimes(), caseProcessStatus);
			}
		}

	}

	/**
	 * 责任明细赔付获取
	 * @param dutyPayList
	 * @param prePayInfoDTO
	 */
	private List<DutyDetailPayDTO> getDutyDetailPay(List<DutyPayDTO> dutyPayList, PrePayInfoDTO prePayInfoDTO,Integer subTimes) {
		List<ClmsPolicyPrepayDutyDetailDTO> dutyDetailDTOList =	clmsPolicyPrepayDutyDetailService.getDutyDetailInfo(prePayInfoDTO.getReportNo(),prePayInfoDTO.getCaseTimes(),subTimes);
		if(CollectionUtils.isEmpty(dutyDetailDTOList)){
			throw new GlobalBusinessException("查询责任明细异常");
		}
		//按责任编码分组
	    Map<String ,List<ClmsPolicyPrepayDutyDetailDTO>> dutycodeMap =dutyDetailDTOList.stream().collect(Collectors.groupingBy(ClmsPolicyPrepayDutyDetailDTO:: getDutyCode));
		List<DutyDetailPayDTO> dutyDetailPayDTOList = new ArrayList<>();
		Date date = new Date();
		for (DutyPayDTO dutyPayDTO : dutyPayList) {
			List<ClmsPolicyPrepayDutyDetailDTO> dtList = dutycodeMap.get(dutyPayDTO.getDutyCode());
			for (ClmsPolicyPrepayDutyDetailDTO dto : dtList) {
				DutyDetailPayDTO  dutyDetailPayDTO = new DutyDetailPayDTO();
				dutyDetailPayDTO.setIdAhcsDutyDetailPay(UuidUtil.getUUID());
				dutyDetailPayDTO.setIdAhcsDutyPay(dutyPayDTO.getIdAhcsDutyPay());
				dutyDetailPayDTO.setCaseNo(dutyPayDTO.getCaseNo());
				dutyDetailPayDTO.setReportNo(prePayInfoDTO.getReportNo());
				dutyDetailPayDTO.setCaseTimes(dto.getCaseTimes());
				dutyDetailPayDTO.setPlanCode(dutyPayDTO.getPlanCode());
				dutyDetailPayDTO.setDutyCode(dutyPayDTO.getDutyCode());
				dutyDetailPayDTO.setCaseNo(dto.getCaseNo());
				dutyDetailPayDTO.setBaseAmountPay(dto.getDutyDetailAmount());
				dutyDetailPayDTO.setClaimType(dutyPayDTO.getClaimType());
				dutyDetailPayDTO.setSettleAmount(dto.getPrepayAmount());
				dutyDetailPayDTO.setAutoSettleAmount(dto.getPrepayAmount());
				dutyDetailPayDTO.setDutyDetailCode(dto.getDutyDetailCode());
				dutyDetailPayDTO.setDutyDetailName(dto.getDutyDetailName());
				dutyDetailPayDTO.setIndemnityMode("1");
				dutyDetailPayDTO.setCreatedBy(WebServletContext.getUserId());
				dutyDetailPayDTO.setCreatedDate(date);
				dutyDetailPayDTO.setUpdatedBy(WebServletContext.getUserId());
				dutyDetailPayDTO.setUpdatedDate(date);
				dutyDetailPayDTO.setPolicyNo(dutyPayDTO.getPolicyNo());
				dutyDetailPayDTOList.add(dutyDetailPayDTO);
			}
		}

		return dutyDetailPayDTOList;
	}

	private void updatePrePayItem(PrePayInfoDTO prePayInfoDTO,Integer subTimes) {
		List<FeeInfoDTO> syncFeePayInfo = feePayMapper.getSyncPrePayItem(prePayInfoDTO.getReportNo(), prePayInfoDTO.getCaseTimes(), SettleConst.CLAIM_TYPE_PRE_PAY,subTimes);
		if (!CollectionUtils.isEmpty(syncFeePayInfo)){
			PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
			paymentItemDTO.setReportNo(prePayInfoDTO.getReportNo());
			paymentItemDTO.setCaseTimes(prePayInfoDTO.getCaseTimes());
			paymentItemDTO.setClaimType(SettleConst.CLAIM_TYPE_PRE_PAY);
			paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
			List<PaymentItemDTO> itemList = paymentItemService.getPrePaymentItem(paymentItemDTO);
			syncFeePayInfo.forEach(dto->{
				for (PaymentItemDTO  itemDTOEx:itemList){
					if (itemDTOEx.getIdClmPaymentInfo().equals(dto.getIdClmPaymentInfo())
							&& itemDTOEx.getPolicyNo().equals(dto.getPolicyNo())
							&& dto.getFeeType().equals(itemDTOEx.getFeeType())) {
						dto.setIdClmPaymentItem(itemDTOEx.getIdClmPaymentItem());
						feePayMapper.updateClmPaymentItem(dto);
						break;
					}
				}
			});
		}
	}

	@Transactional
	@Override
	public void savePrePayFeeInfo(String reportNo, Integer caseTimes, String userId , Integer subTimes , List<FeeInfoDTO> feeList, List<InvoiceInfoDTO> invoiceList) {
		FeeInfoDTO feeInfoDTO = new FeeInfoDTO();
		feeInfoDTO.setReportNo(reportNo);
		feeInfoDTO.setCaseTimes(caseTimes);
		feeInfoDTO.setUpdatedBy(userId);
		feeInfoDTO.setSubTimes(subTimes);
		//删发票
		feePayMapper.delPreInvoiceBySubTimes(feeInfoDTO);
		//删费用
		feePayMapper.delPreFeeBySubTimes(feeInfoDTO);

		if(ListUtils.isNotEmpty(feeList)){
			feePayMapper.addFeePayList(feeList);
		}
		if(ListUtils.isNotEmpty(invoiceList)){
			feePayMapper.addInvoiceInfoList(invoiceList);
		}
	}

	@Override
	@Transactional
	public void delPrePayFeeInfo(String reportNo, Integer caseTimes, String userId, Integer subTimes) {
		FeeInfoDTO feeInfoDTO = new FeeInfoDTO();
		feeInfoDTO.setReportNo(reportNo);
		feeInfoDTO.setCaseTimes(caseTimes);
		feeInfoDTO.setUpdatedBy(userId);
		feeInfoDTO.setSubTimes(subTimes);
		//删发票
		feePayMapper.delPreInvoiceBySubTimes(feeInfoDTO);
		//删费用
		feePayMapper.delPreFeeBySubTimes(feeInfoDTO);
	}
}
