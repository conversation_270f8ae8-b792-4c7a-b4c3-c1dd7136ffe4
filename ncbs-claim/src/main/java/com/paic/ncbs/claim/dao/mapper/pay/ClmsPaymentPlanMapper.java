package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentPlan;
import com.paic.ncbs.claim.model.dto.pay.ProductPlanInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 赔款拆分险种表(ClmsPaymentPlan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 20:59:56
 */
public interface ClmsPaymentPlanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param idClmsPaymentPlan 主键
     * @return 实例对象
     */
    ClmsPaymentPlan queryById(String idClmsPaymentPlan);

    /**
     * 通过 idClmPaymentItem 查询多条数据
     *
     * @param idClmPaymentItem 外键
     * @return 实例对象
     */
    List<ClmsPaymentPlan> getClmsPaymentPlanList(String idClmPaymentItem);

    /**
     * 统计总行数
     *
     * @param clmsPaymentPlan 查询条件
     * @return 总行数
     */
    long count(ClmsPaymentPlan clmsPaymentPlan);

    /**
     * 新增数据
     *
     * @param clmsPaymentPlan 实例对象
     * @return 影响行数
     */
    int insert(ClmsPaymentPlan clmsPaymentPlan);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPaymentPlan> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsPaymentPlan> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPaymentPlan> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ClmsPaymentPlan> entities);

    /**
     * 修改数据
     *
     * @param clmsPaymentPlan 实例对象
     * @return 影响行数
     */
    int update(ClmsPaymentPlan clmsPaymentPlan);

    /**
     * 通过主键删除数据
     *
     * @param idClmsPaymentPlan 主键
     * @return 影响行数
     */
    int deleteById(String idClmsPaymentPlan);

    List<ProductPlanInfo> getProductClass(@Param("paySerialNo") String paySerialNo);

}

