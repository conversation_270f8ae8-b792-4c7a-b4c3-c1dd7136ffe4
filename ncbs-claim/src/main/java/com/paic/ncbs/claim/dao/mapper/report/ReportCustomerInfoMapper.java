package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.model.dto.endcase.CustomerHistoryReportDTO;
import com.paic.ncbs.claim.model.dto.endcase.CustomerReprotInfoDTO;
import com.paic.ncbs.claim.model.dto.openapi.IcdDto;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportCustomerInfoMapper extends BaseDao<ReportCustomerInfoEntity> {

    ReportCustomerInfoEntity getReportCustomerInfoByReportNo(@Param("reportNo") String reportNo);

    List<ReportCustomerInfoEntity> getReportCustomerInfo(@Param("certificateNo") String certificateNo, @Param("reportNo") String reportNo);

    List<ReportCustomerInfoEntity> getHistoryByPolicyNo(@Param("policyNo") String policyNo );

    void insertList(@Param("list") List<ReportCustomerInfoEntity> list);

    ReportCustomerInfoEntity getCustomerName(String reportNo);

    List<IcdDto> getCustomerIcdInfo(String customerNo);

    /**
     * 查询被保人是否有过报案记录
     * @param req
     */
    List<CustomerReprotInfoDTO> getHistoryReportInfo(ReportQueryReqDTO req);

    /**
     * 查询被保险人历史报案记录信息 险种层级
     * @param req
     */
    List<String> getCustomerHistoryPlanInfo(ReportQueryReqDTO req);
}