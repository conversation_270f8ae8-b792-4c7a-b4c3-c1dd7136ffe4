package com.paic.ncbs.claim.common.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;


public class DateUtils {
    public static final String DATE_STR_MILLI_SECOND = "yyyy-MM-dd HH:mm:ss SSS";
    private static final String PARAM_NOT_BE_NULL = "startDay or endDay or dateFormat should not be null";
    public static final String HH_MM_SS = "HH:mm:ss";
    public static final String SIMPLE_DATE_STR = "yyyy-MM-dd";
    public static final String MM_DD = "MM-dd";

    public static final String FULL_DATE_STR = "yyyy-MM-dd HH:mm:ss";
    public static final String PART_DATE_STR = "yyyy-MM-dd HH:mm";

    public static final String SIMPLE_RULE_DATE_STR = "yyyy/MM/dd";
    public static final String FULL_RULE_DATE_STR = "yyyy/MM/dd HH:mm:ss";
    public static final String PART_RULE_DATE_STR = "yyyy/MM/dd HH:mm";
    public static final String SMALL_PART_RULE_DATE_STR = "HH:mm";

    public static final String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String DATE_FORMAT_YYYYMMDDHHMM = "yyyyMMddHHmm";
    public static final String DATE_FORMAT_YYYYMMDDHH = "yyyyMMddHH";

    public static final Integer HOUR_IN_DAY = 24;
    public static final Integer MIN_IN_HOUR = 60  ;

    public static final String SECOND_FORMAT_000000= " 00:00:00";
      
    public static boolean isEffectiveDate() {
        try {
            SimpleDateFormat sf = new SimpleDateFormat(HH_MM_SS);
            String now = sf.format(now());
            Date nowTime = new SimpleDateFormat(HH_MM_SS).parse(now);
            Date startTime = new SimpleDateFormat(HH_MM_SS).parse("01:00:00");
            Date endTime = new SimpleDateFormat(HH_MM_SS).parse("20:00:00");
            if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
                return true;
            }
            Calendar date = Calendar.getInstance();
            date.setTime(nowTime);
            Calendar begin = Calendar.getInstance();
            begin.setTime(startTime);
            Calendar end = Calendar.getInstance();
            end.setTime(endTime);
            if (date.after(begin) && date.before(end)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    public static Date now() {
        return new Date();
    }

    public static Date parseToFormatDate(String dateStr, String formatStr) throws ParseException {
        if (StringUtils.isEmptyStr(dateStr) || StringUtils.isEmptyStr(formatStr)) {
            return null;
        }
        DateFormat dateFormat = new SimpleDateFormat(formatStr);
        return dateFormat.parse(dateStr);
    }

    public static String parseToFormatString(Date date, String fmt) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
        String dateStr = sdf.format(date);
        return dateStr;
    }

    public static Date parseToDate(String dateString) throws ParseException {
        if (dateString == null){
            throw new IllegalArgumentException("dateString should not be null");
        }
        DateFormat dateFormat = new SimpleDateFormat(SIMPLE_DATE_STR);
        DateFormat fullDateFormat = new SimpleDateFormat(FULL_DATE_STR);
        DateFormat partDateFormat = new SimpleDateFormat(PART_DATE_STR);

        DateFormat dateRullFormat = new SimpleDateFormat(SIMPLE_RULE_DATE_STR);
        DateFormat fullRullDateFormat = new SimpleDateFormat(FULL_RULE_DATE_STR);
        DateFormat partRullDateFormat = new SimpleDateFormat(PART_RULE_DATE_STR);
        if (dateString.matches(RegexStrings.REG_FULL_DATE)) {
            return fullDateFormat.parse(dateString);
        } else if (dateString.matches(RegexStrings.REG_DATE)) {
            return dateFormat.parse(dateString);
        } else if (dateString.matches(RegexStrings.REG_PART_DATE)) {
            return partDateFormat.parse(dateString);
        } else if (dateString.matches(RegexStrings.REG_RULE_FULL_DATE)) {
            return fullRullDateFormat.parse(dateString);
        } else if (dateString.matches(RegexStrings.REG_RULE_DATE)) {
            return dateRullFormat.parse(dateString);
        } else if (dateString.matches(RegexStrings.REG_RULE_PART_DATE)) {
            return partRullDateFormat.parse(dateString);
        } else {
            throw new IllegalArgumentException("dateString format error");
        }
    }

    public static Date addDate(Date startDate, int dayNum) {
        if (startDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DATE, dayNum);
        return calendar.getTime();
    }

    public static Date addMonth(Date startDate, int monthNum) {
        if (startDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.MONTH, monthNum);
        return calendar.getTime();
    }

    public static String dateFormat(Date date, String formatStr) {
        if (date == null || formatStr == null) {
            throw new IllegalArgumentException("the date or formatStr should not be null");
        }
        DateFormat dateFormat = new SimpleDateFormat(formatStr);
        return dateFormat.format(date);
    }

    public static String getChineseDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONDAY) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        return year + "年" + month + "月" + day + "日";
    }

    public static int getDaysBetween(Calendar d1, Calendar d2) {
        if (d1.after(d2)) {
            Calendar swap = d1;
            d1 = d2;
            d2 = swap;
        }
        int days = d2.get(Calendar.DAY_OF_YEAR) - d1.get(Calendar.DAY_OF_YEAR);
        int y2 = d2.get(Calendar.YEAR);
        if (d1.get(Calendar.YEAR) != y2) {
            d1 = (Calendar) d1.clone();
            do {
                days += d1.getActualMaximum(Calendar.DAY_OF_YEAR);                  d1.add(Calendar.YEAR, 1);
            } while (d1.get(Calendar.YEAR) != y2);
        }
        return days;
    }

    public static int getDaysBetween(Date d1, Date d2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(d1);
        cal2.setTime(d2);
        int days = getDaysBetween(cal1, cal2);
        return days;
    }

    public static Date parse2Date(String dateString) throws ParseException {

        if (StringUtils.isEmptyStr(dateString)) {
            return null;
        }

        return parseToDate(dateString);
    }

    public static boolean compareTimeBetweenDate(String startDay, String endDay) throws ParseException {
        if (startDay == null || endDay == null) {
            throw new IllegalArgumentException("startDay or endDay should not be null");
        }
        return (parseToDate(endDay).getTime() - parseToDate(startDay).getTime()) > 0 ? true : false;
    }

    public static boolean compareTimeBetweenDate(Date startDay, Date endDay) {
        if (null != startDay && null != endDay) {
            return endDay.getTime() > startDay.getTime();
        }
        return false;
    }

    public static Date getCurrentTime() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        Date date = cal.getTime();
        return date;
    }

    public static String stringParseToFormatString(String dateStr, String formatStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
        Date date = sdf.parse(dateStr);
        dateStr = sdf.format(date);
        return dateStr;
    }

    public static String parseToFormatString(Date date) {
        String dateStr = "";
        if (null == date) {
            return dateStr;
        }
        try {
            dateStr = DateUtils.parseToFormatString(date, DateUtils.FULL_DATE_STR);
        } catch (ParseException e) {
            LogUtil.error("日期转回异常", e);
        }
        return dateStr;
    }

    public static String parseToFormatStr(Date date, String fmt) {
        String dateStr = "";
        if (null == date) {
            return dateStr;
        }
        try {
            dateStr = DateUtils.parseToFormatString(date, fmt);
        } catch (ParseException e) {
            LogUtil.error("日期转回异常", e);
        }
        return dateStr;
    }

    public static String getCurrentDateFormat(String formatStr) {
        DateFormat dateFormat = new SimpleDateFormat(formatStr);
        return dateFormat.format(new Date());
    }

    public static String getCostTime(Date beginTime, Date endTime) {
        if (beginTime == null || endTime == null) {
            return null;
        }
        BigDecimal beginTimeBigDecimal = new BigDecimal(beginTime.getTime());
        BigDecimal endTimeBigDecimal = new BigDecimal(endTime.getTime());
        if (beginTimeBigDecimal.compareTo(endTimeBigDecimal) > 0) {
            return null;
        }
          BigDecimal costTime = endTimeBigDecimal.subtract(beginTimeBigDecimal);
          String costTimeStr = costTime.divide(new BigDecimal(60 * 1000), 2, RoundingMode.HALF_UP).toString();
        return costTimeStr;
    }

    public static String parseToFormatString(String strDate, String format) {
        String date = null;
        try {
            if (StringUtils.isEmptyStr(strDate)) {
                return date;
            }
            date = DateUtils.stringParseToFormatString(strDate, format);
        } catch (ParseException e) {
            LogUtil.error("日期转回异常={}", e);
        }
        return date;
    }

    public static Date formatStringToDate(String dateStr, String formatStr) {
        if (StringUtils.isEmptyStr(dateStr) || StringUtils.isEmptyStr(formatStr)) {
            return null;
        }
        DateFormat dateFormat = new SimpleDateFormat(formatStr);
        Date date = null;
        try {
            date = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            LogUtil.error("日期转换异常", e);
        }
        return date;
    }

    public static LocalDateTime date2LocalDateTime(Date date) {
        Instant instant = date.toInstant();
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static String getYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int year = calendar.get(Calendar.YEAR);
        return String.valueOf(year);
    }

    /**
     * 获取一天中最开始时间点
     * @param date
     * @return
     */
    public static Date beginOfDay(Date date) {
        return DateUtil.beginOfDay(date);
    }

    /**
     * 获取一天中的最后1秒钟
     * @param date
     * @return
     */
    public static Date endOfDay(Date date){
       return  DateUtil.endOfDay(date).offset(DateField.MILLISECOND, -999);
    }
    public static Date getBeforeDate2(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(6, calendar.get(6) - days);
        return calendar.getTime();
    }

    public static int daysOfTwoDate(Date endDate, Date beginDate) {
        double days_resutl = 0;

        Calendar aCalendar = Calendar.getInstance();
        Calendar bCalendar = Calendar.getInstance();
        aCalendar.setTime(endDate);
        bCalendar.setTime(beginDate);

        long atimes = aCalendar.getTimeInMillis();
        long btimes = bCalendar.getTimeInMillis();
        long temp = atimes - btimes;

        double days = (double) temp / (double) (1000 * 60 * 60 * 24);
        if (days > 0) {
            days_resutl = Math.ceil(days);
        } else {
            days_resutl = Math.floor(days);
        }
        return (int) days_resutl;
    }

    /**
     * 获取当前月的第一秒
     * @return
     */
    public static Date getFirstSecondOfMonth(){
        return getFirstSecondOfAppointMonth(0);
    }

    /**
     * 获取指定月份第一秒，基于当前月加减
     * @return
     */
    public static Date getFirstSecondOfAppointMonth(int amount){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, amount);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取指定月份第一天，基于当前月加减
     * @return
     */
    public static String getFirstDayStrOfAppointMonth(int amount) {
        return dateFormat(getFirstSecondOfAppointMonth(amount), SIMPLE_DATE_STR);
    }

    /**
     * 获取当前月的第一天
     * @return
     */
    public static String getFirstDayStrOfMonth() {
        return dateFormat(getFirstSecondOfAppointMonth(0), SIMPLE_DATE_STR);
    }

    /**
     * 设置时间毫秒数为0
     * @param date
     * @return
     */
    public static Date getPreciseTime(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获取指定日期所在月的第一天
     * @param date
     * @return
     */
    public static Date getFirstDayOfMonth (Date date) {
        // 将java.util.Date转换为java.time.LocalDate
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 获取该日期所在月的第一天
        LocalDate firstDayOfMonth = localDate.with(TemporalAdjusters.firstDayOfMonth());

        // 将LocalDate转换回java.util.Date
        return Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定日期所在月的最后一秒
     * @param date
     * @return
     */
    public static Date getLastSecondOfMonth(Date date) {
        // 将Date转换为LocalDate
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 获取当月的最后一天
        LocalDate lastDayOfMonth = localDate.withDayOfMonth(localDate.lengthOfMonth());

        // 获取当天的最后一秒
        LocalDateTime lastSecondOfMonth = lastDayOfMonth.atTime(23, 59, 59);

        // 将LocalDateTime转换回Date
        return Date.from(lastSecondOfMonth.atZone(ZoneId.systemDefault()).toInstant());
    }

}

