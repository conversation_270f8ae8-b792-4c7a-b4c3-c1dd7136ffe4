package com.paic.ncbs.claim.model.vo.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * SOP文件VO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP文件信息")
public class SopFileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文件id")
    private String idSopFile;

    @ApiModelProperty("关联sop主键")
    private String idSopMain;

    @ApiModelProperty("文件id")
    private String fileId;

    @ApiModelProperty("文件路径")
    private String fileUrl;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件格式")
    private String fileFormat;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    @ApiModelProperty("是否有效（Y/N）")
    private String validFlag;

    @ApiModelProperty("文件下载地址")
    private String downloadUrl;

    @ApiModelProperty("文件大小")
    private String fileSize;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sysCtime;

}