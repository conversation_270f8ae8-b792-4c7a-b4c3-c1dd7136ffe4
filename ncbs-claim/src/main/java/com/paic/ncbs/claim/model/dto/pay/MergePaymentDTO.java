package com.paic.ncbs.claim.model.dto.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MergePaymentDTO {
    private Integer id;
    private String batchNo;
    private String parterCode;
    private String parterName;
    private String payeeName;
    private BigDecimal sumAmount;
    private Integer sumCount;
    private String mergePaymentStatus;
    private String settlementStatus;
    private String errorMsg;
    private String isEffective;
    private String createdBy;
    private String updatedBy;
    private Date sysCtime;
    private Date sysUtime;

}
