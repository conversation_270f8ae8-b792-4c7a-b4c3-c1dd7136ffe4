package com.paic.ncbs.claim.sao.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.model.dto.reinsurance.IntfClaimMainDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.ReinsuranceRespDTO;
import com.paic.ncbs.claim.sao.AbstractBaseSAO;
import com.paic.ncbs.claim.sao.ReinsuranceSAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RefreshScope
@Service
public class ReinsuranceSAOImpl extends AbstractBaseSAO implements ReinsuranceSAO {

    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Override
    public ReinsuranceRespDTO repayCal(IntfClaimMainDTO dto) {
        log.info("ReinsuranceSAO.repayCal, url: {}, params: {}", thirdServiceConfig.getRepayCalUrl(), JSON.toJSONString(dto));

        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        HttpEntity<IntfClaimMainDTO> httpEntity = new HttpEntity<>(dto, header);
        ReinsuranceRespDTO respDto;
        if (switchMesh){
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            String result = MeshSendUtils.post(thirdServiceConfig.getRepayCalUrl(), JSON.toJSONString(dto),headers);
            respDto = JSON.parseObject(result,ReinsuranceRespDTO.class);
        }else {
            respDto = restTemplate.postForObject(thirdServiceConfig.getRepayCalUrl(), httpEntity, ReinsuranceRespDTO.class);
        }
        log.info("ReinsuranceSAO.repayCal, result: {}", JSON.toJSONString(respDto));
        return respDto;
    }
}
