package com.paic.ncbs.claim.service.investigate;


import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateCooperationCompanyVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public interface InvestigateService {


    String initInvestigate(InvestigateDTO investigate, UserInfoDTO u) throws GlobalBusinessException;

    void addIsFromOldSystem(Map<String, Object> extVariable, String reportNo);

    public String getIsOldInvestigate(String reportNO);

    void checkIsCanSendInvestigate(InvestigateDTO investigate) throws GlobalBusinessException;

    InvestigateVO getInvestigateById(String idAhcsInvestigate);

    List<InvestigateVO> getHistoryInvestigate(String reportNo, Integer caseTimes);

    List<InvestigateVO> getHistoryOutInvestigate(InvestigateDTO investigateDTO);
    List<AccidentSceneDto> getAccidentSceneData(String collectionCode);

    List<String> getCaseClassListCode(String reportNo, Integer caseTimes);

    InvestigateDTO getNoFinishInvestigateRecord(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    String getIsHaveEnteringFee(String reportNo, Integer caseTimes);

    boolean checkIsCanSendForMultiClaim(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigate(String idAhcsInvestigate);

    List<InvestigateCooperationCompanyVO> getCompanyByIdAhcsInvestigateTask(String idAhcsInvestigateTask);

    List<InvestigateVO> getInvestigateRecord(String reportNo, Integer caseTimes);

    void modifyInvestigateForOption(String idAhcsInvestigate, String feeAuditOption);

    void modifyInvestigateForFee(String idAhcsInvestigate, String isHasAdjustingFee, BigDecimal commonEstimateFee);

    InvestigateDTO getNoCommitData(String reportNo, Integer caseTimes);

    Integer getIvvesigateCount(String reportNo, Integer caseTimes);

    ResponseResult getExternalDepartmentList();

    /**
     * 根据报案号查询出出险人的证件号和保单号， 通过证件号和保单号查询所有理赔案件中存在在途调查任务的案件 报案号集合。
     */
    List<String> getUnfinishedInvestigateOther(String reportNo, Integer caseTimes);

    /**
     * 根据报案号查询出险人证件号；再通过证件号查询客户所有保单出险的理赔案件中存在调查结论异常的 报案号集合（没有保单条件，客户的所有理赔案件）
     */
    List<String> getAbnormalInvestigate(String reportNo);

    ResponseResult<Object> getServerInfoList();
}