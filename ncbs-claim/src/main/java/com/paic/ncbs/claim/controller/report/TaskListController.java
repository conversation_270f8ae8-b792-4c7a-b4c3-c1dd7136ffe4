package com.paic.ncbs.claim.controller.report;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentDuty;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentPlan;
import com.paic.ncbs.claim.dao.entity.report.ActivitiTaskUrlCfg;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkReqVo;
import com.paic.ncbs.claim.model.vo.openapi.GlobalWorkResVo;
import com.paic.ncbs.claim.model.vo.taskdeal.*;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Api(tags = "工作台-任务中心")
@RestController
@RequestMapping("/report/taskList")
public class TaskListController {

    @Autowired
    private TaskListService taskListService;

    @GetMapping(value = "/getUserTaskList")
    @ApiOperation(value = "查询当前用户的任务列表")
    public ResponseResult<Map<String, Object>> getUserTaskList(@ModelAttribute ActivitiTaskUrlCfg activitiTaskUrlCfg) {
        LogUtil.info("公共发方法中获取当前用户id，再根据任务类型taskType获取当前用户下的任务1459");

        Map<String, Object> param = bulidRequestMapParams();
        param.put("taskDefinitionBpmCode", activitiTaskUrlCfg.getTaskDefinitionBpmCode());
        param.put("isWorkTask", activitiTaskUrlCfg.getIsWorkTask());

        Map<String, Object> taskMap = taskListService.getUserTaskList_oc(param);

        return ResponseResult.success(taskMap);
    }

    @GetMapping(value = "/getUserTaskListNew")
    @ApiOperation(value = "查询当前用户的任务列表")
    public ResponseResult<List<WorkBenchTaskVO>> getUserTaskListNew(@ModelAttribute ActivitiTaskUrlCfg activitiTaskUrlCfg) {
        UserInfoDTO user = WebServletContext.getUser();
        WorkBenchTaskQueryVO workBenchTaskQueryVO = new WorkBenchTaskQueryVO();
        workBenchTaskQueryVO.setUserCode(user.getUserCode());
        workBenchTaskQueryVO.setIsMyCase(WorkBenchTaskQueryVO.MY_CASE_Y);
        workBenchTaskQueryVO.setIsIncludeSubordinates(WorkBenchTaskQueryVO.INCLUDE_SUBORDINATES_Y);
        // 包含下级机构
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        workBenchTaskQueryVO.setDepartmentCodes(departmentCodes);
//        workBenchTaskQueryVO.setDepartmentCode(WebServletContext.getDepartmentCode());
        workBenchTaskQueryVO.setTaskDefinitionBpmKey(activitiTaskUrlCfg.getTaskDefinitionBpmCode());
        return ResponseResult.success(taskListService.getSelfTaskList(workBenchTaskQueryVO));
    }

    @PostMapping(value = "/workBench/getTaskList")
    @ApiOperation(value = "工作台根据条件查询任务列表")
    public ResponseResult<Map<String, List<WorkBenchTaskVO>>> getWorkBenchTaskList(@RequestBody WorkBenchTaskQueryVO workBenchTaskQueryVO) throws Exception {
        Map<String, List<WorkBenchTaskVO>> workBenchTaskList = taskListService.getWorkBenchTaskList(workBenchTaskQueryVO);
        return ResponseResult.success(workBenchTaskList);
    }

    @PostMapping(value = "/workBench/getTaskCount")
    @ApiOperation(value = "工作台查询任务数量")
    public ResponseResult<TaskCountVO> getTaskCount(@RequestBody TaskCountVO taskCountVO) throws Exception {
        taskCountVO = taskListService.getTaskCount(taskCountVO);
        return ResponseResult.success(taskCountVO);
    }

    @GetMapping(value = "/workBench/globalQuery")
    @ApiOperation(value = "工作台查询global")
    public ResponseResult<GlobalTaskRespVO> globalQuery() throws Exception {
        GlobalTaskRespVO globalTaskRespVO = taskListService.workbenchGlobalQuery();
        return ResponseResult.success(globalTaskRespVO);
    }

    @GetMapping(value = "/workBench/globalJump")
    @ApiOperation(value = "工作台跳转global")
    public ResponseResult<String> globalJump(@RequestParam("reportNo") String reportNo) throws Exception {
        String resp = taskListService.jumpToGlobal(reportNo);
        return ResponseResult.success(resp);
    }

    @GetMapping(value = "/getTaskCountForUserId")
    @ApiOperation(value = "根据用户id统计任务次数")
    public ResponseResult<List<Map<String, Object>>> getTaskCountForUserId() {
        List<Map<String, Object>> taskCount = taskListService.getTaskCountByUserId_oc(new HashMap<>());
        return ResponseResult.success(taskCount);
    }

    @PostMapping(value = "/getAllMarketProductInfo")
    @ApiOperation(value = "获取所有产品信息")
    public ResponseResult<List<MarketProductInfoVO>> getAllMarketProductInfo(@RequestBody Map<String, String> map) {
        List<MarketProductInfoVO> marketProductInfoVOList = taskListService.getAllMarketProductInfo(map);
        return ResponseResult.success(marketProductInfoVOList);
    }

    @PostMapping(value = "/getAllRiskGroupInfo")
    @ApiOperation(value = "获取所有方案名称")
    public ResponseResult<List<GroupInfoVO>> getAllRiskGroupInfo(@RequestBody Map<String, String> map) {
        List<GroupInfoVO> groupInfoVOList = taskListService.getAllRiskGroupInfo(map);
        return ResponseResult.success(groupInfoVOList);
    }

    private Map<String, Object> bulidRequestMapParams() {
        String userId = WebServletContext.getUserId();
        Map<String, Object> param = new HashMap<>();
//        List<String> roleList = new ArrayList<>();
//        String roles = userService.getRolesList(userId);
//        if (roles != null) {
//            roleList = Arrays.asList(roles.split(","));
//        }
        param.put("userId", userId);
//        param.put("roleList", roleList);
//        param.put("envFlag", null);
        return param;
    }

}
