package com.paic.ncbs.claim.service.notice.impl;


import cn.wesure.httpclient.HttpResponse;
import cn.wesure.httpclient.WesureHttpClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseProcessMapper;
import com.paic.ncbs.claim.dao.mapper.indicator.CaseIndicatorMapper;
import com.paic.ncbs.claim.dao.mapper.notice.NoticePersonConfigMapper;
import com.paic.ncbs.claim.dao.mapper.notice.NoticePersonMapper;
import com.paic.ncbs.claim.dao.mapper.notice.NoticesMapper;
import com.paic.ncbs.claim.dao.mapper.trace.ClmsPersTraceMainMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticePersonConfigDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticePersonDTO;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.model.vo.notice.NoticesVO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceMainVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import com.google.common.base.Stopwatch;

import static com.paic.ncbs.claim.common.constant.IndicatorEnum.OUT_INVESTIGATE;


@Service
@Slf4j
@RefreshScope
public class NoticeServiceImpl implements NoticeService {
    @Autowired
    NoticePersonMapper noticePersonMapper;
    @Autowired
    NoticesMapper noticesMapper;
    @Autowired
    CacheService cacheService;
    @Autowired
    private CaseProcessMapper caseProcessDao;
    @Autowired
    private NoticePersonConfigMapper noticePersonConfDao;
    @Autowired
    private CaseIndicatorMapper caseIndicatorMapper;
    @Autowired
    private InvestigateService investigateService;
    @Value("${wechat.noticeUrl}")
    private String weiChatUrl;
    @Autowired
    private ClmsPersTraceMainMapper clmsPersTraceMainMapper;
    /**
     *生成提醒公共方法
     * noticesDTO 提醒信息
     * userId 接收人工号
     */
    @Override
    public void saveNotices(NoticesDTO noticesDTO,String userID) {
        saveNotices(noticesDTO,null,userID);
    }

    /**
     *生成提醒公共方法
     * noticesDTO 提醒信息
     * userId 接收人工号
     */
    @Override
    public void saveNotices(NoticesDTO noticesDTO,String payee,String userID) {
        //获取当前用户信息
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        //报案号
        String reportNo = noticesDTO.getReportNo();
        String id = UuidUtil.getUUID();
        noticesDTO.setId(id);//任务号
        noticesDTO.setCreatedBy(userInfoDTO.getUserCode());
        noticesDTO.setUpdatedBy(userInfoDTO.getUserCode());
        noticesDTO.setValidStatus("1");
        StringBuilder noticeContent = new StringBuilder();
        noticeContent.append(reportNo);
        if (BpmConstants.NOTICE_CLASS_OC_BACK.equals(noticesDTO.getNoticeClass())) { // 审批退回
            noticesDTO.setNoticeTitle("审批退回提醒");
            noticeContent.append(BpmConstants.NOTICE_SUB_CALSS_MAP.get(noticesDTO.getNoticeSubClass()));
            noticeContent.append("被");
            noticeContent.append(userInfoDTO.getUserName());
            noticeContent.append("审批退回");
        } else if (BpmConstants.NOTICE_CLASS_TASK.equals(noticesDTO.getNoticeClass())) { // 任务调度
            noticesDTO.setNoticeTitle("任务调度提醒");
            noticeContent.append(BpmConstants.TASK_MAP.get(noticesDTO.getNoticeSubClass()));
            noticeContent.append("任务调度给您，请您及时处理。");
            noticesDTO.setNoticeSubClass("");
        } else if (BpmConstants.NOTICE_CLASS_PAY_FALL.equals(noticesDTO.getNoticeClass())) { // 支付失败
            noticesDTO.setNoticeTitle("支付失败提醒");
            noticeContent.append("中的领款人：");
            noticeContent.append(payee);
            noticeContent.append("，支付失败，请及时处理。");
        }else if (BpmConstants.NOTICE_CLASS_PERSON_TRACE.equals(noticesDTO.getNoticeClass())) { // 人伤跟踪提醒
            noticesDTO.setNoticeTitle("人伤跟踪提醒");
            noticeContent.append("您有人伤跟踪任务需要继续跟踪处理，请您及时处理。");
        }
        noticesDTO.setNoticeContent(noticeContent.toString());
        UserInfoDTO userInfoDTO1 = null;
        if(userID != null && !userID.isEmpty()){
            try {
                //接收人
                userInfoDTO1 = cacheService.queryUserInfo(userID);
            }catch (Exception e) {
                log.info("saveNotices-获取接收人信息异常",e);
            }
        }
        String comCode = noticesDTO.getCompanyCode();
        if(BpmConstants.SOURCE_SYSTEM_OC.equals(noticesDTO.getSourceSystem())){
            CaseProcessDTO caseProcessDTO = caseProcessDao.getCaseCompanyCode(reportNo,noticesDTO.getCaseTimes());
            //案件归属机构
            comCode = caseProcessDTO.getCompanyCode();
            noticesDTO.setCompanyCode(comCode);
        }

        //不是内部人员，查询对应机构配置的默认提醒接收人
        if(userID==null||userID.isEmpty()||userInfoDTO1==null){
            //查询案件归属机构下的消息提醒接收人
            List<NoticePersonConfigDTO> noticePersons = noticePersonConfDao.findByComCode(comCode);
            if(noticePersons == null || noticePersons.isEmpty()){
                log.warn("未查询到运营系统对应处理人和默认处理人");
                return;
            }
            for(NoticePersonConfigDTO noticePerson : noticePersons){
                insertNoticePerson(id,noticePerson.getUserCode());
            }
        }else {
            insertNoticePerson(id,userID);
        }
        noticesMapper.insert(noticesDTO);
    }

    /**
     * 组装并插入消息提醒接收人
     * @param id
     * @param userId
     */
    private void insertNoticePerson(String id, String userId){
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        NoticePersonDTO noticePersonDTO = new NoticePersonDTO();
        noticePersonDTO.setId(UuidUtil.getUUID());
        noticePersonDTO.setIdClmsNotices(id);
        noticePersonDTO.setCreatedBy(userInfoDTO.getUserCode());
        noticePersonDTO.setUpdatedBy(userInfoDTO.getUserCode());
        noticePersonDTO.setRecipient(userId);
        noticePersonDTO.setReadStatus("0");
        noticePersonMapper.insert(noticePersonDTO);
    }

    /**
     * 查询消息提醒列表
     *
     * @param noticesVO 查询条件
     * @return
     */
    @Override
    public Map<String, List<NoticesVO>> getNotDealNoticeList(NoticesVO noticesVO) {
        UserInfoDTO user = WebServletContext.getUser();
        noticesVO.setRecipient(user.getUserCode());
        List<NoticesVO> noticeList = noticesMapper.getNoticeList(noticesVO);
        Map<String, List<NoticesVO>> groupedNotices = noticeList.stream()
                .collect(Collectors.groupingBy(NoticesVO::getNoticeClass));

        return groupedNotices;
    }

    @Override
    public void updatePersonStatus(String noticePersonId, String readStatus) {
        UserInfoDTO user = WebServletContext.getUser();
        noticePersonMapper.updatePersonStatus(noticePersonId,readStatus,user.getUserCode() );
    }



    @Override
    public List<NoticesVO> getDefNoticeList(){

        String userId = WebServletContext.getUserId();
        List<NoticesVO> noticesVOList = noticesMapper.getDefaultList(userId);
        return noticesVOList;
    }

    @Override
    public void batchDeleteNotices(List<String> noticePersonIds) {
        UserInfoDTO user = WebServletContext.getUser();
        for (String noticesId : noticePersonIds){
            noticePersonMapper.updatePersonStatus(noticesId,"3",user.getUserCode());
        }
    }

    @Override
    public void sendWechatMessage() {
        //五天后未回销的调查单
        int seconds = 5 * 24 * 60 * 60;
        //增加公估公司
        TpaServerInfoListVO tpaServerInfoList = null;
        try {
            tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        } catch (Exception e) {
            log.info("sendWechatMessage-获取公估公司信息异常", e);
        }

        List<ServerInfoVO> serverInfoList = new ArrayList<>();
        if (tpaServerInfoList != null) {
            serverInfoList = tpaServerInfoList.getServerInfoList();
        }
        if (ListUtils.isNotEmpty(serverInfoList)) {
            //循环serverInfoList
            for (ServerInfoVO serverInfo : serverInfoList) {
                if (serverInfo.getServerCode() != null && !serverInfo.getServerCode().equals("")) {
                    // 查询需要发送外部系统的消息提醒
                    List<String> reportNoList = caseIndicatorMapper.selectInvestigateReportNo(seconds, OUT_INVESTIGATE.getCode(), serverInfo.getServerCode());
                    if (reportNoList == null || reportNoList.isEmpty()) {
                        log.info("没有需要发送的消息提醒");
                    } else {
                        //拼接消息提醒内容
                        StringBuilder message = new StringBuilder();
                        for (String reportNo : reportNoList) {
                            message.append(reportNo);
                            message.append("、");
                        }
                        if (message.length() > 0) {
                            message.deleteCharAt(message.length() - 1); // 删除最后一个逗号
                        }
                        message.append("调查超5天未回销，请及时处理。调查公估公司：");
                        message.append(serverInfo.getServerName());
                        // 根据使用企业微信群的机器人助手发送消息提醒
                        try {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("msgtype", "text");
                            JSONObject textObject = new JSONObject();
                            textObject.put("content", message);
                            jsonObject.put("text", textObject);
                            String jsonString = jsonObject.toJSONString();
                            log.info("企业微信消息发送报文：{}", jsonString);
                            String response = post(weiChatUrl, jsonString,null);
                            log.info("企业微信消息返回报文：{}", response);
                        } catch (Exception e) {
                            log.info("sendWechatMessage-发送企业微信消息提醒异常", e);
                        }
                    }
                }
            }
        }

    }
    public static String post(String url, String body, Map<String, String> headers){
        log.info("mesh调用:url: {}, body: {}，headers：{}", url, body, headers);
        Stopwatch stopwatch = Stopwatch.createStarted();
        HttpResponse response;
        try {
            response = WesureHttpClient.post(url, body, headers);
            log.info("使用mesh调用外部接口 end,  usetime={}, return ===》\t {}", stopwatch.elapsed(TimeUnit.MILLISECONDS), JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("使用mesh调用外部接口,返回异常, error: ", e);
            return "";
        }
        if (!response.isSuccessful()){
            log.error("请求mesh失败,url: {}, code:{}, message: {}", url, response.getCode(), response.getErrorCause().getMessage());
            return "";
        }
        // 获取响应结果
        String result = response.getString();
        log.info("mesh调用结果: {}", result);
        return result;
    }
    @Override
    public void batchDeleteNoticeByTime() {
        int dateTime=90;
        //当前时间减90天
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate dateTimeD = today.minusDays(dateTime);
        noticePersonMapper.deletePersonByDate(dateTimeD);
        noticesMapper.deleteNoticesByDate(dateTimeD);
    }
}
