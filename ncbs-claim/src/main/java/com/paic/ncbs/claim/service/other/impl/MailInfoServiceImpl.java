package com.paic.ncbs.claim.service.other.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.other.MailInfoMapper;
import com.paic.ncbs.claim.model.dto.message.MailInfoDTO;
import com.paic.ncbs.claim.service.other.MailInfoService;
import com.paic.ncbs.message.compent.MailService;
import com.paic.ncbs.message.model.dto.MailSendParam;
import com.paic.ncbs.message.model.dto.SmsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class MailInfoServiceImpl implements MailInfoService {

    @Autowired
    private MailService mailService;
    @Autowired
    private MailInfoMapper mailInfoMapper;

    @Override
    @Async("asyncPool")
    public void sendMailByAsync(String reportNo,MailSendParam mailSendParam) {
        LogUtil.audit("发送邮件reportNo={}",reportNo);
        sendMailBySync(reportNo,mailSendParam);
    }

    @Override
    public void sendMailBySync(String reportNo,MailSendParam mailSendParam) {
        MailInfoDTO mailInfoDTO = buildMailInfoDTO(mailSendParam);
        addMailInfo(reportNo,mailInfoDTO);
        SmsResult result = null;
        try {
//            appendParam(mailSendParam);
            if(mailSendParam.getAttachments() == null || mailSendParam.getAttachments().size() < 1){
                LogUtil.audit("发送邮件param={}",JSON.toJSONString(mailSendParam));
                result =  mailService.sendMail(mailSendParam);
            }else{
                result = mailService.sendMailWithAttachment(mailSendParam);
            }
        }catch (Exception e){
            result = new SmsResult();
            result.setFailCode("error");
            result.setFailReason(e.getMessage());
        }
        result = Optional.ofNullable(result).orElse(new SmsResult());
        if( Constants.SMS_SEND_SUCCESS.equals(result.getIsSuccess())){
            mailInfoDTO.setIsSendSuccess(Constants.SMS_STATUS_SUCCESS);
        }else{
            mailInfoDTO.setIsSendSuccess(Constants.SMS_STATUS_FAIL);
            mailInfoDTO.setFileName(result.getFailCode()+":"+result.getFailReason());
            if(mailInfoDTO.getFileName().length() > 100){
                mailInfoDTO.setFileName(mailInfoDTO.getFileName().substring(0,100));
            }
        }
        mailInfoMapper.updateMailInfo(mailInfoDTO);

    }

    private void appendParam(MailSendParam mailSendParam){
//        mailSendParam.setTemplateType(Constants.MAIL_TEMPLATE_TYPE);
//        mailSendParam.setSystemCode(Constants.MAIL_SYSTEM_CODE);
//        mailSendParam.setBusinessCode(Constants.MAIL_BUSINESS_CODE);
    }

    private MailInfoDTO buildMailInfoDTO(MailSendParam mailSendParam){
        MailInfoDTO mailInfoDTO = new MailInfoDTO();
//        mailInfoDTO.setTemplateCode(mailSendParam.getTemplateName()==null ? "1":mailSendParam.getTemplateName());
//        mailInfoDTO.setSendTos(mailSendParam.getReceiver());
//        mailInfoDTO.setMailCc(mailSendParam.getCc());
        mailInfoDTO.setTitle(mailSendParam.getSubject());
        if(mailSendParam.getAttachments() != null && mailSendParam.getAttachments().size() > 0){
            StringBuilder builder = new StringBuilder();
//            mailSendParam.getAttachments().forEach((k,v)->{
//                builder.append(k);
//            });
            mailInfoDTO.setFilePath(builder.toString());
        }
        String content = "";
//        if(mailSendParam.getTemplateContent() != null && mailSendParam.getTemplateContent().size() > 0){
//            content = JSON.toJSONString(mailSendParam.getTemplateContent());
//        }
//        if(mailSendParam.getContent() != null){
//            content = content + "txt=" + mailSendParam.getContent();
//        }
        mailInfoDTO.setEmailContentMap(content);
        return mailInfoDTO;
    }

    public void addMailInfo(String reportNo,MailInfoDTO mailInfoDTO) {
        mailInfoDTO.setIsSendSuccess(Constants.SMS_STATUS_INIT);
        mailInfoDTO.setDepartmentCode(reportNo);
        if(mailInfoDTO.getCreatedBy() == null ){
            mailInfoDTO.setCreatedBy(ConstValues.SYSTEM);
        }
        if(mailInfoDTO.getUpdatedBy() == null ){
            mailInfoDTO.setUpdatedBy(ConstValues.SYSTEM);
        }
        if(mailInfoDTO.getIdAhcsMailNoticeInfo() == null){
            mailInfoDTO.setIdAhcsMailNoticeInfo(UuidUtil.getUUID());
        }
        if(mailInfoDTO.getTemplateCode() == null){
            mailInfoDTO.setTemplateCode(Constants.MAIL_DEFAULT_DATA);
        }
        if(mailInfoDTO.getFilePath() == null){
            mailInfoDTO.setFilePath(Constants.MAIL_DEFAULT_DATA);
        }
        if(mailInfoDTO.getFileName() == null){
            mailInfoDTO.setFileName(Constants.MAIL_DEFAULT_DATA);
        }

        mailInfoMapper.addMailInfo(mailInfoDTO);
    }

}
