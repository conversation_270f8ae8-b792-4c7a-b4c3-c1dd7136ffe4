package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SOP详情Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
public interface ClmsSopDetailMapper {

    /**
     * 根据主键查询
     *
     * @param idSopDetail 主键ID
     * @return SOP详情
     */
    ClmsSopDetail selectByPrimaryKey(String idSopDetail);

    /**
     * 根据SOP主表ID查询详情列表
     *
     * @param idSopMain SOP主表ID
     * @return SOP详情列表
     */
    List<ClmsSopDetail> selectByIdSopMain(String idSopMain);

    /**
     * 根据SOP主表ID和环节代码查询详情列表
     *
     * @param idSopMain SOP主表ID
     * @param taskBpmKey 环节代码
     * @return SOP详情列表
     */
    List<ClmsSopDetail> selectByIdSopMainAndtaskBpmKey(@Param("idSopMain") String idSopMain,
                                                       @Param("taskBpmKey") String taskBpmKey);

    /**
     * 插入记录
     *
     * @param record SOP详情
     * @return 影响行数
     */
    int insert(ClmsSopDetail record);

    /**
     * 选择性插入记录
     *
     * @param record SOP详情
     * @return 影响行数
     */
    int insertSelective(ClmsSopDetail record);

    /**
     * 批量插入记录
     *
     * @param records SOP详情列表
     * @return 影响行数
     */
    int batchInsert(List<ClmsSopDetail> records);

    /**
     * 根据主键选择性更新
     *
     * @param record SOP详情
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ClmsSopDetail record);

    /**
     * 根据主键更新
     *
     * @param record SOP详情
     * @return 影响行数
     */
    int updateByPrimaryKey(ClmsSopDetail record);

    /**
     * 根据SOP主表ID删除
     *
     * @param idSopMain SOP主表ID
     * @return 影响行数
     */
    int deleteByIdSopMain(String idSopMain);

    /**
     * 根据主键删除
     *
     * @param idSopDetail 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(String idSopDetail);
}