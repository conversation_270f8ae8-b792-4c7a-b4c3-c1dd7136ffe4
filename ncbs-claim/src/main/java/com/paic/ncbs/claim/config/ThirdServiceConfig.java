package com.paic.ncbs.claim.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Getter
public class ThirdServiceConfig {

    /**
     * 发送再保url
     */
    @Value("${ncbs.reinsurance.repayCalUrl:http://reins-main.lb.ssprd.com:48001/reins/claim/genReinsDangerUnit}")
    private String repayCalUrl;

}
