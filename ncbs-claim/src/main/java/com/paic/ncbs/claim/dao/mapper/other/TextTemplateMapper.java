package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TextTemplateMapper {
    int deleteByPrimaryKey(String idAhcsTextTemplate);

    int insert(TextTemplateEntity record);

    TextTemplateEntity selectByPrimaryKey(String idAhcsTextTemplate);

    /**
     * 根据：模板代码查询
     * @param templateCode
     * @param templateDesc 存在时模糊查询
     * @return
     */
    List<TextTemplateEntity> selectByTemplateCode(@Param("templateCode") String templateCode,
                                                  @Param("templateDesc") String templateDesc);

    /**
     * 查询短信标准模板
     */
    List<TextTemplateEntity> getStandardTemplate(TextTemplateEntity textTemplateEntity);

    int updateByPrimaryKeySelective(TextTemplateEntity record);

    int updateByPrimaryKey(TextTemplateEntity record);
}