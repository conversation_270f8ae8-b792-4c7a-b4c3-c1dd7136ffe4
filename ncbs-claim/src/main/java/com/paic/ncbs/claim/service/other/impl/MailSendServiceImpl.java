package com.paic.ncbs.claim.service.other.impl;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.other.MailInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.service.other.MailInfoService;
import com.paic.ncbs.claim.service.other.MailSendService;
import com.paic.ncbs.message.model.dto.MailSendParam;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RefreshScope
public class MailSendServiceImpl implements MailSendService {

    @Autowired
    private MailInfoMapper mailInfoMapper;

    @Autowired
    private MailInfoService mailInfoService;
    @Autowired
    private CacheService  cacheService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;

    @Value ("${mail.env}")
    private String mailEnv;

    @Override
    public void sendMajorCaseMail(String reportNo, String receiver,String cc) {
        try {
            mailInfoService.sendMailByAsync(reportNo,buildMajorCaseParam(reportNo,receiver,cc));
        }catch (Exception e){
            LogUtil.info("发送大案邮件失败"+reportNo ,e);
        }
    }

    @Override
    public void sendCaseMail(String reportNo,String receiver) {
        try {
            List<PolicyInfoDTO> policyList = mailInfoMapper.getMailPolicy(reportNo);
            if(ListUtils.isEmptyList(policyList)){
                return;
            }
            PolicyInfoDTO policyDTO = policyList.get(0);
            String email = Optional.ofNullable(cacheService.queryUserInfo(receiver)).map(UserInfoDTO::getEmail).orElse("");
            if(StringUtils.isEmptyStr(email)){
                LogUtil.info("收件人邮箱为空,reportNo="+reportNo);
                return;
            }
            mailInfoService.sendMailByAsync(reportNo,buildCaseMailParam(reportNo,email,policyDTO.getPolicyNo(),policyDTO.getDepartmentCode()));
        }catch (Exception e){
            LogUtil.info("发送普案邮件失败"+reportNo ,e);
        }
    }

    public MailSendParam buildMajorCaseParam(String reportNo, String receiver,String cc) {
        MailSendParam param = new MailSendParam();
        String title = String.format(Constants.MAJOR_CASE_MAIL_TITLE,reportNo);
        String content = Constants.MAJOR_CASE_MAIL_CONTENT;
        if("test".equals(mailEnv)){
            title = Constants.MAIL_TITLE_TIPS + title;
            content = content+Constants.MAIL_CONTENT_TIPS;
        }

        param.setSubject(title);
//        param.setContent(content);
//        param.setReceiver(receiver);
//        param.setCc(cc);
        return param;
    }

    public MailSendParam buildCaseMailParam(String reportNo, String receiver, String policyNo, String deptCode) {
        String deptName = departmentDefineMapper.queryDepartmentNameByDeptCode(deptCode);
        MailSendParam param = new MailSendParam();
        String title = String.format(Constants.CASE_MAIL_TITLE, reportNo);
        String content = String.format(Constants.CASE_MAIL_CONTENT,policyNo,deptCode+"-"+deptName);
        if("test".equals(mailEnv)){
            title = Constants.MAIL_TITLE_TIPS + title;
            content = content+Constants.MAIL_CONTENT_TIPS;
        }
        param.setSubject(title);
//        param.setContent(content);
//        param.setReceiver(receiver);
        return param;
    }

}
