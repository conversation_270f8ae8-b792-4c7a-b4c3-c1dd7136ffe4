package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanEntity;
import com.paic.ncbs.claim.dao.entity.duty.ReportPlanDutyVo;
import com.paic.ncbs.claim.model.dto.settle.PolicyPlanDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsPolicyPlanMapper extends BaseDao<AhcsPolicyPlanEntity> {

    int deleteByPrimaryKey(String idAhcsPolicyPlan);

    int insert(AhcsPolicyPlanEntity record);

    int insertSelective(AhcsPolicyPlanEntity record);

    AhcsPolicyPlanEntity selectByPrimaryKey(String idAhcsPolicyPlan);

    int updateByPrimaryKeySelective(AhcsPolicyPlanEntity record);

    int updateByPrimaryKey(AhcsPolicyPlanEntity record);

    int deleteByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    List<AhcsPolicyPlanEntity> selectByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    void insertList(@Param("list") List<AhcsPolicyPlanEntity> list);

    List<AhcsPolicyPlanEntity> selectPolicyInfo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("selfCardNo") String selfCardNo, @Param("planCode") String planCode);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    /**
     * 通过保单号查询方案编码
     * @param policyNo
     * @return
     */
    String getPolicyProductPackage(String policyNo);

    List<PolicyPlanDTO> getPolicyPlanInfo(@Param("reportNo") String reportNo);

    List<ReportPlanDutyVo> getPlanList(@Param("reportNo") String reportNo);
}