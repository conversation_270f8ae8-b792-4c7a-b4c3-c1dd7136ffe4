package com.paic.ncbs.claim.model.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@ApiModel("理赔明细信息")
@Data
public class BatchPaymentDetailInfo {
    @ApiModelProperty("支付ID号")
    private String businessNo;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("保单币别")
    private String currency;

    @ApiModelProperty("应付理赔金额(含税金额)")
    private BigDecimal planFee;

    @ApiModelProperty("理赔税额（元）险种代码下对应的税额")
    private BigDecimal taxFee;

    @ApiModelProperty("险种代码")
    private String riskCode;

    @ApiModelProperty("险种大类")
    private String kindCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品线编码")
    private String productLineCode;

}
