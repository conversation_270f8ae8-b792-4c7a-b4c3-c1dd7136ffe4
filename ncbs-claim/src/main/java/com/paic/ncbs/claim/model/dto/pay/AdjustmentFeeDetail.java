package com.paic.ncbs.claim.model.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: EX-TANGCHUNHUI001
 * @Description:
 * @Date: 2023-04-17 11:52
 */
@ApiModel("险种明细信息")
@Data
public class AdjustmentFeeDetail {

    @ApiModelProperty("险种代码")
    private String riskCode;

    @ApiModelProperty("理赔费用类型代码")
    private String feeTypeCode;

    @ApiModelProperty("明细金额")
    private BigDecimal payAmount;

    @ApiModelProperty("险种大类")
    private String kindCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("产品大类")
    private String productLineCode;
}
