package com.paic.ncbs.claim.service.risk.impl;

import com.paic.ncbs.claim.common.enums.ProductClassEnum;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.risk.RiskCheckService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class RiskCheckServiceImpl implements RiskCheckService {
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private AhcsPolicyInfoMapper policyInfoMapper;
    @Autowired
    private InvestigateService investigateService;
    @Autowired
    private ClmsSecondUnderwritingService secondUnderwritingService;

    /**
     * 检查案件被保险人公司内理赔风险：1、其它案件理赔时调查异常；2、其它案件理赔时存在二核拒保。
     *
     * @param reportNo
     * @return
     */
    @Override
    public String checkCustomerClaimRisk(String reportNo) {
        List<AhcsPolicyInfoEntity> policyInfoEntityList = policyInfoMapper.selectByReportNo(reportNo);
        if(CollectionUtils.isEmpty(policyInfoEntityList)){
            return "";
        }
        String msg ="";
        List<String> productClassList = new ArrayList<>();
        policyInfoEntityList.forEach(item -> {
            Map<String, String> plyBaseInfo = ocasMapper.getPlyBaseInfo(item.getPolicyNo());
            productClassList.add(plyBaseInfo.get("productClass"));
        });
        if (productClassList.contains(ProductClassEnum.PRODUCT_CLASS_02.getType())) {
            List<String> reprotNoList = new ArrayList<>();
            reprotNoList.addAll(investigateService.getAbnormalInvestigate(reportNo));
            reprotNoList.addAll(secondUnderwritingService.getRejectSecondUW(reportNo));
            if (CollectionUtils.isNotEmpty(reprotNoList)) {
                String otherReprotNos = reprotNoList.stream().distinct().collect(Collectors.joining("、"));
                // 该被保险人有调查中/二核在途案件，待前案完成后重新提交！   111111 前端不抛异常
                LogUtil.info("案件：{}出险人有历史案件调查异常件/历史案件二核结论拒保，案件号：{}", reportNo, otherReprotNos);
                msg = "历史案件调查异常件/历史案件二核结论拒保，是否继续提交！<br> 案件号：" + otherReprotNos;
            }
        }
        return msg;
    }

    /**
     * 校验其他案件未完成调查任务或者二核任务，哎代码重复就重复吧
     * @param reportNo
     * @param caseTimes
     */
    @Override
    public void checkOtherCaseUnfinishedTask(String reportNo, Integer caseTimes) {
        List<AhcsPolicyInfoEntity> policyInfoEntityList = policyInfoMapper.selectByReportNo(reportNo);
        if(CollectionUtils.isEmpty(policyInfoEntityList)){
            return;
        }
        List<String> productClassList = new ArrayList<>();
        policyInfoEntityList.forEach(item -> {
            Map<String, String> plyBaseInfo = ocasMapper.getPlyBaseInfo(item.getPolicyNo());
            productClassList.add(plyBaseInfo.get("productClass"));
        });

        if (productClassList.contains(ProductClassEnum.PRODUCT_CLASS_02.getType())) {
            List<String> reprotNoList = new ArrayList<>();
            reprotNoList.addAll(investigateService.getUnfinishedInvestigateOther(reportNo, caseTimes));
            reprotNoList.addAll(secondUnderwritingService.getUnfinishedSecondUWOther(reportNo,caseTimes));

            if (CollectionUtils.isNotEmpty(reprotNoList)) {
                String otherReprotNos = reprotNoList.stream().distinct().collect(Collectors.joining("、"));
                // 该被保险人有调查中/二核在途案件，待前案完成后重新提交！
                LogUtil.info("案件：{}出险人有调查中/二核在途案件，案件号：{}", reportNo, otherReprotNos);
                throw new GlobalBusinessException("该被保险人有调查中/二核在途案件，待前案完成后重新提交！<br> 案件号：" + otherReprotNos);
            }
        }
    }
}
