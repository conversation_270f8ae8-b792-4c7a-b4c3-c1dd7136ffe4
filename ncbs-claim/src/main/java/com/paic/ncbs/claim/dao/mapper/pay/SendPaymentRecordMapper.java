package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.model.dto.pay.SendMergePaymentRecordDTO;
import com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord;

import java.util.List;

public interface SendPaymentRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SendPaymentRecord record);

    int insertSelective(SendPaymentRecord record);

    SendPaymentRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SendPaymentRecord record);

    int updateByPrimaryKey(SendPaymentRecord record);

    /**
     * 根据支付流水号查询支付记录
     * @param paySerialNo
     * @return
     */
    List<SendPaymentRecord> selectByPaySerialNo(String paySerialNo);

    int insertSendMergePaymentRecord(SendMergePaymentRecordDTO sendMergePaymentRecordDTO);

}