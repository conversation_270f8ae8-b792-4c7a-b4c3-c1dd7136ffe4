package com.paic.ncbs.claim.service.pay.impl;

import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AntiMoneyLaunderingMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfo;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.prepay.PrePayService;
import com.paic.ncbs.claim.service.schedule.JobService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class PaymentInfoServiceImpl implements PaymentInfoService {
    @Autowired
    private PaymentInfoMapper paymentInfoMapper;
    @Autowired
    private PrePayService prePayService;
    @Autowired
    private CommonParameterMapper commonParameterMapper;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private CommonParameterService commonService;

    @Autowired
    AntiMoneyLaunderingMapper antiMoneyLaunderingMapper;

    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private JobService jobService;


    @Override
    public void addPaymentInfo(PaymentInfoDTO paymentInfoDTO) {
        //领款方式为“微信零钱”，openid必填 1-微信零钱，2-银行转账
        if(Objects.equals("1",paymentInfoDTO.getPayType())){
            if(StringUtils.isEmptyStr(paymentInfoDTO.getOpenId())){
                throw new GlobalBusinessException("领款方式为1-微信零钱，必须录入OpenID！");
            }
        }

        //  【支付信息录入】： 账号类型是个人的时候，同一“收款人姓名”+“证件类型”+证件号，不得录入多个户名
        if ("1".equals(paymentInfoDTO.getBankAccountAttribute())) {
            Integer paymentInfoForOnly = paymentInfoMapper.getPaymentInfoForOnly(paymentInfoDTO);
            if (paymentInfoForOnly>0) {
                throw new GlobalBusinessException("不允许录入多个相同户名！");
            }
        } else {
            Integer paymentInfoForComOnly = paymentInfoMapper.getPaymentInfoForComOnly(paymentInfoDTO);
            if (paymentInfoForComOnly>0) {
                throw new GlobalBusinessException("账号类型是公司时，名称和代码确保唯一！");
            }
        }

        if(StringUtils.isEmptyStr(paymentInfoDTO.getCreatedBy()) || StringUtils.isEmptyStr(paymentInfoDTO.getUpdatedBy())) {
            String userId = WebServletContext.getUserId();
            paymentInfoDTO.setCreatedBy(userId);
            paymentInfoDTO.setUpdatedBy(userId);
        }
        if(StringUtils.isEmptyStr(paymentInfoDTO.getIdClmPaymentInfo())){
            paymentInfoDTO.setIdClmPaymentInfo(UuidUtil.getUUID());
        }
        if(null == paymentInfoDTO.getDataSource()){
            paymentInfoDTO.setDataSource(Constants.DATA_SOURCE_DEFAULT);
        }
        if(null == paymentInfoDTO.getClientBankCode()){
            String bankCode = commonParameterMapper.getBankCodeByName(paymentInfoDTO.getClientBankName());
            paymentInfoDTO.setClientBankCode(bankCode);
        }
        if(null == paymentInfoDTO.getPaymentInfoType()){
            paymentInfoDTO.setPaymentInfoType("00");
        }
        if(null == paymentInfoDTO.getMigrateFrom()){
            paymentInfoDTO.setMigrateFrom(Constants.MIGRATE_FROM_DEFAULT);
        }
        if(null == paymentInfoDTO.getPaymentInfoStatus()){
            paymentInfoDTO.setPaymentInfoStatus(Constants.PAYMENT_INFO_STATUS_0);
        }
        if(null == paymentInfoDTO.getCollectPayApproach()){
            // 实时支付
            paymentInfoDTO.setCollectPayApproach("02");
        }
        if(Constants.PAYMENT_INFO_TYPE_03.equals(paymentInfoDTO.getPaymentInfoType())){
            //预赔次数
            paymentInfoDTO.setSubTimes(prePayService.getApprovedSubTimes(paymentInfoDTO.getReportNo(),paymentInfoDTO.getCaseTimes()));
        }
        getCustomerNo(paymentInfoDTO);
        paymentInfoMapper.addPaymentInfo(paymentInfoDTO);
    }

    /**
     * 调用crm接口获取客户号
     * @param paymentInfoDTO
     */
    @Override
    public void getCustomerNo(PaymentInfoDTO paymentInfoDTO) {
        String cardType=paymentInfoDTO.getClientCertificateType();
        String cardNo=paymentInfoDTO.getClientCertificateNo();
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),paymentInfoDTO.getBankAccountAttribute())){
            cardType=paymentInfoDTO.getCompanyCardType();
            cardNo=paymentInfoDTO.getOrganizeCode();
            log.info("公司"+cardNo+"证件类型"+cardType);
        }
        //调用crm接口
        String customerNo = customerInfoService.getCustomerNo(paymentInfoDTO.getClientName(),paymentInfoDTO.getBankAccountAttribute(),cardType,cardNo);
        log.info("客户为"+customerNo);
        paymentInfoDTO.setCustomerNo(customerNo);

    }

    @Override
    public List<PaymentInfoDTO> getPaymentInfo(PaymentInfoDTO paymentInfoDTO) {
        paymentInfoDTO.setPaymentInfoStatus(Constants.PAYMENT_INFO_STATUS_0);
        return paymentInfoMapper.getPaymentInfo(paymentInfoDTO);
    }

    @Override
    public List<PaymentInfo> getPaymentInfoByReportNo(String reportNo) {
        List<PaymentInfo> list = paymentInfoMapper.getPaymentInfoByReportNo(reportNo);
        if(list!=null && list.size()>0){
            for(PaymentInfo p :list){
                if("00".equals(p.getPaymentInfoType()) || p.getPaymentInfoType() == null){
                    p.setPaymentInfoTypeName("整案");
                }
                if("P1".equals(p.getPaymentUsage())){
                    p.setPaymentUsageName("赔款");
                }else{
                    p.setPaymentUsageName("费用");
                }
                p.setCollectPayApproachName("实时收付");
            }
        }
        return list;
    }

    @Override
    public PaymentInfoDTO getPaymentInfoById(String idClmPaymentInfo){
        return paymentInfoMapper.getPaymentInfoById(idClmPaymentInfo);
    }

    @Override
    public void updatePaymentInfo(PaymentInfoDTO paymentInfoDTO) {
        //领款方式为“微信零钱”，openid必填 1-微信零钱，2-银行转账
        if(Objects.equals("1",paymentInfoDTO.getPayType())){
            if(StringUtils.isEmptyStr(paymentInfoDTO.getOpenId())){
                throw new GlobalBusinessException("领款方式为1-微信零钱，必须录入OpenID！");
            }
        }
        //  【支付信息录入】： 账号类型是个人的时候，同一“收款人姓名”+“证件类型”+证件号，不得录入多个户名
        if ("1".equals(paymentInfoDTO.getBankAccountAttribute())) {
            Integer paymentInfoForOnly = paymentInfoMapper.getPaymentInfoForOnly(paymentInfoDTO);
            if (paymentInfoForOnly>0) {
                throw new GlobalBusinessException("不允许录入多个相同户名！");
            }
        } else {
            Integer paymentInfoForComOnly = paymentInfoMapper.getPaymentInfoForComOnly(paymentInfoDTO);
            if (paymentInfoForComOnly>0) {
                throw new GlobalBusinessException("账号类型是公司时，名称和代码确保唯一！");
            }
        }
        if(null == paymentInfoDTO.getUpdatedBy()){
            paymentInfoDTO.setUpdatedBy(WebServletContext.getUserId());
        }
        if(null == paymentInfoDTO.getClientBankCode()){
            String bankCode = commonParameterMapper.getBankCodeByName(paymentInfoDTO.getClientBankName());
            paymentInfoDTO.setClientBankCode(bankCode);
        }
        PaymentInfoDTO updateDto = getOldPaymentInfoById(paymentInfoDTO);
        paymentInfoMapper.updatePaymentInfo(updateDto);
        paymentItemMapper.updatePaymentItemByInfo(updateDto);
    }

    /**
     * 获取反洗钱信息
     * @param inDto
     */
    private PaymentInfoDTO getOldPaymentInfoById(PaymentInfoDTO inDto) {
        PaymentInfoDTO oldDto = paymentInfoMapper.getPaymentInfoById(inDto.getIdClmPaymentInfo());
        if(ObjectUtil.isEmpty(oldDto)){
            throw new GlobalBusinessException("更新失败领款人信息不存在");
        }
        log.info("更新前参数为={}", JsonUtils.toJsonString(oldDto));
        String cardType=inDto.getClientCertificateType();
        String cardNo=inDto.getClientCertificateNo();
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),inDto.getBankAccountAttribute())){
            cardType=inDto.getCompanyCardType();
            cardNo=inDto.getOrganizeCode();
            log.info("公司证件类号"+cardNo);
        }
        //调用crm接口
        String customerNo = customerInfoService.getCustomerNo(inDto.getClientName(),inDto.getBankAccountAttribute(),cardType,cardNo);
        log.info("客户为"+customerNo);

        BeanUtils.copyProperties(inDto,oldDto);
        oldDto.setCustomerNo(customerNo);
        log.info(inDto.getReportNo()+"更新后参数为={}", JsonUtils.toJsonString(oldDto));
        return  oldDto;

    }

    @Override
    public void delPaymentInfo(PaymentInfoVO paymentInfo) {
        PaymentInfoDTO paymentInfoDTO =new PaymentInfoDTO();
        BeanUtils.copyProperties(paymentInfo,paymentInfoDTO);
        //失效
        paymentInfoDTO.setPaymentInfoStatus(Constants.PAYMENT_INFO_STATUS_1);
        if(null == paymentInfoDTO.getUpdatedBy()){
            paymentInfoDTO.setUpdatedBy(WebServletContext.getUserId());
        }
        //更新状态为作废
        paymentInfoMapper.updateById(paymentInfoDTO);

    }

    @Override
    public List<PaymentItemComData> getPaymentInfoList(PaymentInfoDTO paymentInfoDTO) {
        return exchangeToComData(getPaymentInfo(paymentInfoDTO));
    }

    private List<PaymentItemComData> exchangeToComData(List<PaymentInfoDTO> payDtoList) {
        if (ListUtils.isEmptyList(payDtoList)) {
            return Collections.EMPTY_LIST;
        }
        List<PaymentItemComData> comDataList = new ArrayList<>();
        for (PaymentInfoDTO dto : payDtoList) {
            PaymentItemComData comData = new PaymentItemComData();
            BeanUtils.copyProperties(dto, comData);
            comData.setClientCertificateTypeName(CertificateTypeEnum.getName(dto.getClientCertificateType()));
            comData.setCollectPayApproachName(CollectPayApproachEnum.getName(dto.getCollectPayApproach()));
            comData.setPaymentInfoTypeName(PaymentInfoTypeEnum.getName(dto.getPaymentInfoType()));
            comData.setPaymentUsageName(PaymentInfoTypeEnum.getName(dto.getPaymentUsage()));
            comData.setIsMergePayName("Y".equals(dto.getIsMergePay()) ? "是" : "否");
            comDataList.add(comData);
        }
        return comDataList;
    }

    /**
     * 根据证件类型 证件号 姓名查询银行账号信息
     * @param paymentInfoVO
     * @return
     */
    @Override
    public PaymentInfoDTO getPaymentInfoByNameAndTypeAndCardNo(PaymentInfoVO paymentInfoVO) {
        PaymentInfoDTO returnDto = new PaymentInfoDTO();
        if(Objects.equals(BankAccountTypeEnum.BankAccountType_ZERO.getCode(),paymentInfoVO.getBankAccountAttribute())){
            returnDto = paymentInfoMapper.getCompanyPaymentInfoByNameAndTypeAndCardNo(paymentInfoVO);
        }else{
            returnDto = paymentInfoMapper.getIdvPaymentInfoByNameAndTypeAndCardNo(paymentInfoVO);
        }
        if(Objects.isNull(returnDto)){
           return null;
        }
        log.info("getPaymentInfoByNameAndTypeAndCardNo省"+returnDto.getProvinceName()+"市"+returnDto.getCityCode()+"县"+returnDto.getRegionCode());
        //省市县中文名称反显
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(returnDto.getProvinceName())
                .setAccidentCountyCode(returnDto.getRegionCode()).setAccidentCityCode(returnDto.getCityCode());
        AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
        log.info("支付信息省市县反显数据={}",JsonUtils.toJsonString(detailAdressFormCode));
        returnDto.setProName(detailAdressFormCode.getAccidentProvinceName());
        returnDto.setCiName(detailAdressFormCode.getAccidentCityName());
        returnDto.setCountryName(detailAdressFormCode.getAccidentCountyName());

        return returnDto;
    }

    @Override
    @Transactional
    public void mergePayCompensate(Date failDate) {
        //查询失败日期的数据，
        List<String> idList = paymentItemMapper.getMergePayCompensateId(failDate);
        if(!idList.isEmpty()){
            //更新clm_payment_item表
            paymentItemMapper.updateMergePaymentId(idList);
        }
        //重新推送
        jobService.mergePayment(failDate);
    }

}
