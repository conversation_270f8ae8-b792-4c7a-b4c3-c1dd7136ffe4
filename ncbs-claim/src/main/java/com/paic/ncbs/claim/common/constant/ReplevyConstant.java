package com.paic.ncbs.claim.common.constant;

public class ReplevyConstant {
    /**
     * 1-已申请，2-待审核，3-审核通过，4-退回
     */
    public static final String APPROVE_FLAG_APPROVE = "1";
    public static final String APPROVE_FLAG_BACK = "2";
    public static final String APPROVE_FLAG_CANCEL = "3";
    public static final String APPROVE_FLAG_FINISH = "4";
    /**
     * 关联实收类型 1-追偿、2-负数重开、3-共保回摊
     */
    public static final String RECEIPT_TYPE_RELEVY = "1";
    public static final String RECEIPT_TYPE_NEGATIVE = "2";

    /**
     * 冻结状态R-解冻
     */
    public static final String FREEZE_STATUS_R = "R";
    /**
     * 冻结状态F-冻结
     */
    public static final String FREEZE_STATUS_F = "F";
    /**
     * 追偿类型1-追偿
     */
    public static final String OPINION_TYPE_Z="1";
    /**
     * 追偿类型2-追偿费用
     */
    public static final String OPINION_TYPE_F="2";
}
