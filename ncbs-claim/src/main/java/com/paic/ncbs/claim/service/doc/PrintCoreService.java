package com.paic.ncbs.claim.service.doc;

import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-04-26 17:04
 */
public interface PrintCoreService {
    /**
     * 异步请求
     */
    void saveFileAsync(String docNo, String xslFileName, String before, PrintDutyPayVO printDutyPayVO);

    /**
     * 异步请求
     */
    void saveCommissionFileAsync(String docNo, String xslFileName, String before, PrintEntrustDTO printEntrustDTO);

    void saveFile(String docNo, String xslFileName, String before,PrintDutyPayVO printDutyPayVO);

    void saveCommissionFile(String docNo, String xslFileName, String before,PrintEntrustDTO printEntrustDTO);
}
