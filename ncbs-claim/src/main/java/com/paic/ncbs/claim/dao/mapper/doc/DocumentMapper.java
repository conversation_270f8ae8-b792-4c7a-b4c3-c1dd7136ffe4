package com.paic.ncbs.claim.dao.mapper.doc;

import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentGroupDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DocumentMapper {

    FileDocumentDTO selectByPrimaryKey(String documentGroupItemsId);


    void newDocumentGroup(FileDocumentGroupDTO documentGroupDTO);

    boolean disableDocument(String[] documentGroupItemIds);

    boolean disableDocumentByUm(String[] documentGroupItemIds, @Param("updatedBy") String umCode);

    boolean updateUploadedDocumentInfos(@Param("documentType") String smallCode, String[] documentGroupItemIds);

    /**
     * 根据documentId单证信息数据
     */
    FileDocumentDTO selectByDocumentId(@Param("documentId") String documentId);
}
