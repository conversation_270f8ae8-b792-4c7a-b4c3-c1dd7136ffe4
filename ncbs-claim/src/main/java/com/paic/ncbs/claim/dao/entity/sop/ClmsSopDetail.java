package com.paic.ncbs.claim.dao.entity.sop;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * SOP详情实体类
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
@Data
@ApiModel("SOP详情")
public class ClmsSopDetail extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String idSopDetail;

    @ApiModelProperty("SOP主表ID")
    private String idSopMain;

    @ApiModelProperty("适用环节（多个用逗号分隔）")
    private String taskBpmKeys;

    @ApiModelProperty("关联文件ID（多个用逗号分隔）")
    private String fileIds;

    @ApiModelProperty("SOP规则内容")
    private String sopContent;

    @ApiModelProperty("排序号")
    private Integer sortOrder;

    @ApiModelProperty("是否有效（Y/N）")
    private String validFlag;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime sysCtime;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("修改时间")
    private LocalDateTime sysUtime;
}