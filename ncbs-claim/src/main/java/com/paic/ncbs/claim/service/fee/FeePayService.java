package com.paic.ncbs.claim.service.fee;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import com.paic.ncbs.claim.model.vo.ahcs.FeeBigVO;
import com.paic.ncbs.claim.model.vo.ahcs.FeePaySumVO;
import com.paic.ncbs.claim.model.vo.ahcs.PayPrepayDetailVo;
import com.paic.ncbs.claim.model.vo.ahcs.PrePaymentVO;

import java.math.BigDecimal;
import java.util.List;

public interface FeePayService {

    /**
     * @description 根据报案号查出理赔费用信息
     */
    FeeBigVO getFeePays(String reportNo, Integer caseTimes, String isModifiedFlag, String claimType, String taskId, String replevyFlag) throws GlobalBusinessException;

    /**
     * @description 保存理赔费用信息
     */
    void saveFeePay(String reportNo,Integer caseTimes,List<FeePayDTO> feePayDTOS) throws Exception;

    /**
     * 根据id获取费用赔付信息
     */
    FeeInfoDTO getFeePayById(String idAhcsFeePay);

    /**
     * @description 根据理赔id删除理赔记录
     */
    void removeFeePay(String idAhcsFeePay, String reportNo, Integer caseTimes, FeeInfoDTO dto) throws GlobalBusinessException;


    /**
     * 根据支付信息id(CLM_PAYMENT_INFO表id)删除直接理赔信息记录
     */
    void removeFeePayByIdPayinfo(FeePayDTO feePayDTO) throws Exception;

    /**
     * 根据支付信息id(CLM_PAYMENT_INFO表id)删除直接理赔信息记录
     */
    void removeFeePayByIdPaymentInfo(String reportNo, Integer caseTimes, String idClmPaymentInfo) throws Exception;

    /**
     * 根据报案号赔付次数删除理赔信息
     */
    void removeFeePayByRnCt(String reportNo, Integer caseTimes) throws Exception;

    /**
     * 获取理赔费用总额
     */
    BigDecimal getSumFeePay(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    /**
     * 根据报案号，赔付次数、赔付类型查询赔付费用；
     */
    BigDecimal getFeePayAmount(FeePayDTO feePay);

    /**
     * @Description: 新增发票详细信息
     */
    void addInvoiceInfo(InvoiceInfoDTO invoiceInfo);

    /**
     * @Description: 获取发票详细信息
     */
    InvoiceInfoDTO getInvoiceInfo(InvoiceInfoDTO invoiceInfo);

    /**
     * 查询拒赔费用信息
     */
    FeePaySumVO getFeePayForSum(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    /**
     * @Description: 根据报案号赔付次数查询批次细信息
     */
    BatchDTO getBatchByReportNo(String reportNo, Integer caseTimes);

    /**
     * 置失效费用信息（命中反洗钱时调用）
     */
    void delFeeByReport(String reportNo, Integer caseTimes, String claimType);


    /**
     * 根据报案号和赔付次数查询赔付明细
     */
    List<FeeInfoDTO> getFeeByClaimType(String reportNo, Integer caseTimes, String claimType, Integer subTimes);

    /**
     * 获取案件费用类型及金额
     */
    List<FeePayDTO> getFeeTypeAmountByReportNo(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    List<FeeInfoDTO> getFeePayByParam(FeePayDTO feePay);

    /**
     * @Description: 查询保单的所有费用（不含奖励费）
     */
    BigDecimal getPolicyFeePayAmount(String reportNo, Integer caseTimes, String policyNo, String claimType);

    List<FeePayDTO> getPolicyDepartment(String reportNo, Integer caseTimes) throws GlobalBusinessException;

    List<PrePaymentVO> getPolicyPrePayFeeList(String reportNo, Integer caseTimes, Integer subTimes);

    List<FeePayDTO> getFeePaysForPrepay(FeePayDTO feePay) throws GlobalBusinessException;

    void delPrepayFee(List<String> clmPayItemIdList) throws GlobalBusinessException;

    List<InvoiceInfoDTO> getInvoiceByFeeIds(List<String> feeIdList);

    List<FeeInfoDTO> getPrePayFeeAmountByParam(FeePayDTO feePay);

    public void clearFee(String reportNo,Integer caseTimes );

    /**
     * 进项税费用发票退回修改
     * @param feeBigVO
     */
    void feeInvoiceBackResultModify(FeeBigVO feeBigVO);

    void feeInvoiceBackResultSubmit(String reportNo, Integer caseTimes, String idClmPaymentItem);

    /**
     * 根据报案号，赔付次数，预赔次数查询数据
     * @param reportNo
     * @param caseTimes
     * @param subTims
     * @return
     */
    PayPrepayDetailVo getPayPrepayDetail(String reportNo, Integer caseTimes, Integer subTims);

}