package com.paic.ncbs.claim.service.notice;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.vo.notice.NoticesVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface NoticeService {
    /**
     * 生成提醒信息
     * noticesDTO 提醒信息
     * userId 接收人工号
     */
    public void saveNotices(NoticesDTO noticesDto,String payee,String userId);

    /**
     * 生成提醒信息
     * noticesDTO 提醒信息
     * userId 接收人工号
     */
    public void saveNotices(NoticesDTO noticesDto,String userId);

    Map<String, List<NoticesVO>> getNotDealNoticeList(NoticesVO vo);

    /**
     * 修改接收人员状态
     * @param noticePersonId 接收人员id
     * @param readStatus 读取状态
     */
    void updatePersonStatus(String noticePersonId, String readStatus);


    public List<NoticesVO> getDefNoticeList();

    void batchDeleteNotices(List<String> noticesIds);
    /**
     * 发送微信消息
     */
    void sendWechatMessage();
    /**
     * 定时任务根据时间批量删除提醒信息
     */
    void batchDeleteNoticeByTime();

}
