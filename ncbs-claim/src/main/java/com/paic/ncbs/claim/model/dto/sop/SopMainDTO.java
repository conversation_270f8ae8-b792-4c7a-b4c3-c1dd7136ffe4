package com.paic.ncbs.claim.model.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SOP信息DTO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP信息")
public class SopMainDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("操作类型（01-暂存、02-发布、03-停用）")
    private String initFlag;

    @ApiModelProperty("主键id")
    private String idSopMain;

    @ApiModelProperty("sop名称")
    private String sopName;

    @ApiModelProperty("版本号")
    private String versionNo;

    @ApiModelProperty("sop简要描述")
    private String sopDescription;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("是否全流程（Y/N）")
    private String isAllProcess;

    @ApiModelProperty("SOP详情列表")
    private List<SopDetailDTO> detailList;

    @ApiModelProperty("发布人员")
    private String publisherCode;

    @ApiModelProperty("发布人员姓名")
    private String publisherName;

    @ApiModelProperty("是否有效（Y/N）")
    private String validFlag;

    @ApiModelProperty("状态（01-暂存、02-有效、03-无效）")
    private String status;

    @ApiModelProperty("文件类型（01-文本 02-文件 03-所有）")
    private String fileType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("SOP配置列表")
    private List<SopConfigDTO> configList;

    @ApiModelProperty("适用产品列表")
    private List<String> productCode;

    @ApiModelProperty("适用方案列表")
    private List<String> groupCode;

    @ApiModelProperty("适用险种列表")
    private List<String> planCode;

    @ApiModelProperty("适用环节列表")
    private List<String> taskBpmKey;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("文件类型名称")
    private String fileTypeName;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("修改人")
    private String updatedBy;

}