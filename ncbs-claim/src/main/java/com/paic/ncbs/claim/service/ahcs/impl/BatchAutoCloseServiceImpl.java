package com.paic.ncbs.claim.service.ahcs.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.AccidentTypeEnum;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyStatusEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyStatusEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.*;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ahcs.BatchAutoCloseMapper;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AmlNacMainMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.DiagnoseDefineMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.CityDefineMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.IhcsFeign;
import com.paic.ncbs.claim.model.dto.ahcs.*;
import com.paic.ncbs.claim.model.dto.ocas.PlyApplyFreeze;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.report.BatchReportEntity;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.ThirdPartyReportDetailQueryReqDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoExDTO;
import com.paic.ncbs.claim.model.vo.batch.AutoCloseRequestVO;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchAutoClose;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchAutoReportDTO;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchLinkMan;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchResponse;
import com.paic.ncbs.claim.model.vo.batch.OnlinePaymentInfo;
import com.paic.ncbs.claim.model.vo.openapi.OneTimeCloseCaseResponse;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseAsyncService;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseService;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseTransationService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.ParallelPolicyService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service("batchAutoCloseService")
@RefreshScope
@Slf4j
public class BatchAutoCloseServiceImpl implements BatchAutoCloseService {

    @Autowired
    private BatchAutoCloseMapper batchAutoCloseMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;
    @Autowired
    private DutyPayMapper dutyPayMapper;
    @Autowired
    private BatchAutoCloseTransationService batchAutoCloseTransationService;
    @Autowired
    private BatchAutoCloseAsyncService batchAutoCloseAsyncService;
    @Autowired
    private CommonParameterMapper commonParameterMapper;
    @Autowired
    private ParallelPolicyService parallelPolicyService;
    @Autowired
    private DiagnoseDefineMapper diagnoseDefineMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private CityDefineMapper cityDefineMapper;
    @Autowired
    private TaskListService taskListService;
    @Autowired
    private IhcsFeign ihcsFeign;
    @Autowired
    private AmlNacMainMapper nacMainMapper;

    @Autowired
    private RedissonClient redissonClient;

    // 立案人
    @Value("${return.insurance.batch.register:SYSTEM}")
    private String register;
    //报案人
    @Value("${return.insurance.batch.resporter:SYSTEM}")
    private String resporter;

    @Value("${query.parallel.enabled:true}")
    private boolean queryParallelEnabled;
    @Value("${save.parallel.enabled:true}")
    private boolean saveParallelEnabled;
    @Value("${save.batch.enabled:true}")
    private boolean saveBatchEnabled;
    @Value("${premission.department.enabled:true}")
    private boolean premissionDepartmentEnabled;
    @Autowired
    @Qualifier("asyncPool")
    private Executor executor;

    @Override
    public void saveBatchAutoClose(AhcsBatchEntity result, List<BatchAutoCloseDTO> batchCloseList, String md5) {
        Map<String,String> provinceMap = getProvinceMap();
        long time1 = System.currentTimeMillis();
        boolean pass = checkDataFormat(batchCloseList,provinceMap);
        LogUtil.audit("校验数据格式耗时={}",System.currentTimeMillis()-time1);
        if(!pass){
            result.setOperateShow("数据格式校验失败");
            result.setUpFileCodeDescribe("数据格式校验失败") ;
            return;
        }

        final List<BatchAutoReportDTO> autoReportList = new ArrayList<>();
        long time2 = System.currentTimeMillis();
        pass = checkDataBussiness(batchCloseList,autoReportList,provinceMap);
        LogUtil.audit("校验业务规则耗时={}",System.currentTimeMillis()-time2);
        if(!pass){
            result.setOperateShow("数据校验失败");
            result.setUpFileCodeDescribe("数据校验失败") ;
            return;
        }

        String batchNo = commonService.generateNo(NoConstants.BEATCH_REPORT_NO, VoucherTypeEnum.BARCH_REPORT_CASE_NO, ConfigConstValues.HQ_DEPARTMENT) ;
        long time3 = System.currentTimeMillis();
        pass = batchReport(autoReportList,batchNo,md5);
        LogUtil.audit("批量报案耗时={}",System.currentTimeMillis()-time3);
        if(!pass){
            result.setOperateShow("报案失败");
            result.setUpFileCodeDescribe("报案失败");
            return;
        }

        result.setReportBatchNo(batchNo);
        result.setUpFileStatus(ConstValues.SUCCESS);
        result.setUpFileCode(null);
        asyncBatchClose(batchNo,autoReportList);
        LogUtil.audit("批量结案上传成功batchNo={}",batchNo);
        return;
    }

    private boolean checkDataFormat(List<BatchAutoCloseDTO> batchCloseList,Map<String,String> provinceMap){
        Map<String,String> bankMap = getBankMap();
        Set<String> distinctSet = new HashSet<>();
        Set<String> serialSet = new HashSet<>();
        for (BatchAutoCloseDTO dto : batchCloseList) {
            boolean rowResult = true;
            dto.setMsg("");
            if(StringUtils.isEmptyStr(dto.getSerialNo())){
                setMsg(dto,"请填写序号");
                rowResult = false;
            }else{
                if(!dto.getSerialNo().matches(RapeRegexStrings.REG_NUMBER)){
                    setMsg(dto,"序号信息错误");
                    rowResult = false;
                }
                if(!serialSet.add(dto.getSerialNo())){
                    setMsg(dto,"序号不能重复");
                    rowResult = false;
                }
            }

            if(dto.getApplyAmount() != null){
                if(!BigDecimalUtils.compareBigDecimalPlus(dto.getApplyAmount(),BigDecimal.ZERO)){
                    setMsg(dto,"申请金额需大于0");
                    rowResult = false;
                }
            }
            if(dto.getBillAmount() != null){
                if(!BigDecimalUtils.compareBigDecimalPlus(dto.getBillAmount(),BigDecimal.ZERO)){
                    setMsg(dto,"发票金额需大于0");
                    rowResult = false;
                }
            }
            String insuredName = dto.getInsuredName();
            if(StringUtils.isEmptyStr(insuredName)){
                setMsg(dto,"请填写被保人姓名");
                rowResult = false;
            }else{
                if( insuredName.length() > 100 ){
                    setMsg(dto,"被保人姓名信息错误");
                    rowResult = false;
                }
            }

            if(StringUtils.isEmptyStr(dto.getCertificateNo())){
                setMsg(dto,"请填写被保人证件号");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getReporterName())){
                setMsg(dto,"请填写报案人姓名");
                rowResult = false;
            }else{
                if(dto.getReporterName().length() > 100 ){
                    setMsg(dto,"报案人姓名信息错误");
                    rowResult = false;
                }
            }

            if(StringUtils.isEmptyStr(dto.getLinkName())){
                setMsg(dto,"请填写联系人姓名");
                rowResult = false;
            }else{
                if(dto.getLinkName().length() > 100 ){
                    setMsg(dto,"联系人姓名信息错误");
                    rowResult = false;
                }
            }

            String phone = dto.getLinkPhone();
            if(StringUtils.isEmptyStr(dto.getLinkPhone())){
                setMsg(dto,"请填写联系电话");
                rowResult = false;
            }else{
                if(phone.length() != 11 || !phone.startsWith("1")|| !phone.matches(RapeRegexStrings.REG_NUMBER)){
                    setMsg(dto,"联系电话信息错误");
                    rowResult = false;
                }
            }

            if(StringUtils.isEmptyStr(dto.getPolicyNo())){
                setMsg(dto,"请填写保单号");
                rowResult = false;
            }else{
                if(StringUtils.isEmptyStr(ocasMapper.getPolicyNoFromBase(dto.getPolicyNo()))){
                    setMsg(dto,"未检索到保单号");
                    rowResult = false;
                }
            }

            if(StringUtils.isEmptyStr(dto.getAccidentTypeName())){
                setMsg(dto,"请填写事故类型");
                rowResult = false;
            }else{
                String accidentType = AccidentTypeEnum.getTypeByName(dto.getAccidentTypeName());
                if(StringUtils.isEmptyStr(accidentType)){
                    setMsg(dto,"事故类型信息错误");
                    rowResult = false;
                }
                dto.setAccidentType(accidentType);
            }

            if(StringUtils.isEmptyStr(dto.getCaseClassName())){
                setMsg(dto,"请填写出险类型");
                rowResult = false;
            }else{
                String caseClass = InsuredApplyTypeEnum.getType(dto.getCaseClassName());
                if(StringUtils.isEmptyStr(caseClass)){
                    setMsg(dto,"出险类型信息错误");
                    rowResult = false;
                }
                dto.setCaseClass(caseClass);
            }

            if(dto.getCertificateTypeName() != null){
                String cerType = CertificateTypeEnum.getType(dto.getCertificateTypeName());
                dto.setCertificateType(cerType == null ? "99" :cerType);
            }else{
                dto.setCertificateType("99");
            }

            if(null == dto.getAccidentDate()){
                setMsg(dto,"请填写事故日期");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getPlanCode())){
                setMsg(dto,"请填写险种编码");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getDutyCode())){
                setMsg(dto,"请填写责任编码");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getDutyDetailCode())){
                setMsg(dto,"请填写责任明细编码");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getDiagnoseCode())){
                setMsg(dto,"请填写诊断代码");
                rowResult = false;
            }else{
                String diagNo = diagnoseDefineMapper.getDiagnoseCode(dto.getDiagnoseCode());
                if(StringUtils.isEmptyStr(diagNo)){
                    setMsg(dto,"诊断代码信息错误");
                    rowResult = false;
                }
            }

            if(dto.getPayAmount() == null || !BigDecimalUtils.compareBigDecimalPlus(dto.getPayAmount(), BigDecimal.ZERO)){
                setMsg(dto,"赔款金额需大于0");
                rowResult = false;
            }else{
                if(checkPayAmountSuffix(dto.getPayAmount()) == false){
                    setMsg(dto,"赔款金额最多支持2位小数");
                    rowResult = false;
                }
            }

            if(StringUtils.isEmptyStr(dto.getClientName())){
                setMsg(dto,"请填写收款人姓名");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getClientCertificateNo())){
                setMsg(dto,"请填写收款人证件号");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getClientBankName())){
                setMsg(dto,"请填写收款人银行大类");
                rowResult = false;
            }else{
                String [] bankCodeArr = dto.getClientBankName().split("-");
                String bankName = bankMap.get(bankCodeArr[0]);
                if(StringUtils.isEmptyStr(bankName)){
                    setMsg(dto,"收款人银行大类信息错误");
                    rowResult = false;
                }
                dto.setClientBankCode(bankCodeArr[0]);
            }

            if(StringUtils.isEmptyStr(dto.getBankDetail())){
                setMsg(dto,"请填写开户银行全称");
                rowResult = false;
            }
            if(StringUtils.isEmptyStr(dto.getClientBankAccount())){
                setMsg(dto,"请填写银行账号");
                rowResult = false;
            }else{
                int bankAccLen = dto.getClientBankAccount().length();
                if(bankAccLen<10 || bankAccLen>30 || !dto.getClientBankAccount().matches(RapeRegexStrings.REG_NUMBER)){
                    setMsg(dto,"银行账号信息错误");
                    rowResult = false;
                }
            }

            if(StringUtils.isEmptyStr(dto.getProvinceName())){
                setMsg(dto,"请填写开户行所在省");
                rowResult = false;
            }else{
                if(!provinceMap.containsKey(dto.getProvinceName())){
                    setMsg(dto,"开户行所在省信息错误");
                    rowResult = false;
                }
                dto.setProvinceCode(dto.getProvinceName());
            }

            if(StringUtils.isEmptyStr(dto.getCityName())){
                setMsg(dto,"请填写开户行所在市");
                rowResult = false;
            }else{
                dto.setCityCode(dto.getCityName());
            }

            if(StringUtils.isEmptyStr(dto.getRegionName())){
                setMsg(dto,"请填写开户行所在区");
                rowResult = false;
            }else{
                dto.setRegionCode(dto.getRegionName());
            }

            if(dto.getAccidentDate() != null){
                dto.setAccidentDateStr(DateUtils.dateFormat(dto.getAccidentDate(),DateUtils.SIMPLE_DATE_STR));
                if(!distinctSet.add(dto.getInsuredName()+dto.getCertificateNo()+dto.getPolicyNo() +dto.getAccidentDateStr())){
                    setMsg(dto,"数据重复");
                    rowResult = false;
                }
            }

            if(dto.getPolicyNo() != null && dto.getCertificateNo() != null && dto.getInsuredName() != null){
                List<String> accidentDateStrList = policyInfoMapper.getAccidentDateStr(dto.getPolicyNo(),dto.getCertificateNo(),dto.getInsuredName());
                if(ListUtils.isNotEmpty(accidentDateStrList)){
                    for (String s : accidentDateStrList) {
                        if(dto.getAccidentDateStr().equals(s)){
                            setMsg(dto,"重复报案");
                            rowResult = false;
                        }
                    }
                }
            }

            if(rowResult == false){
                return false;
            }
        }
        return true;
    }

    private static boolean checkPayAmountSuffix(BigDecimal payAmout){
        String amountStr = payAmout.toString();
        int index = amountStr.indexOf(".");
        if(index > 0){
            String suffix = amountStr.substring(index);
            if(suffix.length() > 3){
                return false;
            }
        }
        return true;
    }


    private boolean checkDataBussiness(List<BatchAutoCloseDTO> batchCloseList,List<BatchAutoReportDTO> autoReportList,Map<String,String> provinceMap){
        Map<String, AhcsPolicyDomainDTO> policyMap = new HashMap<>();
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        if(queryParallelEnabled){
            policyMap = copyPolicyByParallel(batchCloseList);
        }
        for(BatchAutoCloseDTO dto : batchCloseList){
            AhcsDomainDTO domainDTO = new AhcsDomainDTO();
            List<AhcsPolicyDomainDTO> policyDomailList = new ArrayList<>();
            AhcsPolicyDomainDTO domain = null;
            if(queryParallelEnabled){
                domain = new AhcsPolicyDomainDTO();
                AhcsPolicyDomainDTO source = policyMap.get(dto.getPolicyNo()+dto.getCertificateNo()+dto.getInsuredName());
                if(source == null){
                    LogUtil.audit("抄单返回保单号不匹配");
                    setMsg(dto,"未查询到当前保单下的被保人姓名/证件号");
                    return false;
                }
                BeanUtils.copyProperties(source,domain);
                AhcsPolicyInfoEntity policyEntity = new AhcsPolicyInfoEntity();
                BeanUtils.copyProperties(domain.getAhcsPolicyInfo(),policyEntity);
                domain.setAhcsPolicyInfo(policyEntity);
            }else{
                domain = copyPolicy(dto);
            }
            policyDomailList.add(domain);
            if(domain.getAhcsPolicyInfo() == null || StringUtils.isEmptyStr(domain.getAhcsPolicyInfo().getPolicyNo())){
                setMsg(dto,"未检索到保单");
                return false;
            }
            String policyDepartmentCode = domain.getAhcsPolicyInfo().getDepartmentCode();
            if(premissionDepartmentEnabled){
                if( policyDepartmentCode == null || !departmentCodes.contains(policyDepartmentCode)){
                    setMsg(dto,"保单机构不在您的权限机构内");
                    return false;
                }
            }
            if(domain.getAhcsPolicyInfo().getInsuranceBeginTime().after(dto.getAccidentDate())
                    || domain.getAhcsPolicyInfo().getInsuranceEndTime().before(dto.getAccidentDate())){
                setMsg(dto,"事故日期不在保单生效范围内");
                return false;
            }
            if(ListUtils.isEmptyList(domain.getAhcsInsuredPresonDTOs())){
                setMsg(dto,"该保单系统内无被保人信息");
                return false;
            }

            matchInsured(domain,dto,domainDTO);

            if(null == domainDTO.getReportCustomerInfo()){
                setMsg(dto,"未查询到当前保单下的被保人姓名/证件号");
                return false;
            }
            String code = dto.getPlanCode() + dto.getDutyCode() + dto.getDutyDetailCode();
            if(!matchPlanDuty(domain,code)){
                setMsg(dto,"未查询到当前保单下的险种代码/责任代码/责任明细代码");
                return false;
            }
            MaxPayParam maxPayParam = new MaxPayParam();
            maxPayParam.setPolicyNo(dto.getPolicyNo());
            maxPayParam.setPlanCode(dto.getPlanCode());
            maxPayParam.setDutyCode(dto.getDutyCode());
            if(!checkPlanDutyMaxPay(maxPayParam,domainDTO.getReportCustomerInfo(),domain,dto.getPayAmount())){
                setMsg(dto,"剩余理赔金额不足");
                return false;
            }
            String deptCode = domain.getAhcsPolicyInfo().getDepartmentCode();
            if(deptCode!= null && deptCode.startsWith("211")){
                dto.setAccidentProvince("110000");
                dto.setAccidentCity("110100");
                dto.setAccidentCounty("110101");
            }else{
                if(StringUtils.isNotEmpty(dto.getAccidentProvinceName())){
                    String provinceCode = provinceMap.get(dto.getAccidentProvinceName());
                    if(StringUtils.isEmptyStr(provinceCode)){
                        setMsg(dto,"事故地点-省信息错误");
                        return false;
                    }
                    dto.setAccidentProvince(provinceCode);
                    if(StringUtils.isEmptyStr(dto.getAccidentCityName())){
                        setMsg(dto,"事故地点-市信息错误");
                        return false;
                    }
                    String cityCode = cityDefineMapper.getCityCodeByProvinceCityName(provinceCode,dto.getAccidentCityName());
                    if(StringUtils.isEmptyStr(cityCode)){
                        setMsg(dto,"事故地点-市信息错误");
                        return false;
                    }
                    dto.setAccidentCity(cityCode);
                    if(StringUtils.isEmptyStr(dto.getAccidentCountyName())){
                        setMsg(dto,"事故地点-区信息错误");
                        return false;
                    }

                    String countryCode = cityDefineMapper.getCountryCodeByProvinceCityName(provinceCode,dto.getAccidentCountyName());
                    if(StringUtils.isEmptyStr(countryCode)){
                        setMsg(dto,"事故地点-区信息错误");
                        return false;
                    }
                    dto.setAccidentCounty(countryCode);
                }else{
                    dto.setAccidentProvince("110000");
                    dto.setAccidentCity("110100");
                    dto.setAccidentCounty("110101");
                }
            }
            if(checkPolicyStatus(domain,dto) == false){
                return false;
            }

            domainDTO.setAhcsPolicyDomainDTOs(policyDomailList);
            BatchAutoReportDTO autoReportDTO = new BatchAutoReportDTO();
            autoReportDTO.setAhcsDomainDTO(domainDTO);
            autoReportDTO.setBatchAutoCloseDTO(dto);
            autoReportList.add(autoReportDTO);
        }
        return true;
    }

    private void matchInsured(AhcsPolicyDomainDTO domain,BatchAutoCloseDTO dto,AhcsDomainDTO domainDTO){
        for (AhcsInsuredPresonDTO insured : domain.getAhcsInsuredPresonDTOs()) {
            if(insured.getAhcsInsuredPreson()!= null){
                if(dto.getCertificateNo().equals(insured.getAhcsInsuredPreson().getCertificateNo())
                        && dto.getInsuredName().equals(insured.getAhcsInsuredPreson().getName())){
                    ReportCustomerInfoEntity customer = new ReportCustomerInfoEntity();
                    BeanUtils.copyProperties(insured.getAhcsInsuredPreson(),customer);
                    customer.setClientCluster(insured.getAhcsInsuredPreson().getPersonnelAttribute());
                    customer.setClientNo(insured.getAhcsInsuredPreson().getClientNo());
                    customer.setPersonnelCode(insured.getAhcsInsuredPreson().getPersonnelCode());
                    domainDTO.setReportCustomerInfo(customer);
                }
            }
        }
    }

    private boolean matchPlanDuty(AhcsPolicyDomainDTO domain,String code){
        for (AhcsPolicyPlanDTO plan : domain.getAhcsPolicyPlanDTOs()) {
            for (AhcsPolicyDutyDTO duty : plan.getAhcsPolicyDutyDTOs()) {
                for (AhcsPolicyDutyDetailEntity detail : duty.getAhcsPolicyDutyDetail()) {
                    if(code.equals(plan.getAhcsPolicyPlan().getPlanCode()+duty.getAhcsPolicyDuty().getDutyCode()+detail.getDutyDetailCode())){
                        domain.setBatchCloseDuty(duty.getAhcsPolicyDuty());
                        return true;
                    }
                }
            }
        }
        return false;
    }
    private boolean matchPlanDutyOnline(AhcsPolicyDomainDTO domain,String code){
        for (AhcsPolicyPlanDTO plan : domain.getAhcsPolicyPlanDTOs()) {
            for (AhcsPolicyDutyDTO duty : plan.getAhcsPolicyDutyDTOs()) {
                if(code.equals(plan.getAhcsPolicyPlan().getPlanCode()+duty.getAhcsPolicyDuty().getDutyCode())){
                    domain.setBatchCloseDuty(duty.getAhcsPolicyDuty());
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkPlanDutyMaxPay(MaxPayParam maxPayParam,ReportCustomerInfoEntity customer,AhcsPolicyDomainDTO domain,BigDecimal payAmount){
        AhcsPolicyDutyEntity dutyEntity = domain.getBatchCloseDuty();
        LogUtil.audit("dutyEntity={}",JSON.toJSONString(dutyEntity));

        if(!"1".equals(dutyEntity.getIsDutySharedAmount())){
            //1是标的共享
            maxPayParam.setInsuredCode(customer.getClientNo());
        }
        String dutyMerger = dutyEntity.getDutySharedAmountMerge();
        if(StringUtils.isNotEmpty(dutyMerger)){
            //责任共享
            maxPayParam.setDutyCode(null);
            maxPayParam.setDutyCodeList(Arrays.asList(dutyMerger.split(",")));
        }
        LogUtil.audit("maxPayParam={}",JSON.toJSONString(maxPayParam));
        BigDecimal hisAmt = BigDecimalUtils.nvl(dutyPayMapper.getDutyHistoryAmount(maxPayParam),"0");
        BigDecimal baseAmt = BigDecimalUtils.nvl(dutyEntity.getDutyAmount(),"0");
        LogUtil.audit("基本amt={},已赔amt={},本次amt={}",baseAmt,hisAmt,payAmount);
        if(BigDecimalUtils.compareBigDecimalPlusOrEqual(baseAmt.subtract(hisAmt),payAmount)){
            return true;
        }
        return false;
    }

    private void setReportNo(List<BatchAutoReportDTO> autoReportList,String reportBatchNo){
        for (BatchAutoReportDTO dto : autoReportList) {
            BatchAutoCloseDTO d = dto.getBatchAutoCloseDTO();
            d.setReportNo(dto.getAhcsDomainDTO().getReportNo());
            d.setBatchNo(reportBatchNo);
            d.setIdClmsBatchAutoClose(UuidUtil.getUUID());
            d.setCreatedBy(ConstValues.SYSTEM);
            d.setUpdatedBy(ConstValues.SYSTEM);
            d.setMsg(dto.getAhcsDomainDTO().getReportNo());
        }
    }

    private void removeReportNo(List<BatchAutoReportDTO> autoReportList){
        for (BatchAutoReportDTO dto : autoReportList) {
            BatchAutoCloseDTO d = dto.getBatchAutoCloseDTO();
            d.setMsg("报案失败");
        }
    }

    private AhcsPolicyDomainDTO copyPolicy(BatchAutoCloseDTO dto){
        CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(dto.getPolicyNo(),DateUtils.dateFormat(dto.getAccidentDate(),DateUtils.FULL_DATE_STR));
        queryVO.setCertNo(dto.getCertificateNo());
        queryVO.setClientName(dto.getInsuredName());
        return customerInfoStoreSAO.getPolicyDomainInfo(queryVO);
    }

    private Map<String,AhcsPolicyDomainDTO> copyPolicyByParallel(List<BatchAutoCloseDTO> closeList){

        List<CopyPolicyQueryVO> queryVOList = new ArrayList<>();
        Set<String> policyNoSet = new HashSet<>();
        //去重，相同的保单号和被保人只需调用一次接口
        for (BatchAutoCloseDTO dto : closeList) {
            if(!policyNoSet.add(dto.getPolicyNo()+dto.getCertificateNo()+dto.getInsuredName())){
                continue;
            }
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(dto.getPolicyNo(),DateUtils.dateFormat(dto.getAccidentDate(),DateUtils.FULL_DATE_STR));
            queryVO.setCertNo(dto.getCertificateNo());
            queryVO.setClientName(dto.getInsuredName());
            queryVOList.add(queryVO);
        }
        long time = System.currentTimeMillis();
        List<AhcsPolicyDomainDTO> policyDomainList = parallelPolicyService.queryPolicyDomainByParallel(queryVOList);
        LogUtil.audit("批量抄单耗时={}",System.currentTimeMillis()-time);
//        List<AhcsPolicyDomainDTO> policyDomainList = parallelPolicyService.getPolicyDomainByParallel(paramList);
        Map<String,AhcsPolicyDomainDTO> resultMap = new HashMap<>();
        if(ListUtils.isNotEmpty(policyDomainList)){
            for (AhcsPolicyDomainDTO domain : policyDomainList) {
                if(ListUtils.isNotEmpty(domain.getAhcsInsuredPresonDTOs())){
                    AhcsInsuredPresonEntity insured = domain.getAhcsInsuredPresonDTOs().get(0).getAhcsInsuredPreson();
                    resultMap.put(domain.getAhcsPolicyInfo().getPolicyNo()+insured.getCertificateNo()+insured.getName(),domain);
                }
            }

            //可能存在数组越界
//            resultMap = policyDomainList.stream().collect(Collectors.toMap(k->k.getAhcsPolicyInfo().getPolicyNo()+
//                    k.getAhcsInsuredPresonDTOs().get(0).getAhcsInsuredPreson().getCertificateNo()+
//                            k.getAhcsInsuredPresonDTOs().get(0).getAhcsInsuredPreson().getName()
//                    ,v->v));
        }
        return resultMap;
    }

    private boolean batchReport(List<BatchAutoReportDTO> autoReportList,String batchNo,String md5){
        try {
            long time1 = System.currentTimeMillis();
            buildReportParam(autoReportList);
            setReportNo(autoReportList,batchNo);
            BatchReportEntity entity = buildBatchReportEntity(batchNo,md5);
            long time2 = System.currentTimeMillis();
            LogUtil.audit("构建报案表数据耗时={}",time2-time1);

            if(saveBatchEnabled){
                batchAutoCloseTransationService.saveReportDomainBatch(buildReportDomain(autoReportList,entity));
            }else{
                if(saveParallelEnabled){
                    batchAutoCloseTransationService.saveReportDomainParallel(autoReportList,entity);
                }else{
                    batchAutoCloseTransationService.saveReportDomain(autoReportList,entity);
                }
            }
            return true;
        }catch (Exception e){
            LogUtil.error("批量自动报案失败:",e);
        }
        removeReportNo(autoReportList);
        return false;
    }

    private void buildReportParam(List<BatchAutoReportDTO> autoReportList){
        List<String> reportNoList = null;
        List<String> caseNoList = null;
        List<String> registNoList = null;
        if(queryParallelEnabled){
            List<String> deptCodeList = new ArrayList<>();
            autoReportList.forEach(e ->{
                deptCodeList.add(e.getAhcsDomainDTO().getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO)
                        .getAhcsPolicyInfo().getDepartmentCode());
            });

            reportNoList = parallelPolicyService.getNoByParallel(NoConstants.REPORT_NO, VoucherTypeEnum.REPORT_CASE_NO,deptCodeList);
            caseNoList = parallelPolicyService.getNoByParallel(NoConstants.CASE_NO, VoucherTypeEnum.CLAIM_NO,deptCodeList);
            registNoList = parallelPolicyService.getNoByParallel(NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO,deptCodeList);
        }

        for (int i=0; i<autoReportList.size();i++) {
            BatchAutoReportDTO reportDTO = autoReportList.get(i);
            AhcsDomainDTO ahcsDomainDTO = reportDTO.getAhcsDomainDTO();
            BatchAutoCloseDTO autoCloseDTO = reportDTO.getBatchAutoCloseDTO();
            ahcsDomainDTO.setClientNo(ahcsDomainDTO.getReportCustomerInfo().getClientNo());
            if(StringUtils.isEmptyStr(ahcsDomainDTO.getClientNo())){
                ahcsDomainDTO.setClientNo("1");
            }
            ahcsDomainDTO.setReportAcceptUm(ConstValues.SYSTEM);// 设置接报案人
            String departmentCode = ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode();
            ahcsDomainDTO.setAcceptDepartmentCode(departmentCode);// 接报案机构
            if (StringUtils.isEmptyStr(ahcsDomainDTO.getReportNo())) {
                if(queryParallelEnabled){
                    ahcsDomainDTO.setReportNo(reportNoList.get(i));
                }else{
                    ahcsDomainDTO.setReportNo(commonService.generateNo(NoConstants.REPORT_NO, VoucherTypeEnum.REPORT_CASE_NO,departmentCode)) ;
                }
            }
            buildReportLinkManInfo(ahcsDomainDTO,autoCloseDTO);
            buildReportInfo(ahcsDomainDTO,autoCloseDTO);
            buildReportInfoEx(ahcsDomainDTO,null,null);//默认人伤
            buildReportAccident(ahcsDomainDTO,autoCloseDTO);
            buildReportAccidentEx(ahcsDomainDTO,autoCloseDTO);
            buildWholeCaseBase(ahcsDomainDTO,"01");
            buildWholeCaseBaseEx(ahcsDomainDTO);
            String caseNo = Optional.ofNullable(caseNoList).orElse(new ArrayList<>()).get(i);
            String registNo = Optional.ofNullable(registNoList).orElse(new ArrayList<>()).get(i);
            buildCaseBase(ahcsDomainDTO,caseNo,registNo);
            buildReportExc(ahcsDomainDTO);
            buildCustomer(ahcsDomainDTO);
        }

    }

    private BatchReportEntity buildBatchReportEntity(String batchNo,String md5){
        String userId = WebServletContext.getUserId();
        BatchReportEntity batchReportEntity = new BatchReportEntity();
        batchReportEntity.setCreatedBy(userId);
        batchReportEntity.setUpdatedBy(userId);
        batchReportEntity.setImportUserId(userId);
        batchReportEntity.setReportBatchNo(batchNo);
        batchReportEntity.setFileMd5(md5);
        batchReportEntity.setBatchStatus("0");
        batchReportEntity.setBatchReportType("AHCS_BATCH_CLOSE_CASE");
        batchReportEntity.setIsMerge(ConstValues.NO);
        return batchReportEntity;
    }

    public void buildReportLinkManInfo(AhcsDomainDTO ahcsDomainDTO, BatchAutoCloseDTO autoCloseDTO) {
        List<LinkManEntity> linkMans = new ArrayList<>();
        LinkManEntity linkMan = new LinkManEntity();
        linkMan.setLinkManName(autoCloseDTO.getLinkName());
        linkMan.setLinkManTelephone(autoCloseDTO.getLinkPhone());
        linkMan.setLinkManRelation("00"); //本人
        linkMan.setApplicantPerson(autoCloseDTO.getInsuredName());
        linkMan.setApplicantType(ReportConstant.APPLICANT_TYPE_ONE);	//被保险人
        linkMan.setCertificateType(autoCloseDTO.getCertificateType());
        linkMan.setCertificateNo(autoCloseDTO.getCertificateNo());
        linkMan.setCreatedDate(new Date());
        linkMan.setUpdatedDate(new Date());
        linkMan.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        linkMan.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        linkMan.setIdAhcsLinkMan(UuidUtil.getUUID());
        linkMan.setIsReport(CommonConstant.YES);
        linkMan.setReportNo(ahcsDomainDTO.getReportNo());
        short i = 1;
        linkMan.setLinkManNo(i);
        linkMan.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        if (linkMan.getLinkManRelation() == null) {
            linkMan.setLinkManRelation("98");
        }
        linkMans.add(linkMan);
        ahcsDomainDTO.setLinkMans(linkMans);

    }

    public void buildReportInfo(AhcsDomainDTO ahcsDomainDTO,BatchAutoCloseDTO autoCloseDTO) {

        ReportInfoEntity reportInfo = ahcsDomainDTO.getReportInfo();
        if (reportInfo == null) {
            reportInfo = new ReportInfoEntity();
        }
        reportInfo.setReportNo(ahcsDomainDTO.getReportNo());
        reportInfo.setReportType(ReportConstant.NORMAL);// 正常报案
        reportInfo.setReportMode(ReportConstant.REPORT_MODE_COUNTER);// 报案来源
        List<LinkManEntity> linkManList = ahcsDomainDTO.getLinkMans();

        if (RapeCheckUtil.isListNotEmpty(linkManList)) {
            LinkManEntity linkMan = linkManList.get(0);
            reportInfo.setReporterCallNo(linkMan.getLinkManTelephone());
            reportInfo.setReporterRegisterTel(linkMan.getLinkManTelephone());
        }
        reportInfo.setReporterName(autoCloseDTO.getReporterName());
        reportInfo.setReportNo(ahcsDomainDTO.getReportNo());
        reportInfo.setIdClmReportInfo(UuidUtil.getUUID());
        reportInfo.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setCreatedDate(new Date());
        reportInfo.setUpdatedDate(new Date());
        if (reportInfo.getReportDate() == null) {
            reportInfo.setReportDate(new Date());
        }
        reportInfo.setReportRegisterUm(ahcsDomainDTO.getReportAcceptUm());

        reportInfo.setAcceptDepartmentCode(ahcsDomainDTO.getAcceptDepartmentCode());

        reportInfo.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        ahcsDomainDTO.setReportInfo(reportInfo);
    }

    public void buildReportInfoEx(AhcsDomainDTO ahcsDomainDTO, String threeSource,String thirdBatchNo) {
        List<ReportInfoExEntity> reportInfoExList = new ArrayList<ReportInfoExEntity>();
        List<LinkManEntity> linkManList = ahcsDomainDTO.getLinkMans();
        ReportInfoExEntity reportInfoEx = new ReportInfoExEntity();
        if (RapeCheckUtil.isListNotEmpty(ahcsDomainDTO.getReportInfoExs())) {
            reportInfoEx = ahcsDomainDTO.getReportInfoExs().get(0);
        }
        reportInfoEx.setSuccorService(CommonConstant.NO);

        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());
        reportInfoEx.setIsSpecialReport(ConstValues.NO);
        reportInfoEx.setReportNo(ahcsDomainDTO.getReportNo());
        reportInfoEx.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfoEx.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfoEx.setCreatedDate(new Date());
        reportInfoEx.setUpdatedDate(new Date());
        reportInfoEx.setCaseClass("2".equals(threeSource) ? BaseConstant.STRING_2: BaseConstant.STRING_1);// 美团点评为非人伤
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());

        if (RapeCheckUtil.isListNotEmpty(linkManList)) {
            LinkManEntity linkMan = linkManList.get(0);
            reportInfoEx.setLinkManName(linkMan.getLinkManName());
            if (linkMan.getLinkManRelation() == null) {
                linkMan.setLinkManRelation("98");
            }
            reportInfoEx.setLinkManRelation(linkMan.getLinkManRelation());
            reportInfoEx.setRelationWithReporter(linkMan.getApplicantType());
            reportInfoEx.setSendMessage(linkMan.getSendMessage());
        }
        reportInfoEx.setAcceptanceNumber(thirdBatchNo);

        reportInfoExList.add(reportInfoEx);
        ahcsDomainDTO.setReportInfoExs(reportInfoExList);
    }

    public void buildReportAccident(AhcsDomainDTO ahcsDomainDTO,BatchAutoCloseDTO autoCloseDTO) {
        ReportAccidentEntity reportAccident = Optional.ofNullable(ahcsDomainDTO.getReportAccident()).orElse(new ReportAccidentEntity());
        reportAccident.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccident.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccident.setCreatedDate(new Date());
        reportAccident.setUpdatedDate(new Date());
        reportAccident.setAccidentDate(autoCloseDTO.getAccidentDate());
        reportAccident.setAccidentPlace(autoCloseDTO.getAccidentProvinceName()+autoCloseDTO.getAccidentCityName()+autoCloseDTO.getAccidentCountyName());
        reportAccident.setIdClmReportAccident(UuidUtil.getUUID());
        reportAccident.setReportNo(ahcsDomainDTO.getReportNo());
        reportAccident.setAccidentCauseLevel1("意健险");
        reportAccident.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        ahcsDomainDTO.setReportAccident(reportAccident);
    }

    public void buildReportAccidentEx(AhcsDomainDTO ahcsDomainDTO,BatchAutoCloseDTO autoCloseDTO) {
        ReportAccidentExEntity reportAccidentEx = ahcsDomainDTO.getReportAccidentEx();
        if (reportAccidentEx == null) {
            reportAccidentEx = new ReportAccidentExEntity();
        }
        reportAccidentEx.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccidentEx.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccidentEx.setCreatedDate(new Date());
        reportAccidentEx.setUpdatedDate(new Date());
        reportAccidentEx.setAccidentType(autoCloseDTO.getAccidentType());
        reportAccidentEx.setIdAhcsReportAccidentEx(UuidUtil.getUUID());
        reportAccidentEx.setReportNo(ahcsDomainDTO.getReportNo());
        reportAccidentEx.setInsuredApplyStatus(InsuredApplyStatusEnum.END_OF_TREATMENT.getType());
        ahcsDomainDTO.setReportAccidentEx(reportAccidentEx);
    }

    public void buildWholeCaseBase(AhcsDomainDTO ahcsDomainDTO,String caseIdentification) {
        List<WholeCaseBaseEntity> wholeCaseBaseList = new ArrayList();
        WholeCaseBaseEntity wholeCaseBase = new WholeCaseBaseEntity();
        wholeCaseBaseList.add(wholeCaseBase);
        wholeCaseBase.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        wholeCaseBase.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        wholeCaseBase.setCreatedDate(new Date());
        wholeCaseBase.setUpdatedDate(new Date());
        wholeCaseBase.setIdClmWholeCaseBase(UuidUtil.getUUID());
        wholeCaseBase.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        wholeCaseBase.setAllowQuickFinish(CommonConstant.NO);
        wholeCaseBase.setReportNo(ahcsDomainDTO.getReportNo());
        wholeCaseBase.setReceiveVoucherUm(ahcsDomainDTO.getReportAcceptUm());
        wholeCaseBase.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        wholeCaseBase.setWholeCaseStatus(ReportConstant.REPROTED);
        wholeCaseBase.setDocumentFullDate(new Date());
        wholeCaseBase.setIsHugeAccident(ConstValues.NO);
        wholeCaseBase.setCaseType("01");
        wholeCaseBase.setIndemnityConclusion(SettleConst.INDEMNITY_MODE_PAY);
//        wholeCaseBase.setIsRegister(ConstValues.YES);
//        wholeCaseBase.setRegisterDate(new Date());
//        wholeCaseBase.setRegisterUm(register);
        wholeCaseBase.setCaseIdentification(caseIdentification);
        ahcsDomainDTO.setWholeCaseBase(wholeCaseBaseList);
    }

    public void buildWholeCaseBaseEx(AhcsDomainDTO ahcsDomainDTO) {
        List<WholeCaseBaseExEntity> wholeCaseBaseExList = new ArrayList();
        WholeCaseBaseExEntity wholeCaseBaseEx = new WholeCaseBaseExEntity();
        wholeCaseBaseExList.add(wholeCaseBaseEx);
        wholeCaseBaseEx.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        wholeCaseBaseEx.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        wholeCaseBaseEx.setCreatedDate(new Date());
        wholeCaseBaseEx.setUpdatedDate(new Date());
        wholeCaseBaseEx.setIdClmWholeCaseBase(ahcsDomainDTO.getWholeCaseBase().get(CommonConstant.ZERO).getIdClmWholeCaseBase());
        wholeCaseBaseEx.setIdClmWholeCaseBaseEx(UuidUtil.getUUID());
        wholeCaseBaseEx.setReportNo(ahcsDomainDTO.getReportNo());
        wholeCaseBaseEx.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        ahcsDomainDTO.setWholeCaseBaseEx(wholeCaseBaseExList);
    }

    public void buildCaseBase(AhcsDomainDTO ahcsDomainDTO, String caseNo, String registNo) {
        List<CaseBaseEntity> caseBaseList = ahcsDomainDTO.getCaseBases();
        if (caseBaseList == null) {
            caseBaseList = new ArrayList<>();
        }

        for (AhcsPolicyDomainDTO policyDomainDto : ahcsDomainDTO.getAhcsPolicyDomainDTOs()) {
            String departmentCode = policyDomainDto.getAhcsPolicyInfo().getDepartmentCode();
            CaseBaseEntity caseBase = new CaseBaseEntity();
            caseBase.setCreatedDate(new Date());
            caseBase.setUpdatedDate(new Date());
            caseBase.setReportNo(ahcsDomainDTO.getReportNo());
            if(StringUtils.isEmptyStr(caseNo)){
                caseNo = commonService.generateNo(NoConstants.CASE_NO, VoucherTypeEnum.CLAIM_NO,departmentCode);
            }
            if(StringUtils.isEmptyStr(registNo)){
                registNo =commonService.generateNo( NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO, departmentCode);
            }
            caseBase.setCaseNo(caseNo);
            caseBase.setRegistNo(registNo);
            policyDomainDto.getAhcsPolicyInfo().setCaseNo(caseNo);
            caseBase.setIdClmCaseBase(UuidUtil.getUUID());
            caseBase.setPolicyNo(policyDomainDto.getAhcsPolicyInfo().getPolicyNo());
            caseBase.setDepartmentCode(departmentCode);
            caseBase.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            caseBase.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            caseBase.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
            caseBase.setCaseStatus(ReportConstant.INIT_CASE_STATUS);
            caseBase.setMigrateFrom(CommonConstant.MIGRATE_FROM);
            policyDomainDto.getAhcsPolicyInfo().setCaseNo(caseNo);
            caseBaseList.add(caseBase);
        }

        ahcsDomainDTO.setCaseTimes(ReportConstant.INIT_CASE_TIMES);
        ahcsDomainDTO.setCaseBases(caseBaseList);
    }

    public void buildReportExc(AhcsDomainDTO ahcsDomainDTO) {
        ReportExcEntity reportExc = ahcsDomainDTO.getReportExc();
        if (reportExc != null) {
            reportExc.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportExc.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
            reportExc.setCreatedDate(new Date());
            reportExc.setUpdatedDate(new Date());
            reportExc.setIdReportExc(UuidUtil.getUUID());
        }

    }

    public void buildCustomer(AhcsDomainDTO ahcsDomainDTO){
        ReportCustomerInfoEntity reportCustomerInfo =  ahcsDomainDTO.getReportCustomerInfo();
        reportCustomerInfo.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportCustomerInfo.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportCustomerInfo.setCreatedDate(new Date());
        reportCustomerInfo.setUpdatedDate(new Date());
        reportCustomerInfo.setIdAhcsReportCustomer(UuidUtil.getUUID());
        reportCustomerInfo.setReportNo(ahcsDomainDTO.getReportNo());
    }

    private void setMsg(BatchAutoCloseDTO dto,String msg){
        msg = StringUtils.isEmptyStr(dto.getMsg()) ? msg : dto.getMsg()+";" + msg;
        dto.setMsg(msg);
    }

    @Override
    public void asyncBatchClose(String batchNo, List<BatchAutoReportDTO> autoReportList) {

        batchAutoCloseAsyncService.asyncBatchClose(batchNo,autoReportList);
    }

    private Map<String,String> getBankMap(){
        Map<String,String> bankMap = new HashMap<>();
        List<CommonParameterTinyDTO> payBankList = commonParameterMapper.getCommonParameterList(new String[] {"BANK_TYPE_BATCH"});
        if(ListUtils.isNotEmpty(payBankList)){
            bankMap = payBankList.stream().collect(Collectors.toMap(k->k.getValueCode(),v->v.getValueChineseName()));
        }
        return bankMap;
    }

    private Map<String,String> getProvinceMap(){
        Map<String,String> provinceMap = new HashMap<>();
        List<CommonParameterTinyDTO> provinceList = commonParameterMapper.getCommonParameterList(new String[] {"PC00"});
        if(ListUtils.isNotEmpty(provinceList)){
            provinceMap = provinceList.stream().collect(Collectors.toMap(k->k.getValueChineseName(),v->v.getValueCode()));
        }
        return provinceMap;
    }

    @Override
    public List<BatchReportTempEntity> getBatchCloseList(String batchNo) {
        List<BatchReportTempEntity> resultList = batchAutoCloseMapper.getBatchCloseList(batchNo);
        if(ListUtils.isEmptyList(resultList)){
            return new ArrayList<>();
        }
        return resultList;
    }

    @Override
    @Transactional
    public OnlineBatchResponse saveOnlineBatchAutoClose(AutoCloseRequestVO vo) {
        String thirdBatchNo = vo.getThirdBatchNo();
        List<OnlineBatchDTO> dtoList = batchAutoCloseMapper.getByThirdBatchNo(thirdBatchNo,null);
        List<Map<String, String>> resultMapList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            String batchNo = dtoList.get(0).getBatchNo();
            for (OnlineBatchDTO dto : dtoList) {
                Map<String, String> map = new HashMap<>();
                map.put("reportNo", dto.getReportNo());
                map.put("policyNo", dto.getPolicyNo());
                map.put("paySerialNo", dto.getPaySerialNo());
                map.put("paymentMode", dto.getPaymentMode());
                map.put("expressCompanyCode", dto.getExpressCompanyCode());
                resultMapList.add(map);
            }
            OnlineBatchResponse onlineBatchResponse = new OnlineBatchResponse();
            onlineBatchResponse.setBatchNo(batchNo);
            onlineBatchResponse.setReportInfos(resultMapList);
            onlineBatchResponse.setMsg("批量结案成功");
            return onlineBatchResponse;
        } else {
            executor.execute(() -> {
                saveOnlineBatchAutoCloseAsync(vo);
            });
            OnlineBatchResponse onlineBatchResponse = new OnlineBatchResponse();
            onlineBatchResponse.setMsg("该批次号正在处理中");
            return onlineBatchResponse;
        }
    }

    @Transactional
    private void saveOnlineBatchAutoCloseAsync(AutoCloseRequestVO vo){
        String thirdBatchNo = vo.getThirdBatchNo();
        RLock lock = redissonClient.getLock(RedisKeyConstants.getBatchCloseCaseLock(thirdBatchNo));
        try {
            if (lock.tryLock()) {
                List<Map<String, String>> resultMapList = new ArrayList<>();
                List<OnlineBatchAutoClose> batchCloseList = vo.getBatchCloseList();
                final List<OnlineBatchAutoReportDTO> autoReportList = new ArrayList<>();
                // 参数校验
                checkDataFormatOnline(batchCloseList);
                // 调用抄单构建批量报案对象集合 并做相应的业务校验
                bulidDataBussiness(batchCloseList, autoReportList);
                // 生成批次号
                String batchNo = commonService.generateNo(NoConstants.BEATCH_REPORT_NO, VoucherTypeEnum.BARCH_REPORT_CASE_NO, ConfigConstValues.HQ_DEPARTMENT);
                // 先批量报案
                batchReportOnline(autoReportList, batchNo, resultMapList, thirdBatchNo);

                batchAutoCloseAsyncService.asyncBatchCloseOnline(batchNo, autoReportList);
            }
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                log.info("退运险逻辑处理完成解锁，thirdBatchNo：{}", thirdBatchNo);
                lock.unlock();
            }
        }


    }

    @Override
    @Transactional
    public OneTimeCloseCaseResponse saveOnlineOneTimeCloseCase(AutoCloseRequestVO vo) {
        String thirdBatchNo = vo.getThirdBatchNo();
        Integer reopenNum = vo.getReopenNum();
        RLock lock = redissonClient.getLock(RedisKeyConstants.getBatchCloseCaseLock(thirdBatchNo));
        try {
            if (lock.tryLock()) {
                List<OnlineBatchDTO> dtoList = batchAutoCloseMapper.getByThirdBatchNo(thirdBatchNo,reopenNum);
                List<Map<String, String>> resultMapList = new ArrayList<>();
                OneTimeCloseCaseResponse result = new OneTimeCloseCaseResponse();
                String batchNo;

                if (CollectionUtils.isNotEmpty(dtoList)) {
                    OnlineBatchDTO dto = dtoList.get(0);
                    result.setThirdRequestNo(thirdBatchNo);
                    result.setReportNo(dto.getReportNo());
                    result.setCaseTimes("1");
                    result.setPaySerialNo(dto.getPaySerialNo());
                    result.setMsg("该案件已处理");
                } else {
                    List<OnlineBatchAutoClose> batchCloseList = vo.getBatchCloseList();
                    final List<OnlineBatchAutoReportDTO> autoReportList = new ArrayList<>();
                    // 参数校验
                    checkDataFormatOnline(batchCloseList);
                    // 调用抄单构建批量报案对象集合 并做相应的业务校验
                    bulidDataBussiness(batchCloseList,autoReportList);
                    // 生成批次号
                    batchNo = commonService.generateNo(NoConstants.BEATCH_REPORT_NO, VoucherTypeEnum.BARCH_REPORT_CASE_NO, ConfigConstValues.HQ_DEPARTMENT) ;
                    // 先批量报案
                    batchReportOnline(autoReportList, batchNo, resultMapList, thirdBatchNo);
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            // batchAutoCloseAsyncService.asyncBatchCloseOnline(batchNo,autoReportList);
                            batchAutoCloseAsyncService.batchCloseOnline(batchNo,autoReportList);
                        }
                    });
                    // 反洗钱做反洗钱黑名单校验
//                    checkOnlineBatchReportAml(batchCloseList);

                    Map<String, String> dealMap = resultMapList.get(0);
                    result.setThirdRequestNo(thirdBatchNo);
                    result.setReportNo(dealMap.get("reportNo"));
                    result.setCaseTimes("1");
                    result.setPaySerialNo(dealMap.get("paySerialNo"));
                }
                return result;
            } else {
                throw new GlobalBusinessException("该一步结案号正在处理中！");
            }
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional
    public OneTimeCloseCaseResponse saveOnlineOneTimeZeroCancel(AutoCloseRequestVO vo) {
        String thirdBatchNo = vo.getThirdBatchNo();
        Integer reopenNum = vo.getReopenNum();
        RLock lock = redissonClient.getLock(RedisKeyConstants.getBatchCloseCaseLock(thirdBatchNo));
        try {
            if (lock.tryLock()) {
                List<OnlineBatchDTO> dtoList = batchAutoCloseMapper.getByThirdBatchNo(thirdBatchNo,reopenNum);
                List<Map<String, String>> resultMapList = new ArrayList<>();
                OneTimeCloseCaseResponse result = new OneTimeCloseCaseResponse();
                String batchNo;

                if (CollectionUtils.isNotEmpty(dtoList)) {
                    OnlineBatchDTO dto = dtoList.get(0);
                    result.setThirdRequestNo(thirdBatchNo);
                    result.setReportNo(dto.getReportNo());
                    result.setCaseTimes("1");
                    result.setPaySerialNo(dto.getPaySerialNo());
                    result.setMsg("该一步零结案件已处理");
                } else {
                    List<OnlineBatchAutoClose> batchCloseList = vo.getBatchCloseList();
                    final List<OnlineBatchAutoReportDTO> autoReportList = new ArrayList<>();
                    final ServletRequestAttributes requestAttributes =
                            (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

                    // 零结参数校验
                    //checkDataFormatOnline(batchCloseList);
                    // 调用抄单构建批量报案对象集合 并做相应的业务校验
                    bulidDataBussiness(batchCloseList,autoReportList);
                    // 生成批次号
                    batchNo = commonService.generateNo(NoConstants.BEATCH_REPORT_NO, VoucherTypeEnum.BARCH_REPORT_CASE_NO, ConfigConstValues.HQ_DEPARTMENT) ;
                    // 先批量报案
                    batchReportOnline(autoReportList, batchNo, resultMapList, thirdBatchNo);
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            List<CompletableFuture<Void>> futureList = new ArrayList<>();
                            futureList.add(CompletableFuture.runAsync(() -> {
                                batchAutoCloseAsyncService.asyncOntTimeZeroCancelOnline(batchNo, autoReportList, requestAttributes);
                            }, executor));
                            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
                        }
                    });
                    Map<String, String> dealMap = resultMapList.get(0);
                    result.setThirdRequestNo(thirdBatchNo);
                    result.setReportNo(dealMap.get("reportNo"));
                    result.setCaseTimes("1");
                }
                return result;
            } else {
                throw new GlobalBusinessException("该请求号一步零结正在处理中！");
            }
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
      *
      * @Description  业务和产品下需求 需要根据小保单做反洗钱做反洗钱黑名单校验
      * 此逻辑没有任何实际意义，只是单纯为了实现业务要求
      * <AUTHOR>
      * @Date 2023/10/30 9:20
      **/
    @Async("asyncPool")
    public void checkOnlineBatchReportAml(List<OnlineBatchAutoClose> batchCloseList) {
        LogUtil.info("退运险反洗钱--start！");
        try {
            ThirdPartyReportDetailQueryReqDTO reqDTO = new ThirdPartyReportDetailQueryReqDTO();
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, -1);
            reqDTO.setReportTimeStart(calendar.getTime());
            reqDTO.setReportTimeEnd(calendar.getTime());
            String result = ihcsFeign.queryPolicyReportDetailDataList(reqDTO);
            if (StringUtils.isEmptyStr(result)) {
                LogUtil.info("未查到小保单，无需处理！");
                return;
            }
            JSONObject ihcsResult = JSON.parseObject(result);
            //ihcs 小保单数据
            JSONArray jar = ihcsResult.getJSONArray("data");
            if (jar.isEmpty()) {
                LogUtil.info("未查到小保单，无需处理！");
                return;
            }

            // 微保
            //OnlineBatchAutoClose wb = batchCloseList.stream().filter(b -> PAYMENTMODE_COMPANY_PAY.equals(b.getPaymentMode())).findFirst().orElse(null);
            // 顺丰
            //OnlineBatchAutoClose sf = batchCloseList.stream().filter(b -> PAYMENTMODE_COMPANY_PAY.equals(b.getPaymentMode())).findFirst().orElse(null);

            //组装公司组织代码与支付信息名称映射
            Map<String, String> paymentInfoMap = new HashMap<>();
            for (OnlineBatchAutoClose batchAutoClose: batchCloseList){
                if (!Objects.isNull(batchAutoClose.getPaymentInfo())){
                    paymentInfoMap.put(batchAutoClose.getPaymentInfo().getOrganizeCode(),batchAutoClose.getPaymentInfo().getClientName());
                }
            }
            //调用反洗钱
            paymentInfoMap.forEach((k, v)->{
                int moneyLaunderingBlackListCount = nacMainMapper.getMoneyLaunderingBlackListCount(v, k);
                if (moneyLaunderingBlackListCount > 0) {
                    LogUtil.error(v +"命中反洗钱黑名单，请排查，谨慎交易！");
                } else {
                    LogUtil.info(v +"未命中反洗钱黑名单！");
                }
            });

           // LogUtil.info("遍历得到支付信息：{}",JSON.toJSONString(paymentInfoMap));
          //  List<ThirdPartyReportDetailResponseDTO> responseDTOS = jar.toJavaList(ThirdPartyReportDetailResponseDTO.class);
            //遍历小保单数据
           /* responseDTOS.forEach(report -> {
                //LogUtil.debug("遍历小保单数据：{}",JSON.toJSONString(report));
                // 区分 微保 和 顺丰
                if (PAYMENTMODE_COMPANY_PAY.equals(report.getPaymentMethod()) && wb != null) {
                    // 微保 调用反洗钱黑名单接口看是否命中黑名单
                    OnlinePaymentInfo wbPaymentInfo = wb.getPaymentInfo();
                    String clientName = wbPaymentInfo.getClientName();
                    int moneyLaunderingBlackListCount = nacMainMapper.getMoneyLaunderingBlackListCount(clientName, wbPaymentInfo.getOrganizeCode());
                    if (moneyLaunderingBlackListCount > 0) {
                        LogUtil.error(clientName +"命中反洗钱黑名单，请排查，谨慎交易！");
                    } else {
                        LogUtil.info(clientName +"未命中反洗钱黑名单！");
                    }
                }
                if (PAYMENTMODE_OFFLINE_PAY.equals(report.getPaymentMethod())) {
                    //区分每家公司，进行黑名单校验
                    OnlinePaymentInfo paymentInfo = paymentInfoMap.get(report.getExpressCompanyCode());
                    if (paymentInfo != null){
                        String clientName =paymentInfo.getClientName();
                        int moneyLaunderingBlackListCount = nacMainMapper.getMoneyLaunderingBlackListCount(clientName, paymentInfo.getOrganizeCode());
                        if (moneyLaunderingBlackListCount > 0) {
                            LogUtil.error(clientName +"命中反洗钱黑名单，请排查，谨慎交易！");
                        } else {
                            LogUtil.info(clientName +"未命中反洗钱黑名单！");
                        }
                    }
                    //  顺丰 调用反洗钱黑名单接口看是否命中黑名单
                    *//*OnlinePaymentInfo sfPaymentInfo = sf.getPaymentInfo();
                    String clientName = sfPaymentInfo.getClientName();
                    int moneyLaunderingBlackListCount = nacMainMapper.getMoneyLaunderingBlackListCount(clientName, sfPaymentInfo.getOrganizeCode());
                    if (moneyLaunderingBlackListCount > 0) {
                        LogUtil.error(clientName+"命中反洗钱黑名单，请排查，谨慎交易！");
                    } else {
                        LogUtil.info(clientName+"未命中反洗钱黑名单！");
                    }*//*
                }
            });*/
        } catch (Exception e) {
            LogUtil.error("退运险调用反洗钱黑名单异常！",e);
        }
        LogUtil.info("退运险反洗钱--end！");
    }

    /**
      *
      * @Description 参数校验
      * <AUTHOR>
      * @Date 2023/7/20 20:04
      **/
    private void checkDataFormatOnline(List<OnlineBatchAutoClose> batchCloseList) {
        batchCloseList.forEach(close->{
            OnlinePaymentInfo paymentInfo = close.getPaymentInfo();
            //美团大众点评校验
            if ("3".equals(paymentInfo.getPayType()) && StringUtils.isEmptyStr(paymentInfo.getOpenId())){
                throw new GlobalBusinessException(close.getPolicyNo() + "美团支付时,openId不可为空");
            }
            //过滤美团大众点评的校验
            if (!ReportConstant.THREE_SOURCE_DIANPING.equals(close.getThreeSource()) && "0".equals(paymentInfo.getBankAccountAttribute()) &&
                    (StringUtils.isEmptyStr(paymentInfo.getBankDetail()) || (StringUtils.isEmptyStr(paymentInfo.getBankDetailCode()))
                      || StringUtils.isEmptyStr(paymentInfo.getClientBankAccount()) || StringUtils.isEmptyStr(paymentInfo.getClientBankCode())
                            || StringUtils.isEmptyStr(paymentInfo.getClientBankName())|| StringUtils.isEmptyStr(paymentInfo.getClientMobile()))
            ){
                throw new GlobalBusinessException(close.getPolicyNo() + "领款人支行编码和支行名称等信息不可为空");
            }
            LogUtil.audit("批量结案-checkDataFormatOnline- payAmount={}",paymentInfo.getPayAmount());
            if(paymentInfo.getPayAmount() == null || !BigDecimalUtils.compareBigDecimalPlus(paymentInfo.getPayAmount(), BigDecimal.ZERO)){
                throw new GlobalBusinessException(close.getPolicyNo() + "赔款金额需大于0");
            }else{
                if(!checkPayAmountSuffix(paymentInfo.getPayAmount())){
                    throw new GlobalBusinessException(close.getPolicyNo() + "赔款金额最多支持2位小数");
                }
            }
        });
    }

    /**
      *
      * @Description  批量报案入库
      * <AUTHOR>
      * @Date 2023/7/13 20:31
      **/
    private void batchReportOnline(List<OnlineBatchAutoReportDTO> autoReportList, String batchNo, List<Map<String, String>> resultMapList, String thirdBatchNo) {
        long time1 = System.currentTimeMillis();
        for (OnlineBatchAutoReportDTO reportDTO : autoReportList) {
            AhcsDomainDTO ahcsDomainDTO = reportDTO.getAhcsDomainDTO();
            LogUtil.audit("batchReportOnline-ahcsDomainDTO={}", JSON.toJSONString(ahcsDomainDTO));
            OnlineBatchAutoClose onlineBatchAutoClose = reportDTO.getOnlineBatchAutoClose();
            ahcsDomainDTO.setClientNo(ahcsDomainDTO.getReportCustomerInfo().getClientNo());
            ahcsDomainDTO.setReportAcceptUm(resporter);// 设置接报案人
            String departmentCode = ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(CommonConstant.ZERO).getAhcsPolicyInfo().getDepartmentCode();
            ahcsDomainDTO.setAcceptDepartmentCode(departmentCode);// 接报案机构
            String reportNo = commonService.generateNoOnline(NoConstants.REPORT_NO, VoucherTypeEnum.REPORT_CASE_NO, departmentCode);
            ahcsDomainDTO.setReportNo(reportNo);
            // 开始构建报案所需对象
            buildReportLinkManInfoOnline(ahcsDomainDTO, onlineBatchAutoClose);
            buildReportInfoOnline(ahcsDomainDTO, onlineBatchAutoClose);
            buildReportInfoEx(ahcsDomainDTO, onlineBatchAutoClose.getThreeSource(),thirdBatchNo);
            buildReportAccidentOnline(ahcsDomainDTO, onlineBatchAutoClose);
            buildReportAccidentExOnline(ahcsDomainDTO, onlineBatchAutoClose);
            String caseIdentification = ReportConstant.THREE_SOURCE_DIANPING.equals(onlineBatchAutoClose.getThreeSource()) ? "03":"02";
            buildWholeCaseBase(ahcsDomainDTO, caseIdentification);
            buildWholeCaseBaseEx(ahcsDomainDTO);
            String caseNo = commonService.generateNoOnline(NoConstants.CASE_NO, VoucherTypeEnum.CLAIM_NO, departmentCode);
            String registNo = commonService.generateNoOnline(NoConstants.REGIST_NO, VoucherTypeEnum.CASE_NO, departmentCode);
            buildCaseBase(ahcsDomainDTO, caseNo, registNo);
            buildReportExc(ahcsDomainDTO);
            buildCustomer(ahcsDomainDTO);
            Map<String, String> map = new HashMap<>();
            map.put("reportNo", reportNo);
            map.put("policyNo", onlineBatchAutoClose.getPolicyNo());
            map.put("paymentMode", onlineBatchAutoClose.getPaymentMode());
            // 赔款的唯一id
            String paySerialNo = UuidUtil.getUUID();
            map.put("paySerialNo", paySerialNo);
            map.put("expressCompanyCode", onlineBatchAutoClose.getExpressCompanyCode());
            reportDTO.getOnlineBatchAutoClose().getPaymentInfo().setPaySerialNo(paySerialNo);
            resultMapList.add(map);
        }

        long time2 = System.currentTimeMillis();
        LogUtil.audit("batchReportOnline-构建报案表数据耗时={}", time2 - time1);

        batchAutoCloseTransationService.saveReportDomainParallelOnline(autoReportList, batchNo, thirdBatchNo);
    }

    /**
      *
      * @Description 调用批改抄单接口 业务校验组装抄单结果 因为退运险没有批改
      * <AUTHOR>
      * @Date 2023/7/13 13:47
      **/
    private void bulidDataBussiness(List<OnlineBatchAutoClose> batchCloseList, List<OnlineBatchAutoReportDTO> autoReportList) {
        List<CopyPolicyQueryVO> queryVOList = new ArrayList<>();
//        Set<String> policyNoSet = new HashSet<>();
        //去重，相同的保单号和被保人只需调用一次接口
        for (OnlineBatchAutoClose dto : batchCloseList) {
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(dto.getPolicyNo(),DateUtils.dateFormat(dto.getAccidentDate(),DateUtils.FULL_DATE_STR));
            queryVO.setClientName(dto.getClientName());
            queryVO.setPaymentCompanyMode(dto.getExpressCompanyCode());
            queryVOList.add(queryVO);
        }
        long time = System.currentTimeMillis();
        List<AhcsPolicyDomainDTO> policyDomainList = parallelPolicyService.queryPolicyDomainByParallel(queryVOList);
        LogUtil.audit("bulidDataBussiness-批量抄单耗时={}",System.currentTimeMillis()-time);
        Map<String,AhcsPolicyDomainDTO> resultMap = new HashMap<>();
        if(ListUtils.isEmptyList(policyDomainList)){
            throw new GlobalBusinessException("退运险或点评调用抄单异常");
        }
        for (AhcsPolicyDomainDTO domain : policyDomainList) {
            if(ListUtils.isNotEmpty(domain.getAhcsInsuredPresonDTOs())){
                resultMap.put(domain.getAhcsPolicyInfo().getPolicyNo()+domain.getPaymentCompanyMode(),domain);
            }
        }
        // 遍历 构建报案对象集合
        for(OnlineBatchAutoClose dto : batchCloseList){
            AhcsDomainDTO domainDTO = new AhcsDomainDTO();
            List<AhcsPolicyDomainDTO> policyDomailList = new ArrayList<>();
            AhcsPolicyDomainDTO domain = resultMap.get(dto.getPolicyNo() + dto.getExpressCompanyCode());
            if(domain == null){
                throw new GlobalBusinessException("退运险调用抄单:" + dto.getPolicyNo() + "返回被保险人和报案不一致");
            }
            AhcsPolicyInfoEntity policyEntity = new AhcsPolicyInfoEntity();
            AhcsPolicyInfoEntity ahcsPolicyInfo = domain.getAhcsPolicyInfo();
            BeanUtils.copyProperties(ahcsPolicyInfo,policyEntity);
            domain.setAhcsPolicyInfo(policyEntity);
            policyDomailList.add(domain);

            Date accidentDate = dto.getAccidentDate();

            if(ahcsPolicyInfo.getInsuranceBeginTime().after(accidentDate)
                    || ahcsPolicyInfo.getInsuranceEndTime().before(accidentDate)){
                throw new GlobalBusinessException(dto.getPolicyNo() +"该保单事故日期不在保单生效范围内");
            }
            if(ListUtils.isEmptyList(domain.getAhcsInsuredPresonDTOs())){
                throw new GlobalBusinessException(dto.getPolicyNo() +"该保单系统内无被保人信息");
            }

            if(PolicyStatusEnum.TWO.getType().equals(ahcsPolicyInfo.getPolicyStatus())){
                //注销
                throw new GlobalBusinessException(dto.getPolicyNo() +"该保单系统内已经注销");
            }
            if (ahcsPolicyInfo.getInsuranceBeginTime().equals(ahcsPolicyInfo.getInsuranceEndTime())){
                // 未生效退保
                throw new GlobalBusinessException(dto.getPolicyNo() +"该保单系统内未生效退保");
            }

            if(PolicyStatusEnum.STOP.getType().equals(ahcsPolicyInfo.getPolicyStatus())){
                Date stopDate = ocasMapper.getPolicyStopDate(ahcsPolicyInfo.getPolicyNo());
                if(stopDate != null && stopDate.before(accidentDate)){
                    //中止：批改生效日期
                    throw new GlobalBusinessException(dto.getPolicyNo() +"该保单系统内已经中止");
                }

            }

            if(ListUtils.isNotEmpty(domain.getPlyApplyFreezes())){
                //冻结起止期
                for (PlyApplyFreeze freeze : domain.getPlyApplyFreezes()) {
                    if(freeze.getFreezeStarttime()==null || freeze.getFreezeEndtime()==null){
                        continue;
                    }
                    LocalDateTime accidentLocalDate = DateUtils.date2LocalDateTime(accidentDate);
                    if(freeze.getFreezeStarttime().isBefore(accidentLocalDate) && freeze.getFreezeEndtime().isAfter(accidentLocalDate)){
                        //在冻结期内
                        throw new GlobalBusinessException(dto.getPolicyNo() +"该保单系统内在冻结期内");
                    }
                }
            }
            // 删除批改在途
            // if (ConfigConstValues.YES.equals(domain.getIsProcess()) && !ConstValues.SCENE20022.equals(domain.getScenceList())) {
            //     throw new GlobalBusinessException(dto.getPolicyNo() +"批改在途");
            // }

            // 退运险应该只有一个被保人
            for (AhcsInsuredPresonDTO insured : domain.getAhcsInsuredPresonDTOs()) {
                if(insured.getAhcsInsuredPreson()!= null && dto.getClientName().equals(insured.getAhcsInsuredPreson().getName())){
                    ReportCustomerInfoEntity customer = new ReportCustomerInfoEntity();
                    BeanUtils.copyProperties(insured.getAhcsInsuredPreson(),customer);
                    customer.setClientCluster(insured.getAhcsInsuredPreson().getPersonnelAttribute());
                    customer.setClientNo(insured.getAhcsInsuredPreson().getClientNo());
                    customer.setPersonnelCode(insured.getAhcsInsuredPreson().getPersonnelCode());
                    domainDTO.setReportCustomerInfo(customer);
                    break;
                }
            }

            String code = dto.getPlanCode() + dto.getDutyCode();
            if(!matchPlanDutyOnline(domain,code)){
                throw new GlobalBusinessException(dto.getPolicyNo() +"未查询到当前保单下的险种代码/责任代码");
            }
            MaxPayParam maxPayParam = new MaxPayParam();
            maxPayParam.setPolicyNo(dto.getPolicyNo());
            maxPayParam.setPlanCode(dto.getPlanCode());
            maxPayParam.setDutyCode(dto.getDutyCode());
            if(!checkPlanDutyMaxPay(maxPayParam,domainDTO.getReportCustomerInfo(),domain,dto.getPaymentInfo().getPayAmount())){
                throw new GlobalBusinessException(dto.getPolicyNo() +"该保单剩余理赔金额不足");
            }
            domainDTO.setAhcsPolicyDomainDTOs(policyDomailList);
            OnlineBatchAutoReportDTO autoReportDTO = new OnlineBatchAutoReportDTO();
            autoReportDTO.setAhcsDomainDTO(domainDTO);
            autoReportDTO.setOnlineBatchAutoClose(dto);
            autoReportList.add(autoReportDTO);
        }
    }

    private boolean checkPolicyStatus(AhcsPolicyDomainDTO domain,BatchAutoCloseDTO dto){
        AhcsPolicyInfoEntity policy = domain.getAhcsPolicyInfo();
        Date accidentDate = dto.getAccidentDate();
        if(PolicyStatusEnum.TWO.getType().equals(policy.getPolicyStatus())){
            //注销
            setMsg(dto,"保单已注销");
            return false;
        }
        if (policy.getInsuranceBeginTime().after(accidentDate) || policy.getInsuranceEndTime().before(accidentDate)){
            //不在起止期内
            setMsg(dto,"事故日期超出保单起止期");
            return false;
        }


        if(PolicyStatusEnum.STOP.getType().equals(policy.getPolicyStatus())){
            Date stopDate = ocasMapper.getPolicyStopDate(policy.getPolicyNo());
            if(stopDate != null && stopDate.before(accidentDate)){
                //中止：批改生效日期
                setMsg(dto,"事故日期在保单中止期之后");
                return false;
            }
        }

        if(ListUtils.isNotEmpty(domain.getPlyApplyFreezes())){
            //冻结起止期
            for (PlyApplyFreeze freeze : domain.getPlyApplyFreezes()) {
                if(freeze.getFreezeStarttime()==null || freeze.getFreezeEndtime()==null){
                    continue;
                }
                LocalDateTime accidentLocalDate = DateUtils.date2LocalDateTime(accidentDate);
                if(freeze.getFreezeStarttime().isBefore(accidentLocalDate) && freeze.getFreezeEndtime().isAfter(accidentLocalDate)){
                    //在冻结期内
                    setMsg(dto,"事故日期在保单冻结期间");
                    return false;
                }
            }

        }
        return true;
    }

    private BatchEntityDTO buildReportDomain(List<BatchAutoReportDTO> autoReportList,BatchReportEntity batchReportEntity){
        long time1 = System.currentTimeMillis();

        BatchEntityDTO batchEntityDTO = new BatchEntityDTO();
        List<BatchAutoCloseDTO> closeList = new ArrayList<>();
        List<ReportInfoEntity> reportInfoList = new ArrayList<>();
        List<ReportInfoExEntity> reportInfoExList = new ArrayList<>();
        List<ReportAccidentEntity> reportAccidentList = new ArrayList<>();
        List<ReportAccidentExEntity> reportAccidentExList = new ArrayList<>();
        List<CaseBaseEntity> caseBaseList = new ArrayList<>();
        List<LinkManEntity> linkManList = new ArrayList<>();
        List<WholeCaseBaseEntity> wholeCaseBaseList = new ArrayList<>();
        List<WholeCaseBaseExEntity> wholeCaseBaseExList = new ArrayList<>();
        List<ReportCustomerInfoEntity> reportCustomerInfoList = new ArrayList<>();

        for (BatchAutoReportDTO reportDTO : autoReportList) {
            closeList.add(reportDTO.getBatchAutoCloseDTO());
            AhcsDomainDTO ahcsDomainDTO = reportDTO.getAhcsDomainDTO();
            reportInfoList.add(ahcsDomainDTO.getReportInfo());
            reportInfoExList.addAll(ahcsDomainDTO.getReportInfoExs());
            reportAccidentList.add(ahcsDomainDTO.getReportAccident());
            reportAccidentExList.add(ahcsDomainDTO.getReportAccidentEx());
            caseBaseList.addAll(ahcsDomainDTO.getCaseBases());
            linkManList.addAll(ahcsDomainDTO.getLinkMans());
            wholeCaseBaseList.addAll(ahcsDomainDTO.getWholeCaseBase());
            wholeCaseBaseExList.addAll(ahcsDomainDTO.getWholeCaseBaseEx());
            reportCustomerInfoList.add(ahcsDomainDTO.getReportCustomerInfo());
            buildPolicyDomain(ahcsDomainDTO,batchEntityDTO);
        }
        batchEntityDTO.setCloseList(closeList);
        batchEntityDTO.setReportInfoList(reportInfoList);
        batchEntityDTO.setReportInfoExList(reportInfoExList);
        batchEntityDTO.setReportAccidentList(reportAccidentList);
        batchEntityDTO.setReportAccidentExList(reportAccidentExList);
        batchEntityDTO.setCaseBaseList(caseBaseList);
        batchEntityDTO.setLinkManList(linkManList);
        batchEntityDTO.setWholeCaseBaseList(wholeCaseBaseList);
        batchEntityDTO.setWholeCaseBaseExList(wholeCaseBaseExList);
        batchEntityDTO.setReportCustomerInfoList(reportCustomerInfoList);
        batchEntityDTO.setBatchReportEntity(batchReportEntity);
        LogUtil.audit("构建报案表数据2耗时={}",System.currentTimeMillis()-time1);
        return batchEntityDTO;
    }

    private void buildPolicyDomain(AhcsDomainDTO ahcsDomainDTO,BatchEntityDTO batchEntityDTO){
        String userId = ahcsDomainDTO.getReportAcceptUm();
        Date now = new Date();
        String policyId = UuidUtil.getUUID();
        AhcsPolicyDomainDTO policyDomainDto = ahcsDomainDTO.getAhcsPolicyDomainDTOs().get(0);
        AhcsPolicyInfoEntity policyInfo = policyDomainDto.getAhcsPolicyInfo();

        policyInfo.setCreatedBy(userId);
        policyInfo.setUpdatedBy(userId);
        policyInfo.setCreatedDate(now);
        policyInfo.setUpdatedDate(now);
        policyInfo.setPolicyValid(BaseConstant.UPPER_CASE_Y);
        policyInfo.setIdAhcsPolicyInfo(policyId);
        policyInfo.setReportNo(ahcsDomainDTO.getReportNo());
        policyInfo.setPolicyExtend(buildPolicyExtends(policyDomainDto.getPolicyInfoExDTO()));
        batchEntityDTO.getPolicyInfoList().add(policyInfo);

        for (AhcsInsuredPresonDTO insuredPresonDto : policyDomainDto.getAhcsInsuredPresonDTOs()) {
            AhcsInsuredPresonEntity insuredPreson = new AhcsInsuredPresonEntity();
            BeanUtils.copyProperties(insuredPresonDto.getAhcsInsuredPreson(),insuredPreson);
            insuredPreson.setCreatedBy(userId);
            insuredPreson.setUpdatedBy(userId);
            insuredPreson.setCreatedDate(now);
            insuredPreson.setUpdatedDate(now);
            insuredPreson.setIdAhcsInsuredPerson(UuidUtil.getUUID());
            insuredPreson.setIdAhcsPolicyInfo(policyId);
            insuredPreson.setPlyCertificateType(insuredPreson.getCertificateType());
            insuredPreson.setCertificateType(insuredPreson.getCertificateType());
            batchEntityDTO.getInsuredPresonList().add(insuredPreson);
            AhcsInsuredPersonExtEntity insuredPersonExt = new AhcsInsuredPersonExtEntity();
            BeanUtils.copyProperties(insuredPresonDto.getAhcsInsuredPersonExt(),insuredPersonExt);
            insuredPersonExt.setCreatedBy(userId);
            insuredPersonExt.setUpdatedBy(userId);
            insuredPersonExt.setCreatedDate(now);
            insuredPersonExt.setUpdatedDate(now);
            insuredPersonExt.setIdAhcsInsuredPerson(insuredPreson.getIdAhcsInsuredPerson());
            insuredPersonExt.setIdAhcsInsuredPersonExt(UuidUtil.getUUID());
            batchEntityDTO.getInsuredPresonExList().add(insuredPersonExt);
        }

        for (AhcsPolicyHolderEntity policyHolder : policyDomainDto.getAhcsPolicyHolder()) {
            AhcsPolicyHolderEntity holder = new AhcsPolicyHolderEntity();
            BeanUtils.copyProperties(policyHolder,holder);
            holder.setCreatedBy(userId);
            holder.setUpdatedBy(userId);
            holder.setCreatedDate(now);
            holder.setUpdatedDate(now);
            holder.setIdAhcsPolicyHolder(UuidUtil.getUUID());
            holder.setIdAhcsPolicyInfo(policyId);
            batchEntityDTO.getPolicyHolderList().add(holder);
        }

        for (AhcsSpecialPromiseEntity specialPromise : policyDomainDto.getAhcsSpecialPromise()) {
            AhcsSpecialPromiseEntity promiseEntity = new AhcsSpecialPromiseEntity();
            BeanUtils.copyProperties(specialPromise,promiseEntity);
            promiseEntity.setCreatedBy(userId);
            promiseEntity.setUpdatedBy(userId);
            promiseEntity.setCreatedDate(now);
            promiseEntity.setUpdatedDate(now);
            promiseEntity.setIdAhcsSpecialPromise(UuidUtil.getUUID());
            promiseEntity.setIdAhcsPolicyInfo(policyId);
            batchEntityDTO.getSpecialPromiseList().add(promiseEntity);
        }

        for (AhcsCoinsureEntity coinsure : policyDomainDto.getAhcsCoinsure()) {
            AhcsCoinsureEntity coinsureEntity = new AhcsCoinsureEntity();
            BeanUtils.copyProperties(coinsure,coinsureEntity);
            coinsureEntity.setCreatedBy(userId);
            coinsureEntity.setUpdatedBy(userId);
            coinsureEntity.setCreatedDate(now);
            coinsureEntity.setUpdatedDate(now);
            coinsureEntity.setIdAhcsCoinsure(UuidUtil.getUUID());
            coinsureEntity.setIdAhcsPolicyInfo(policyId);
            batchEntityDTO.getCoinsureList().add(coinsureEntity);
        }

        for (AhcsPolicyPlanDTO policyPlanDTO : policyDomainDto.getAhcsPolicyPlanDTOs()) {
            AhcsPolicyPlanEntity policyPlan = new AhcsPolicyPlanEntity();
            BeanUtils.copyProperties(policyPlanDTO.getAhcsPolicyPlan(),policyPlan);
            policyPlan.setCreatedBy(userId);
            policyPlan.setUpdatedBy(userId);
            policyPlan.setCreatedDate(now);
            policyPlan.setUpdatedDate(now);
            String policyPlanId = UuidUtil.getUUID();
            policyPlan.setIdAhcsPolicyPlan(policyPlanId);
            policyPlan.setIdAhcsPolicyInfo(policyId);
            policyPlanDTO.setAhcsPolicyPlan(policyPlan);
            for (AhcsPolicyDutyDTO policyDutyDTO : policyPlanDTO.getAhcsPolicyDutyDTOs()) {
                AhcsPolicyDutyEntity policyDuty = new AhcsPolicyDutyEntity();
                BeanUtils.copyProperties(policyDutyDTO.getAhcsPolicyDuty(),policyDuty);
                policyDuty.setCreatedBy(userId);
                policyDuty.setUpdatedBy(userId);
                policyDuty.setCreatedDate(now);
                policyDuty.setUpdatedDate(now);
                policyDuty.setIdAhcsPolicyPlan(policyPlanId);
                String policyDutyId = UuidUtil.getUUID();
                policyDuty.setIdAhcsPolicyDuty(policyDutyId);
                policyDutyDTO.setAhcsPolicyDuty(policyDuty);

                List<AhcsPolicyDutyDetailEntity> dutyDetailList = new ArrayList<>();
                for (AhcsPolicyDutyDetailEntity policyDutyDetail : policyDutyDTO.getAhcsPolicyDutyDetail()) {
                    AhcsPolicyDutyDetailEntity dutyDetailEntity = new AhcsPolicyDutyDetailEntity();
                    BeanUtils.copyProperties(policyDutyDetail,dutyDetailEntity);
                    dutyDetailEntity.setCreatedBy(userId);
                    dutyDetailEntity.setUpdatedBy(userId);
                    dutyDetailEntity.setCreatedDate(now);
                    dutyDetailEntity.setUpdatedDate(now);
                    dutyDetailEntity.setIdAhcsPolicyDutyDetail(UuidUtil.getUUID());
                    dutyDetailEntity.setIdAhcsPolicyDuty(policyDutyId);
                    dutyDetailList.add(dutyDetailEntity);
                }
                policyDutyDTO.setAhcsPolicyDutyDetail(dutyDetailList);

                for (AhcsDutyAttributeDTO attributeDTO : policyDutyDTO.getAhcsDutyAttributeDTOs()) {
                    AhcsDutyAttributeEntity dutyAttributeEntity = new AhcsDutyAttributeEntity();
                    BeanUtils.copyProperties(attributeDTO.getAhcsDutyAttribute(),dutyAttributeEntity);
                    String dutyAttributeId = UuidUtil.getUUID();
                    dutyAttributeEntity.setCreatedBy(userId);
                    dutyAttributeEntity.setUpdatedBy(userId);
                    dutyAttributeEntity.setCreatedDate(now);
                    dutyAttributeEntity.setUpdatedDate(now);
                    dutyAttributeEntity.setIdAhcsPolicyDuty(policyDutyId);
                    dutyAttributeEntity.setIdAhcsDutyAttribute(dutyAttributeId);
                    attributeDTO.setAhcsDutyAttribute(dutyAttributeEntity);
                    List<AhcsDutyAttributeDetailEntity> attrDetailList = new ArrayList<>();
                    for (AhcsDutyAttributeDetailEntity attributeDetailEntity : attributeDTO.getAhcsDutyAttributeDetail()) {
                        AhcsDutyAttributeDetailEntity attrDetailEntity = new AhcsDutyAttributeDetailEntity();
                        BeanUtils.copyProperties(attributeDetailEntity,attrDetailEntity);
                        attrDetailEntity.setCreatedBy(userId);
                        attrDetailEntity.setUpdatedBy(userId);
                        attrDetailEntity.setCreatedDate(now);
                        attrDetailEntity.setUpdatedDate(now);
                        attrDetailEntity.setIdAhcsDutyAttribute(dutyAttributeId);
                        attrDetailEntity.setIdAhcsDutyAttributeDetail(UuidUtil.getUUID());
                        attrDetailList.add(attrDetailEntity);
                    }
                    attributeDTO.setAhcsDutyAttributeDetail(attrDetailList);
                }
            }
        }

        List<AhcsPolicyPlanDTO> planList =  mergePlanAndDutyInfo(policyDomainDto.getAhcsPolicyPlanDTOs());

        for (AhcsPolicyPlanDTO policyPlanDTO : planList) {
            batchEntityDTO.getPolicyPlanList().add(policyPlanDTO.getAhcsPolicyPlan());
            for (AhcsPolicyDutyDTO policyDutyDTO : policyPlanDTO.getAhcsPolicyDutyDTOs()) {
                batchEntityDTO.getPolicyDutyList().add(policyDutyDTO.getAhcsPolicyDuty());
                batchEntityDTO.getPolicyDutyDetailList().addAll(policyDutyDTO.getAhcsPolicyDutyDetail());
                for (AhcsDutyAttributeDTO attributeDTO : policyDutyDTO.getAhcsDutyAttributeDTOs()) {
                    batchEntityDTO.getDutyAttributeList().add(attributeDTO.getAhcsDutyAttribute());
                    batchEntityDTO.getDutyAttributeDetailList().addAll(attributeDTO.getAhcsDutyAttributeDetail());
                }
            }
        }

    }

    private String buildPolicyExtends(PolicyInfoExDTO policyInfoExDTO) {
        String policyExtend = "";
        try {
            if (policyInfoExDTO != null) {
                List<String> param = new ArrayList<>();
                List<String> agentNames = policyInfoExDTO.getAgentNames();
                if (agentNames != null && !agentNames.isEmpty()) {
                    StringBuilder agentNameParam = new StringBuilder(BaseConstant.AGENT_NAME + ":" + agentNames.get(0));
                    for (int i = 1; i < agentNames.size(); i++) {
                        agentNameParam.append(BaseConstant.SEPARATE_CHAR).append(agentNames.get(i));
                    }
                    param.add(agentNameParam.toString());
                }

                if (param == null || param.size() == 0) {
                    return null;
                }
                policyExtend = param.get(0);
                for (int i = 1; i < param.size(); i++) {
                    policyExtend = policyExtend + "," + param.get(i);
                }
            }
        } catch (Exception e1) {
            LogUtil.info("构建保单表扩展字段异常： policyInfoExDTO ：", JSONObject.toJSONString(policyInfoExDTO));
        }
        return policyExtend;
    }

    private List<AhcsPolicyPlanDTO> mergePlanAndDutyInfo(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyPlanDTO> mergePolicyPlanList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyPlanDTOs)) {
            for (AhcsPolicyPlanDTO planDto : ahcsPolicyPlanDTOs) {
                String planId = planDto.getAhcsPolicyPlan().getIdAhcsPolicyPlan();
                if (!codeList.contains(planDto.getAhcsPolicyPlan().getPlanCode())) {
                    planDto.setAhcsPolicyDutyDTOs(mergeDutyInfo(planDto.getAhcsPolicyDutyDTOs(), planId));
                    mergePolicyPlanList.add(planDto);
                    codeList.add(planDto.getAhcsPolicyPlan().getPlanCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyPlanList)) {
                        for (AhcsPolicyPlanDTO mergePlanDto : mergePolicyPlanList) {

                            if (mergePlanDto.getAhcsPolicyPlan().getPlanCode().equals(planDto.getAhcsPolicyPlan().getPlanCode())) {
                                mergePlanDto.getAhcsPolicyDutyDTOs().addAll(planDto.getAhcsPolicyDutyDTOs());
                                mergePlanDto.setApplyNum(mergePlanDto.getApplyNum().add(planDto.getApplyNum()));
                                mergePlanDto.setAhcsPolicyDutyDTOs(mergeDutyInfo(mergePlanDto.getAhcsPolicyDutyDTOs(), mergePlanDto.getAhcsPolicyPlan().getIdAhcsPolicyPlan()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyPlanList;
    }

    private List<AhcsPolicyDutyDTO> mergeDutyInfo(List<AhcsPolicyDutyDTO> ahcsPolicyDutyDTOs, String planId) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyDutyDTO> mergePolicyDutyList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDutyDTOs)) {
            for (AhcsPolicyDutyDTO dutyDto : ahcsPolicyDutyDTOs) {
                dutyDto.getAhcsPolicyDuty().setIdAhcsPolicyPlan(planId);
                if (!codeList.contains(dutyDto.getAhcsPolicyDuty().getDutyCode())) {
                    mergePolicyDutyList.add(dutyDto);
                    codeList.add(dutyDto.getAhcsPolicyDuty().getDutyCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyDutyList)) {
                        for (AhcsPolicyDutyDTO mergeDutyuDto : mergePolicyDutyList) {

                            if (mergeDutyuDto.getAhcsPolicyDuty().getDutyCode().equals(dutyDto.getAhcsPolicyDuty().getDutyCode())) {

                                mergeDutyuDto.getAhcsPolicyDuty().setDutyAmount(mergeDutyuDto.getAhcsPolicyDuty().getDutyAmount().add(dutyDto.getAhcsPolicyDuty().getDutyAmount()));
                                mergeDutyuDto.getAhcsPolicyDutyDetail().addAll(dutyDto.getAhcsPolicyDutyDetail());
                                mergeDutyuDto.setAhcsPolicyDutyDetail(mergeDutyDetailInfo(mergeDutyuDto.getAhcsPolicyDutyDetail(), mergeDutyuDto.getAhcsPolicyDuty().getIdAhcsPolicyDuty()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyDutyList;
    }

    private List<AhcsPolicyDutyDetailEntity> mergeDutyDetailInfo(List<AhcsPolicyDutyDetailEntity> ahcsPolicyDutyDetailDTOs, String dutyId) {
        List<String> codeList = new ArrayList<>();
        List<AhcsPolicyDutyDetailEntity> mergePolicyDutyDetailList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(ahcsPolicyDutyDetailDTOs)) {
            for (AhcsPolicyDutyDetailEntity ahcsPolicyDutyDetail : ahcsPolicyDutyDetailDTOs) {

                ahcsPolicyDutyDetail.setIdAhcsPolicyDuty(dutyId);
                if (!codeList.contains(ahcsPolicyDutyDetail.getDutyDetailCode())) {
                    mergePolicyDutyDetailList.add(ahcsPolicyDutyDetail);
                    codeList.add(ahcsPolicyDutyDetail.getDutyDetailCode());
                } else {
                    if (RapeCheckUtil.isNotEmpty(mergePolicyDutyDetailList)) {
                        for (AhcsPolicyDutyDetailEntity mergeDutyDetail : mergePolicyDutyDetailList) {
                            if (mergeDutyDetail.getDutyDetailCode().equals(ahcsPolicyDutyDetail.getDutyDetailCode())) {
                                mergeDutyDetail.setDutyAmount(mergeDutyDetail.getDutyAmount().add(ahcsPolicyDutyDetail.getDutyAmount()));
                            }
                        }
                    }
                }
            }
        }
        return mergePolicyDutyDetailList;
    }

    /**
      *
      * @Description buildReportLinkManInfoOnline
      * <AUTHOR>
      * @Date 2023/8/7 15:21
      **/
    private void buildReportLinkManInfoOnline(AhcsDomainDTO ahcsDomainDTO, OnlineBatchAutoClose autoCloseDTO) {
        List<LinkManEntity> linkMans = new ArrayList<>();
        LinkManEntity linkMan = new LinkManEntity();
        OnlineBatchLinkMan onlineBatchLinkMan = autoCloseDTO.getLinkMan();
        BeanUtils.copyProperties(onlineBatchLinkMan,linkMan);
        linkMan.setCreatedDate(new Date());
        linkMan.setUpdatedDate(new Date());
        linkMan.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        linkMan.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        linkMan.setIdAhcsLinkMan(UuidUtil.getUUID());
        linkMan.setIsReport(CommonConstant.YES);
        linkMan.setReportNo(ahcsDomainDTO.getReportNo());
        short i = 1;
        linkMan.setLinkManNo(i);
        linkMan.setCaseTimes(Short.parseShort(ReportConstant.INIT_CASE_TIMES));
        linkMans.add(linkMan);
        ahcsDomainDTO.setLinkMans(linkMans);
    }

    /**
     *
     * @Description buildReportInfoOnline
     * <AUTHOR>
     * @Date 2023/8/7 15:21
     **/
    private void buildReportInfoOnline(AhcsDomainDTO ahcsDomainDTO,OnlineBatchAutoClose autoCloseDTO) {
        ReportInfoEntity reportInfo = Optional.ofNullable(ahcsDomainDTO.getReportInfo()).orElse(new ReportInfoEntity());
        reportInfo.setReportNo(ahcsDomainDTO.getReportNo());
        reportInfo.setReportType(ReportConstant.NORMAL);// 正常报案
        if (ReportConstant.THREE_SOURCE_DIANPING.equals(autoCloseDTO.getThreeSource())){
            reportInfo.setReportMode(ReportConstant.REPORTMODE_DIANPING_ONLINE);// 报案来源
            reportInfo.setReportSubMode(ReportConstant.REPORTMODE_DIANPING_ONLINE_01);
        }else {
            reportInfo.setReportMode(ReportConstant.REPORTMODE_ONLINE);// 报案来源
            reportInfo.setReportSubMode(ReportConstant.REPORTSUBMODE_ONLINE_01);
        }
        OnlineBatchLinkMan onlineBatchLinkMan = autoCloseDTO.getLinkMan();
        reportInfo.setReporterCallNo(onlineBatchLinkMan.getLinkManTelephone());
        reportInfo.setReporterRegisterTel(onlineBatchLinkMan.getLinkManTelephone());
        reportInfo.setReporterName(autoCloseDTO.getClientName());
        reportInfo.setReportNo(ahcsDomainDTO.getReportNo());
        reportInfo.setIdClmReportInfo(UuidUtil.getUUID());
        reportInfo.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setCreatedDate(new Date());
        reportInfo.setUpdatedDate(new Date());
        if (reportInfo.getReportDate() == null) {
            reportInfo.setReportDate(new Date());
        }
        reportInfo.setReportRegisterUm(ahcsDomainDTO.getReportAcceptUm());
        reportInfo.setAcceptDepartmentCode(ahcsDomainDTO.getAcceptDepartmentCode());
        reportInfo.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        ahcsDomainDTO.setReportInfo(reportInfo);
    }


    /**
     *
     * @Description buildReportAccidentOnline
     * <AUTHOR>
     * @Date 2023/8/7 15:21
     **/
    private void buildReportAccidentOnline(AhcsDomainDTO ahcsDomainDTO,OnlineBatchAutoClose autoCloseDTO) {
        ReportAccidentEntity reportAccident = Optional.ofNullable(ahcsDomainDTO.getReportAccident()).orElse(new ReportAccidentEntity());
        reportAccident.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccident.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccident.setCreatedDate(new Date());
        reportAccident.setUpdatedDate(new Date());
        reportAccident.setAccidentDate(autoCloseDTO.getAccidentDate());
        reportAccident.setAccidentPlace(autoCloseDTO.getAccidentPlace());
        reportAccident.setIdClmReportAccident(UuidUtil.getUUID());
        reportAccident.setReportNo(ahcsDomainDTO.getReportNo());
        //判断是退运险还是大众点评
        if (!"2".equals(autoCloseDTO.getThreeSource())){
            reportAccident.setAccidentCauseLevel1("accident_000");
            reportAccident.setAccidentCauseLevel2("999");
        }else {
            reportAccident.setAccidentCauseLevel1(autoCloseDTO.getAccidentCauseLevel1());
            reportAccident.setAccidentCauseLevel2(autoCloseDTO.getAccidentCauseLevel2());
        }
        reportAccident.setMigrateFrom(CommonConstant.MIGRATE_FROM);
        reportAccident.setOverseasOccur("0");
        reportAccident.setProvinceCode(autoCloseDTO.getAccidentProvince());
        reportAccident.setAccidentCityCode(autoCloseDTO.getAccidentCity());
        reportAccident.setAccidentCountyCode(autoCloseDTO.getAccidentCounty());
        if (StringUtils.isEmptyStr(autoCloseDTO.getAccidentCity())){
            reportAccident.setAccidentCityCode(BaseConstant.OTHER_CITY_CODE);
            reportAccident.setAccidentCountyCode(BaseConstant.OTHER_COUNTRY_CODE);
        }
        if (StringUtils.isEmptyStr(autoCloseDTO.getAccidentCounty())){
            reportAccident.setAccidentCountyCode(BaseConstant.OTHER_COUNTRY_CODE);
        }
        // {被保人}于{事故日期}因{出险原因}申请理赔
        String accidentDateStr = DateUtils.dateFormat(autoCloseDTO.getAccidentDate(), DateUtils.FULL_DATE_STR);
        String accidentDetail = autoCloseDTO.getClientName() + "于" + accidentDateStr + "因其他申请理赔";
        reportAccident.setAccidentDetail(accidentDetail);
        reportAccident.setOverseasOccur("0");
        ahcsDomainDTO.setReportAccident(reportAccident);
    }

    /**
     *
     * @Description buildReportAccidentExOnline
     * <AUTHOR>
     * @Date 2023/8/7 15:21
     **/
    private void buildReportAccidentExOnline(AhcsDomainDTO ahcsDomainDTO, OnlineBatchAutoClose onlineBatchAutoClose) {
        ReportAccidentExEntity reportAccidentEx = ahcsDomainDTO.getReportAccidentEx();
        if (reportAccidentEx == null) {
            reportAccidentEx = new ReportAccidentExEntity();
        }
        reportAccidentEx.setCreatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccidentEx.setUpdatedBy(ahcsDomainDTO.getReportAcceptUm());
        reportAccidentEx.setCreatedDate(new Date());
        reportAccidentEx.setUpdatedDate(new Date());
        // 出险类型
        if (ReportConstant.THREE_SOURCE_DIANPING.equals(onlineBatchAutoClose.getThreeSource())){
            reportAccidentEx.setInsuredApplyType(InsuredApplyTypeEnum.DUTY_LAW_OR_OTHER.getType());
        }else {
            reportAccidentEx.setInsuredApplyType(InsuredApplyTypeEnum.OTHER_ALLOWANCE.getType());
        }
        reportAccidentEx.setIdAhcsReportAccidentEx(UuidUtil.getUUID());
        reportAccidentEx.setReportNo(ahcsDomainDTO.getReportNo());
        reportAccidentEx.setInsuredApplyStatus(InsuredApplyStatusEnum.OTHER.getType());
        ahcsDomainDTO.setReportAccidentEx(reportAccidentEx);
    }

}
