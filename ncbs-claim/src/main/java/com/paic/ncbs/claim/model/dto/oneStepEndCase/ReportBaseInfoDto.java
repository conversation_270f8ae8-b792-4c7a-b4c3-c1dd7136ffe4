package com.paic.ncbs.claim.model.dto.oneStepEndCase;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ReportBaseInfoDto {
    private String policyNo;//保单号
    private String clientName;//被保人姓名
    private String certificateType;//被保人证件类型
    private String certificateNo;//被保人证件号
    private String isSpecialReport;//是否特殊报案，Y-是，N-否，默认N
    private String reportMode;//报案来源大类，默认9-渠道报案
    private String reportSubMode;//报案来源小类，默认9-04-万欣和
    private String caseClass;//案件类别：1-人伤 2-非人伤，默认1-人伤
    private String isHugeAccident;//是否重大灾难：Y-是;N-否，默认N-否
    private String hugeAccidentName;//重灾名称（是否重大灾难为是时必须）
    private String isSuffice;//单证是否齐全：Y-是；N-否，默认Y
    private String professionCode;//职业大类类型，核心可以根据小类分割
    private String subProfessionCode;//职业小类类型，默认 00103002
    @JSONField(alternateNames = "reporterName")
    private String applicantPerson;//报案人姓名，默认出险人名称
    @JSONField(alternateNames = "reporterType")
    private String applicantType;//报案人类型：01-本人；02-受益人；03-其他；04-雇员
    private String reporterCertificateType;//报案人证件类型，默认出险人证件类型
    private String reporterCertificateNo;//报案人证件号，默认出险人证件号码
    private String linkManName;//联系人姓名，默认出险人名称
    private String linkManRelation;//联系人与被保人关系
    private String linkManTelephone;//联系人电话
    private String acceptanceNumber;//案件受理号
    private String claimDealWay;
}
