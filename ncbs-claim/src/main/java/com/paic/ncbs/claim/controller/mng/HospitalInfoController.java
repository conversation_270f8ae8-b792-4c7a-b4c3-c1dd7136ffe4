package com.paic.ncbs.claim.controller.mng;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.fileupolad.HospitalImportResultVO;
import com.paic.ncbs.claim.service.checkloss.HospitalInfoService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Controller
@RequestMapping("/mng/app/hospitalInfoAction")
@Api(tags = {"医院信息"})
public class HospitalInfoController extends BaseController {

	@Autowired
	private HospitalInfoService hospitalInfoService;
	@Autowired
	private CommonParameterService commonService;

	@ApiOperation(value = "根据医院名称模糊查询有效的医院信息")
	@ResponseBody
	@RequestMapping(value = "/getValidHospitalList", method = RequestMethod.POST)
	public ResponseResult<List<HospitalInfoVO>> getValidHospitalList(@RequestBody HospitalInfoVO hospitalInfoVO) {
		LogUtil.info("#根据医院名称模糊查询有效的医院信息#,传入参数hospitalName={" + hospitalInfoVO.getQuery() + hospitalInfoVO.getReportNo()+"}");

		List<HospitalInfoVO> hospitalInfoVOList = hospitalInfoService.getValidHospitalList(hospitalInfoVO.getQuery(),hospitalInfoVO.getReportNo());
		return ResponseResult.success(hospitalInfoVOList);
	}

	@ApiOperation(value = "通过医院Code查询医院信息")
	@ResponseBody
	@RequestMapping(value = "/getHospitalByCode/{hospitalCode}", method = RequestMethod.GET)
	public ResponseResult<HospitalInfoVO> getHospitalByCode(@ApiParam("医院编码")@PathVariable("hospitalCode") String hospitalCode) {
		LogUtil.info("#通过医院Code查询医院信息#,传入参数hospitalCode={" + hospitalCode + "}");

		HospitalInfoVO hospitalInfoVO = hospitalInfoService.getHospitalByCode(hospitalCode);
		return ResponseResult.success(hospitalInfoVO);
	}

	@ApiOperation(value = "获取医院列表信息")
	@ResponseBody
	@PostMapping("/getHospitalInfoList")
	public ResponseResult getHospitalInfoList(@RequestBody HospitalInfoVO hospitalInfoVO){
		Pager pager = hospitalInfoVO.getPager();
		PageHelper.startPage(pager.getPageIndex(),pager.getPageRows(),true);
		List<HospitalInfoVO> list = hospitalInfoService.getHospitalInfoList(hospitalInfoVO);
        if(null != list){
           for (HospitalInfoVO hospitalInfo :  list) {
			   //省市县中文名称反显
			   AdressSearchDto adressSearchDto  = new AdressSearchDto();
			   adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(hospitalInfo.getProvinceCode())
					   .setAccidentCountyCode(hospitalInfo.getDistrictCode()).setAccidentCityCode(hospitalInfo.getPrefectureLevelCode());
			   AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
			   hospitalInfo.setProvinceName(detailAdressFormCode.getAccidentProvinceName());
			   hospitalInfo.setPrefectureLevelName(detailAdressFormCode.getAccidentCityName());
			   hospitalInfo.setDistrictName(detailAdressFormCode.getAccidentCountyName());
		   }
        }
		PageInfo<HospitalInfoVO> pageInfo = new PageInfo<>(list);
		hospitalInfoVO.getPager().setTotalRows((int)pageInfo.getTotal());
		return ResponseResult.success(list,hospitalInfoVO.getPager());
	}

	@ApiOperation(value = "保存医院信息")
	@ResponseBody
	@RequestMapping(value = "/saveHospitalInfo", method = RequestMethod.POST)
	public ResponseResult<HospitalInfoVO> saveHospitalInfo(@RequestBody HospitalInfoVO hospitalInfoVO) {
		return ResponseResult.success(hospitalInfoService.saveHospitalInfo(hospitalInfoVO));
	}

	@ApiOperation(value = "删除医院信息")
	@ResponseBody
	@RequestMapping(value = "/deleteHospitalInfo", method = RequestMethod.GET)
	public ResponseResult<HospitalInfoVO> deleteHospitalInfo(@ApiParam("主键ID")@RequestParam("idHospitalInfo") String idHospitalInfo) {
		return ResponseResult.success(hospitalInfoService.deleteHospitalInfo(idHospitalInfo));
	}

	@ApiOperation(value = "批量导入医院信息")
	@ResponseBody
	@RequestMapping(value = "/importHospitalInfo", method = RequestMethod.POST)
	public ResponseResult<HospitalImportResultVO> importHospitalInfo(@RequestParam("file") MultipartFile file) {
		return ResponseResult.success(hospitalInfoService.importHospitalInfo(file));
	}

	@ApiOperation(value = "下载导入医院信息模板")
	@GetMapping("/getHospitalInfoTemplate")
	public void getHospitalInfoTemplate(HttpServletResponse response) throws IOException {
		hospitalInfoService.getHospitalInfoTemplate(response);
	}

	@ApiOperation(value = "下载批量导入失败信息")
	@ResponseBody
	@RequestMapping(value = "/getImportFailInfo", method = RequestMethod.GET)
	public void getImportFailInfo(@RequestParam("batchNo")String batchNo,HttpServletResponse response) throws IOException {
		hospitalInfoService.getImportFailInfo(batchNo,response);
	}

}
