package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.service.sop.SopMatchService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SOP匹配Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP匹配")
@RestController
@RequestMapping("/who/app/sopMatchAction")
@Slf4j
public class SopMatchController extends BaseController {

    @Autowired
    private SopMatchService sopMatchService;

    @ApiOperation("根据案件信息匹配SOP规则")
    @GetMapping(value = "/matchSopByCase")
    public ResponseResult<List<SopMainVO>> matchSopByCase(@RequestParam("reportNo") String reportNo,
                                                          @RequestParam(value = "caseTimes", required = false) Integer caseTimes,
                                                          @RequestParam(value = "taskBpmKey", required = false) String taskBpmKey) {
        try {
            LogUtil.audit("根据案件信息匹配SOP规则，reportNo：{ }，caseTimes：{}，taskBpmKey：{}",
                    reportNo, caseTimes, taskBpmKey);
            List<SopMainVO> result = sopMatchService.matchSopByCase(reportNo, caseTimes, taskBpmKey);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

}