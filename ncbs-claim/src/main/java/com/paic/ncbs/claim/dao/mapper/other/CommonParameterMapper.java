package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.report.BankInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface CommonParameterMapper extends BaseDao<CommonParameterEntity> {

    CommonParameterEntity getCommonParameterByCollectionCode(String collectionCode);

    String getClaimMadeProductAmount(String productNo);

    List<String> getValueCodeByCollectionCode(String collectionCode);

    List<CommonParameterTinyDTO> getCommonParameterList(@Param("collectionCodeArray") String[] collectionCodeArray);

    String getTotalSwitch();

    List<String> getCommonParameterByCode(String collectionCode);

    String getNameByCode(@Param("valueCode")String valueCode,@Param("collectionCode")String collectionCode);

    List<CommonParameterTinyDTO> getCommonParameterListByArraryList(@Param("collectionCodeArray") List<String> collectionCodeArray);

    List<BankInfoDTO> getBankInfoList();

    List<BankInfoDTO> getBranchBankInfoList(@RequestParam("bankName") String bankName);

    String getBankCodeByName(@RequestParam("bankName") String bankName);

    String getBankIfExistByBranchBankName(@RequestParam("branchBankName") String branchBankName);

    /**
     * 查询出险原因
     */
    List<CommonParameterTinyDTO> getAccidentReasonList(List<String> list);

    /**
     * 查询财产损失原因
     */
    List<CommonParameterTinyDTO> getPropertyLossReasonList(List<String> list);

    /**
     * 查询救援类型
     * @return
     */
    List<CommonParameterTinyDTO> getSuccourTypeList();

    /**
     * 查询重点项目
     * @return
     */
    List<CommonParameterTinyDTO> getKeyProjectList();

    /**
     * 根据名称查询伤情诊断信息
     */
    List<CommonParameterTinyDTO> getCommonParameter(@RequestParam("collectionCode")String collectionCode, @RequestParam("searchStr") String searchStr);
}


