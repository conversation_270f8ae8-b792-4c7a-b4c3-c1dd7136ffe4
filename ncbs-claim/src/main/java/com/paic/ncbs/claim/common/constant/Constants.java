package com.paic.ncbs.claim.common.constant;

import com.paic.ncbs.claim.model.vo.user.PermissionTypeVO;

import java.util.*;

public class Constants {
    private Constants() {}

    public static final String COMPANY_TYPE_RESCUE = "R";


    /**
     * 数据来源为 0
     */
    public final static String DATA_SOURCE_DEFAULT = "0";

    /**
     * 数据来源为 na
     */
    public final static String MIGRATE_FROM_DEFAULT = "na";

    /**
     * 支付信息状态0为有效
     */
    public final static String PAYMENT_INFO_STATUS_0 = "0";

    /**
     * 支付信息状态1为失效
     */
    public final static String PAYMENT_INFO_STATUS_1 = "1";

    /**
     * 支付项状态10为草稿状态
     */
    public final static String PAYMENT_ITEM_STATUS_10 = "10";

    /**
     * 支付项状态20为暂存
     */
    public final static String PAYMENT_ITEM_STATUS_20 = "20";
    /**
     * 追偿支付项暂存
     */
    public final static String PAYMENT_ITEM_STATUS_30 = "30";
    /**
     * 实收实付确认复核通过
     */
    public final static String PAYMENT_ITEM_STATUS_31 = "31";
    /**
     * 实收实付失败
     */
    public final static String PAYMENT_ITEM_STATUS_32 = "32";
    /**
     * 支付项状态11为 送收付成功
     */
    public final static String PAYMENT_ITEM_STATUS_11 = "11";

    /**
     * 支付项状态 70 支付失败 退回待修改
     */
    public final static String PAYMENT_ITEM_STATUS_70 = "70";

    /**
     * 支付项状态 71 支付失败 退票 退回待修改
     */
    public final static String PAYMENT_ITEM_STATUS_71 = "71";

    /**
     * 支付项状态 80 支付成功
     */
    public final static String PAYMENT_ITEM_STATUS_80 = "80";
    /**
     * 支付项状态90为失效
     */
    public final static String PAYMENT_ITEM_STATUS_90 = "90";
    /**
     * 支付项01为不合并
     */
    public final static String PAYMENT_ITEM_MERGE_01 = "01";
    /**
     * 支付信息类型03为预赔
     */
    public final static String PAYMENT_INFO_TYPE_03 = "03";

    public final static String CURR_USER = "currUser";
    // 权限机构
    public final static String CURR_COMCODE = "currComCode";

    public final static String SMS_STATUS_INIT = "01";
    public final static String SMS_STATUS_FAIL= "03";
    public final static String SMS_STATUS_SUCCESS = "04";
    public final static Integer SMS_SEND_SUCCESS = 1;
    public final static String REPORT_SMS_TEMPLATE = "尊敬的客户，您好！您的案件%s已受理，我司工作人员会尽快与您联系。【三星保险】";
    public final static String VERIFY_SMS_TEMPLATE = "尊敬的客户，您好！您的%s已经审核完成，理赔保险金共计%s元（RMB）。我们会尽快向您支付保险金。【第三方系统保险】";
    public final static String PAY_SMS_TEMPLATE = "尊敬的客户，您好！您的%s理赔保险金已经支付完成，共计支付保险金%m元（%s），请注意查收。【第三方系统保险】";
    public final static String DISPATCH_SMS_TEMPLATE = "报案号%s，联系人:%s %s，投保人:%s，出险时间:%s，出险类型:%s。【第三方系统保险】";
    public final static String SMS_LINK_REPORT = "report";
    public final static String SMS_LINK_REGISTER = "register";
    public final static String SMS_LINK_DISPATCH = "dispatch";
    public final static String SMS_LINK_REVIEW = "review";

    public final static String MAIL_TEMPLATE_TYPE = "4";
    public final static String MAIL_SYSTEM_CODE = "NCBSCLAIM";
    public final static String MAIL_BUSINESS_CODE = "NCBSCLAIM";
    public final static String MAIL_DEFAULT_DATA = "-";

    public final static String SEPARATOR=",";
    public final static String SEPARATOR_SPECIAL_CHARACTERS="#";

    /**
     * EffectiveStatus 0-有效，1-作废
     */
    public final static String EFFECTIVE_STATUS_ZERO="0";
    /**
     * EffectiveStatus 0-有效，1-作废
     */
    public final static String EFFECTIVE_STATUS_ONE="1";
    /**
     * 分隔符_
     */
    public final static String SEPARATOR_LINE="_";

    public final static String SYSTEM_USER="system";

    public final static String MAJOR_CASE_MAIL_CONTENT = "您好：一通意健险大案报案信息，请知悉并及时跟进处理，谢谢！";

    public final static String MAJOR_CASE_MAIL_TITLE = "意健险大案信息反馈 %s";

    public final static String CASE_MAIL_CONTENT = "您好！一通意健险案件，保单号%s，保单归属机构%s，请及时跟进处理，谢谢！";

    public final static String CASE_MAIL_TITLE = "意健险案件信息反馈 %s";

    public final static String MAIL_TITLE_TIPS = "[测试邮件]";
    public final static String MAIL_CONTENT_TIPS = "[特别说明：本邮件由新理赔测试系统发出，只为验证邮件功能是否正常，无需理会，如有打扰敬请谅解^_^]";

    public final static String PERMISSION_VERIFY = "verify";
    public final static String PERMISSION_REGIST = "regist";
    public final static String PERMISSION_PRE = "pre";
    public final static String PERMISSION_ZERO = "zero";
    public final static String PERMISSION_INVEST = "invest";

    //报案号
    public final static String ONLINE_FLAG = "0";
    public final static String OFFLINE_FLAG = "1";

    public final static String SUCCESS="success";

    /**
     * 解约方式0 -不退费，1-退费
     */
    public final static String SURRENDER_TYPE_ZERO="0";
    public final static String SURRENDER_TYPE_ONE="1";

    /**
     * 退费金额类型 0-退未满期净保费，1-约定退费金额
     */
    public final static String  REFUND_TYPE_ZERO="0";
    public final static String  REFUND_TYPE_ONE="1";

    public final static String COPY_POLICY_UW_ERROR_MESS="抄单数据核保信息保单号:";

    /**
     * 不足免配额提示
     */
    public final static String LESS_THAN_REMIT_AMOUNT="(合理费用不足免赔理算金额为0)";

    public  final static String HOSTPITAL_NOTICE="住院费用无可赔付责任";

    /**
     * 是Y，N-否
     */
    public final static String FLAG_N="N";
    public final static String FLAG_Y="Y";

    public static final String SETTLE_AMOUNT_REMARK ="手动修改责任明细理算金额后自动分摊金额到发票";
    public static final String REMIT_AMOUNT_REMARK ="手动修改责任明细免赔额后自动分摊免赔额到发票";

    public static final String BILL_REMARK_NO_EFFECTIVE="发票不在保单有效期";
    public static final String BILL_REMARK_EXCEEDMONTH_PAY_DAY="超每月赔付天数";
    public static final String EXCEE_YEAYLY_PAY_DAY="超年度赔付天数";

    public static final String WAIT_FALG="等待期发票";
    public final static Map<String, String> PERMISSION_TYPE_MAP = new HashMap<>();
    static {
        PERMISSION_TYPE_MAP.put(PERMISSION_VERIFY,"核赔岗");
        PERMISSION_TYPE_MAP.put(PERMISSION_REGIST,"立案审批岗");

    }

    public final static Map<String, Map<Integer,String>> PERMISSION_MAP = new HashMap<>();
    public final static Map<Integer, String> PERMISSION_VERIFY_MAP = new TreeMap<>();
    public final static Map<Integer, String> PERMISSION_REGIST_MAP = new TreeMap<>();
    static {
        PERMISSION_VERIFY_MAP.put(1,"核赔一级");
        PERMISSION_VERIFY_MAP.put(2,"核赔二级");
        PERMISSION_VERIFY_MAP.put(3,"核赔三级");
        PERMISSION_VERIFY_MAP.put(4,"核赔四级");
        PERMISSION_VERIFY_MAP.put(5,"核赔五级");
        PERMISSION_VERIFY_MAP.put(6,"核赔六级");
        PERMISSION_VERIFY_MAP.put(7,"核赔七级");
        PERMISSION_VERIFY_MAP.put(8,"核赔八级");
        PERMISSION_VERIFY_MAP.put(9,"核赔九级");
        PERMISSION_VERIFY_MAP.put(10,"核赔十级");

        PERMISSION_REGIST_MAP.put(1,"立案一级");
        PERMISSION_REGIST_MAP.put(2,"立案二级");
        PERMISSION_REGIST_MAP.put(3,"立案三级");
        PERMISSION_REGIST_MAP.put(4,"立案四级");
        PERMISSION_REGIST_MAP.put(5,"立案五级");

        PERMISSION_MAP.put(PERMISSION_VERIFY,PERMISSION_VERIFY_MAP);
        PERMISSION_MAP.put(PERMISSION_REGIST,PERMISSION_REGIST_MAP);
       
    }

    public final static Map<Integer, String> TASK_GRADE_MAP = new TreeMap<>();
    static {
        TASK_GRADE_MAP.put(1,"一级");
        TASK_GRADE_MAP.put(2,"二级");
        TASK_GRADE_MAP.put(3,"三级");
        TASK_GRADE_MAP.put(4,"四级");
        TASK_GRADE_MAP.put(5,"五级");
        TASK_GRADE_MAP.put(6,"六级");
        TASK_GRADE_MAP.put(7,"七级");
        TASK_GRADE_MAP.put(8,"八级");
        TASK_GRADE_MAP.put(9,"九级");
    }

    public final static Map<Integer, String> AUDIT_GRADE_MAP = new TreeMap<>();
    static {
        AUDIT_GRADE_MAP.put(1,"一级");
        AUDIT_GRADE_MAP.put(2,"二级");
        AUDIT_GRADE_MAP.put(3,"三级");
        AUDIT_GRADE_MAP.put(4,"四级");
        AUDIT_GRADE_MAP.put(5,"五级");
        AUDIT_GRADE_MAP.put(6,"六级");
        AUDIT_GRADE_MAP.put(7,"七级");
        AUDIT_GRADE_MAP.put(8,"八级");
        AUDIT_GRADE_MAP.put(9,"九级");
    }

    public final static List<PermissionTypeVO> PERMISSION_TYPE_LIST = new ArrayList<>();
    static {
        PERMISSION_TYPE_LIST.add(new PermissionTypeVO(PERMISSION_VERIFY,"核赔"));
        PERMISSION_TYPE_LIST.add(new PermissionTypeVO(PERMISSION_REGIST,"立案"));
    }

    public final static List<String> DUTY_CODE_LIST=new ArrayList<>();
    static {
        DUTY_CODE_LIST.add("C00289");
    }
    public final static List<PermissionTypeVO> PERMISSION_ROLE_LIST = new ArrayList<>();
    static {
        PERMISSION_ROLE_LIST.add(new PermissionTypeVO(PERMISSION_VERIFY,"核赔岗"));
        PERMISSION_ROLE_LIST.add(new PermissionTypeVO(PERMISSION_REGIST,"立案审批岗"));
    }

    public final static Integer PERMISSION_VERIFY_MAX = 8;

    /**
     * 总部code，这里为三星产险机构code
     */
    public static final String DEPARTMENT_CODE = "1";

    /**
     * 服务器环境——三星
     */
    public static final String ENV_SAMSUNG = "samsung";

    /**
     * 币种——人民币
     */
    public static final String CURRENCY_CNY = "CNY";

    /**
     * 补偿财务对接
     */
    public static final String COMPENSATION_JOB_TYPE_1 = "1";

    /**
     * 补偿再保对接
     */
    public static final String COMPENSATION_JOB_TYPE_7 = "7";

    /**
     * 补偿理赔送收付
     */
    public static final String COMPENSATION_BUSINESS_TYPE_12 = "12";

    /**
     * 补偿理赔送再保
     */
    public static final String COMPENSATION_BUSINESS_TYPE_72 = "72";

    /**
     * 微保理算同步保司
     */
    public static final String WESURE_SYNC_TYPE_SETTLE = "0";
    /**
     * 保司复核同步微保
     */
    public static final String WESURE_SYNC_TYPE_REVIEW = "1";

    public static final String EXCEEd_MONTH_PAY_DAY_NOTICE="超每月赔付天数不予赔付，理算金额";
    public static final String EXCEEd_YEARLY_PAY_DAY_NOTICE="超年度赔付天数不予赔付，理算金额";
    public static final String POLICY_EFFECTIVE_OUT="发票不在保单有效期内，理算金额";
    /**
     * 是否标识
     */
    public static final String YES_FLAG="Y";
    public static final String NOT_FLAG="N";
    /**
     * 默认合理费用计算
     */
    public final static String DEFAULT_IPML="defaultReasonAmountServiceImpl";
    public final static String REASONA_SUB_THIRDPARTY="reasonableSubtractThirdPartyServiceImpl";
    public final static String REASONA_SUB_DRUGS="reasonableDrugsServiceImpl";
    /**
     * 次免赔额
     */
    public final static String TIMES_REMIT_AMOUNT_IMPL="timesRemitAmountServiceImpl";
    /**
     * 日免赔额
     */
    public final static String DAY_REMIT_AMOUNT_IMPL="dayRemitAmountServiceImpl";
    public final static String NO_REMIT_AMOUNT_IMPL ="noRemitAmountServiceImpl";
    /**
     * 年免赔额
     */
    public final static String YEAR_REMIT_AMOUNT_IMPL="yearRemitAmountServiceImpl";
    /**
     * 赔付比例
     */
    public final static String PAY_PROPORTION_IMPL="payProportionServiceImpl";

    /**
     * 药房责任的合理费用
     */
    public final static String REASONABLE_AMOUNT_02="ramount02";
    /**
     * 合理费用=账单金额-第三方
     */
    public final static String REASONABLE_AMOUNT_01="ramount01";
    /**
     * 默认合理费用
     */
    public final static String REASONABLE_AMOUNT_00="ramount00";

    public  final static String PROPORTION="payProportion";

    /**
     * 因子实现集合Map
     * 00-合理费用默认实现
     * 01-合理费用等于 发票金额-自费-第三方金额实现
     * times次免赔
     * day-日免赔
     * year-年免赔
     * payProportion-赔付比列
     */
    public final static Map<String,String>  FACTOR_IMPL_MAP=new HashMap<>();
    static {
        FACTOR_IMPL_MAP.put("ramount00",DEFAULT_IPML);//合理费用默认实现
        FACTOR_IMPL_MAP.put("ramount01",REASONA_SUB_THIRDPARTY);//合理费用等于 发票金额-自费-第三方金额实现
        FACTOR_IMPL_MAP.put("ramount02",REASONA_SUB_DRUGS);//合理费用等于发票金额
        FACTOR_IMPL_MAP.put("times",TIMES_REMIT_AMOUNT_IMPL);//次免赔
        FACTOR_IMPL_MAP.put("day",DAY_REMIT_AMOUNT_IMPL);//日免赔
        FACTOR_IMPL_MAP.put("year",YEAR_REMIT_AMOUNT_IMPL);//年免赔
        FACTOR_IMPL_MAP.put("noremit",NO_REMIT_AMOUNT_IMPL);//没有免赔

        FACTOR_IMPL_MAP.put("payProportion",PAY_PROPORTION_IMPL);//赔付比例
    }

    public final static Map<String, String> SETTLE_IMPL_MAP = new HashMap<>();
    static{
        //医疗费用责任计算实现类映射
        SETTLE_IMPL_MAP.put("01","deathSettleServiceImpl");//身故
        SETTLE_IMPL_MAP.put("02","disabilitySettleServiceImpl");//伤残
        SETTLE_IMPL_MAP.put("03","diseaseSettleServiceImpl");//重疾
        SETTLE_IMPL_MAP.put("04","medicalSettleServiceImpl");//医疗
        SETTLE_IMPL_MAP.put("05","allowanceSettleServiceImpl");//津贴
        SETTLE_IMPL_MAP.put("06","serveSettleServiceImpl");//服务
        SETTLE_IMPL_MAP.put("07","quotaSettleServiceImpl");//一般定额
        SETTLE_IMPL_MAP.put("08","lossSettleServiceImpl");//损失
        SETTLE_IMPL_MAP.put("12","dutymedicalSettleServiceImpl");//责任
        SETTLE_IMPL_MAP.put("99","ohtermedicalSettleServiceImpl");//其他
    }
    /**
     * 赔偿限额类型:0-每次，1-每件，2-每天，
     */
    public final static Map<String,String> LIMIT_IMPL_MAP=new HashMap<>();
    static {
        LIMIT_IMPL_MAP.put("0","timesLimitAmountServiceImpl");
        LIMIT_IMPL_MAP.put("2","dayLimitAmountServiceImpl");
    }
    /**
     * 赔偿限额类型: 3-每月限额，4-季限额，5-年限额
     */
    public final static Map<String,String> ADDUP_LIMIT_IMPL_MAP=new HashMap<>();
    static {
        ADDUP_LIMIT_IMPL_MAP.put("3","monthLimitAmountServiceImpl");
        ADDUP_LIMIT_IMPL_MAP.put("4","seasonLimitAmountServiceImpl");
        ADDUP_LIMIT_IMPL_MAP.put("5","yearLimitAmountServiceImpl");
        ADDUP_LIMIT_IMPL_MAP.put("s","specialProjectMonthLimitServiceImpl");//指定产品的定制化
    }

    /**
     * 理算依据模板实现
     */
    public final static Map<String, String> TEMPLATE_IMPL_MAP = new HashMap<>();
    static{
        //理算依据实现类映射
        TEMPLATE_IMPL_MAP.put("01","deathTemplateServiceImpl");//身故
        TEMPLATE_IMPL_MAP.put("02","clmsDisabilityTemplateServiceImpl");//伤残
        TEMPLATE_IMPL_MAP.put("03","diseaseTemplateServiceImpl");//重疾
        TEMPLATE_IMPL_MAP.put("04","medicalTemplateServiceImpl");//医疗
        TEMPLATE_IMPL_MAP.put("05","clmsAllowanceTemplateServiceImpl");//津贴
        TEMPLATE_IMPL_MAP.put("06","clmsServeeTemplateServiceImpl");//服务
        TEMPLATE_IMPL_MAP.put("07","quotaTemplateServiceImpl");//一般定额
        TEMPLATE_IMPL_MAP.put("08","clmsLossTemplateServiceImpl");//损失
        TEMPLATE_IMPL_MAP.put("12","dutymedicalTemplateServiceImpl");//责任
        TEMPLATE_IMPL_MAP.put("99","ohtermedicalTemplateServiceImpl");//其他
    }

    /**
     * 模板样式名称
     */
    public final static Map<String, String> TEMPLATE_FTL_MAP = new HashMap<>();
    static{
        //理算依据实现类映射
        TEMPLATE_FTL_MAP.put("01","deathSettleReason.ftl");//身故
        TEMPLATE_FTL_MAP.put("02","disabilitySettleReason.ftl");//伤残
        TEMPLATE_FTL_MAP.put("03","diseaseSettleReason.ftl");//重疾
        TEMPLATE_FTL_MAP.put("04","medicalSettleReason.ftl");//医疗
        TEMPLATE_FTL_MAP.put("05","allowanceSettleReason.ftl");//津贴
        TEMPLATE_FTL_MAP.put("06","serveeSettleReason.ftl");//服务
        TEMPLATE_FTL_MAP.put("07","quotaSettleReason.ftl");//一般定额
        TEMPLATE_FTL_MAP.put("08","lossSettleReason.ftl");//损失
        TEMPLATE_FTL_MAP.put("12","dutySettleReason.ftl");//责任
        TEMPLATE_FTL_MAP.put("99","ohterSettleReason.ftl");//其他
        TEMPLATE_FTL_MAP.put("04_2","medicalSettleReason_04_2.ftl");//医疗非级距
    }

    /**
     * /** 免赔额类型：0-次免赔，1-年免赔,2-日免赔
     */
    public final static Map<String, String> REMIT_AMOUNT_TYPE_MAP = new HashMap<>();
    static{
        //免赔额类型
        REMIT_AMOUNT_TYPE_MAP.put("0","times");//0-次免赔
        REMIT_AMOUNT_TYPE_MAP.put("1","year");//1-年免赔
        REMIT_AMOUNT_TYPE_MAP.put("2","day");//2-日免赔
        REMIT_AMOUNT_TYPE_MAP.put("99","999");//无免赔
    }

    /**
     * 责任明细分类公式映射
     */
    public final static Map<String,String> DUTY_DETAIL_FORMULA_MAP=new HashMap<>();
    static{
        //理算依据实现类映射
        DUTY_DETAIL_FORMULA_MAP.put("01","deathFormulaServiceImpl");//身故
        DUTY_DETAIL_FORMULA_MAP.put("02","disabilityFormulaServiceImpl");//伤残
        DUTY_DETAIL_FORMULA_MAP.put("03","diseaseFormulaServiceImpl");//重疾
        DUTY_DETAIL_FORMULA_MAP.put("04","medicalFormulaServiceImpl");//医疗
        DUTY_DETAIL_FORMULA_MAP.put("05","allowanceFormulaServiceImpl");//津贴
        DUTY_DETAIL_FORMULA_MAP.put("06","serveFormulaServiceImpl");//服务
        DUTY_DETAIL_FORMULA_MAP.put("07","quotaFormulaServiceImpl");//一般定额
        DUTY_DETAIL_FORMULA_MAP.put("08","lossFormulaServiceImpl");//损失
        DUTY_DETAIL_FORMULA_MAP.put("12","dutyFormulaServiceImpl");//责任
        DUTY_DETAIL_FORMULA_MAP.put("99","otherFormulaServiceImpl");//其他
    }

    /**
     * 责任属性实现
     */
    public final static Map<String,String> ATTRIBUTES_MAP=new HashMap<>();
    static{
        //责任属性
        ATTRIBUTES_MAP.put("02","disabilityDutyAttributeServiceImpl");//伤残
        ATTRIBUTES_MAP.put("04","medicalDutyAttributeServiceImpl");//医疗
        ATTRIBUTES_MAP.put("05","allowanceDutyAttributeServiceImpl");//津贴

    }
    /**
     * 免赔额数据初始化
     */
    public final static Map<String,String> REMIT_INIT_MAP=new HashMap<>();
    static{
        //责任免赔额
        //REMIT_INIT_MAP.put("year","yearRemitAmountInitServiceImpl");//年免赔
        //REMIT_INIT_MAP.put("season","") ;//季免赔：暂没有这种类型业务 以后也许有
        //REMIT_INIT_MAP.put("month","monthRemitAmountInitServiceImpl");//月免赔：暂没有这种类型业务 以后也许有
        REMIT_INIT_MAP.put("day","dayRemitAmountInitServiceImpl");//日免赔
        REMIT_INIT_MAP.put("times","timesRemitAmountInitServiceImpl");//次免赔


    }

    public final static String SETTLE_DEADLINE = "E002";
    public final static String REGISTRATION_DEADLINE = "E001";
    public final static String REOPEN_SETTLE_DEADLINE = "reopen-settle-deadline";
    public final static String NO_ESTIMATELOSS = "1";
    public final static String NO_ENDCASE = "2";
    public final static String NO_RECORD_CLAIM_LOG = "3";


    public final static Map<String, String> OUTTYPE_MAP = new HashMap<>();
    static {
        OUTTYPE_MAP.put(REGISTRATION_DEADLINE,"首次结案超时");
        OUTTYPE_MAP.put(SETTLE_DEADLINE,"立案超时");
        OUTTYPE_MAP.put(REOPEN_SETTLE_DEADLINE,"重开结案超时");
        OUTTYPE_MAP.put(NO_ESTIMATELOSS,"未估损");
        OUTTYPE_MAP.put(NO_ENDCASE,"未结案");
        OUTTYPE_MAP.put(NO_RECORD_CLAIM_LOG,"未记录理赔日志");
    }
}
