package com.paic.ncbs.claim.service.casezero.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.constant.investigate.NoConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.common.enums.SmsTypeEnum;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.prepay.PrePayMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.casezero.ZeroCancelAuditInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import com.paic.ncbs.claim.mq.producer.MqProducerProblemCaseService;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.casezero.CaseZeroCancelService;
import com.paic.ncbs.claim.service.common.ClaimUpdateDocumentFullDateService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.EndCaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.other.CommonService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicySurrenderInfoService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleValidateService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.trace.PersonTraceService;
import com.paic.ncbs.document.model.dto.VoucherTypeEnum;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service("caseZeroCancelService")
@RefreshScope
public class CaseZeroCancelServiceImpl implements CaseZeroCancelService {
    private static final String VERIFY_OPTIONS = ",verifyOptions=";

    private static final String CASE_TIMES_MARK = ",caseTimes=";


    @Autowired
    private CaseZeroCancelMapper caseZeroCancelDao;

    @Autowired
    private SettleValidateService settleValidateService;

    @Autowired
    private CommonParameterService commonParameterService;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private EndCaseService endCaseService;

    @Autowired
    private PrintService printService;

    @Autowired
    @Lazy
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper ;

    @Autowired
    private CommonService commonService ;
    @Autowired
    private PrePayMapper prePayMapper;

    @Autowired
    PolicyPayService policyPayService;

    @Autowired
    PaymentItemService paymentItemService;

    @Autowired
    private FeePayService feePayService;

    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @Autowired
    private ClmsPolicySurrenderInfoService clmsPolicySurrenderInfoService;
    @Autowired
    private SmsInfoService smsInfoService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private TPAFeign tpaFegin;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Value("${zeroCancelCase.autoPassReason: null}")
    private String autoPassCancelReason;

    @Autowired
    private ClaimUpdateDocumentFullDateService claimUpdateDocumentFullDateService;

    @Autowired
    private MqProducerProblemCaseService mqProducerProblemCaseService;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private TaskInfoService taskInfoService ;
    @Autowired
    private PersonTraceService personTraceService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Value("${autoZeroCancel.productPackage}")
    private List<String> autoZeroCancelPackage;

    @Override
    public CaseZeroCancelDTO getZeroCancelApplyInfo(String reportNo, Integer caseTimes, String status) throws GlobalBusinessException {
        CaseZeroCancelDTO caseZeroCancelDTO = new CaseZeroCancelDTO();
        // 查询当前案件是零注申请审批完成的次数
        int applyTimes = caseZeroCancelDao.getMaxApplyTimes(reportNo, caseTimes) + 1;
        // 查询未完成或待审批的零注申请
        if (EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_TEMP.equals(status)) {
            caseZeroCancelDTO = caseZeroCancelDao.getTempApplyInfo(reportNo, caseTimes, applyTimes);
        } else if (EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING.equals(status)) {
            List<CaseZeroCancelDTO> caseZeroCancelInfoList = caseZeroCancelDao.getCaseZeroCancelApplyAuditList(reportNo, caseTimes);
            if (ListUtils.isNotEmpty(caseZeroCancelInfoList)) {
                caseZeroCancelDTO = caseZeroCancelInfoList.get(0);
            }
        }
        caseZeroCancelDTO.setApplyTimes(applyTimes);
        return caseZeroCancelDTO;
    }

    @Transactional
    @Override
    public void saveCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO) {
        String reportNo = zeroCancelDTO.getReportNo();
        Integer caseTimes = zeroCancelDTO.getCaseTimes();
        String taskDefinitionKey = zeroCancelDTO.getTaskDefinitionKey();

        WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
        if ("1".equals(zeroCancelDTO.getApplyType()) && !ConstValues.YES.equals(wholeCase.getIsRegister())) {
            throw new GlobalBusinessException(ErrorCode.EndCase.ERROR_CANCEL_CHECK_CASE_IS_REGISTER, "请完成立案");
        }

        List<PolicyInfoDTO> policyInfoListByReportNo = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        String productPackageType = "";
        if (!CollectionUtils.isEmpty(policyInfoListByReportNo)) {
            productPackageType = policyInfoListByReportNo.get(0).getProductPackageType();
        }

        if("1".equals(zeroCancelDTO.getApplyType())) {
            zeroCancelDTO.setCancelType(null);
        }

        if("2".equals(zeroCancelDTO.getApplyType()) && ConstValues.YES.equals(wholeCase.getIsRegister())) {
            zeroCancelDTO.setCancelType("5");
        }

        String bpmKey = taskInfoMapper.getPendingTaskCount(reportNo, caseTimes);
        // 判断发起零注的页面是否是 待处理任务的页面，以防是理算退回收单做的零注申请
        if (StringUtils.isNotEmpty(bpmKey) && !bpmKey.equals(taskDefinitionKey)){
            zeroCancelDTO.setTaskDefinitionKey(bpmKey);
            taskDefinitionKey = bpmKey;
        }
        String status = zeroCancelDTO.getStatus();
        LogUtil.audit("#零注申请保存数据,reportNo={},caseTimes={},任务节点={},状态：{}", reportNo, caseTimes,taskDefinitionKey,status);
        zeroCancelDTO.setApplyTimes(caseZeroCancelDao.getMaxApplyTimes(reportNo, caseTimes) + 1);
        //校验案件是否结案&是否已存在零注流程&是否已存在其他流程
        this.checkCaseZeroCancelApply(zeroCancelDTO);
        //校验责任明细和责任明细基本保额不能为空
        //settleValidateService.validDutyDetailNull(reportNo, caseTimes);
        if(EndCaseConstValues.APPLY_TYPE_ZERO.equals(zeroCancelDTO.getApplyType()) && (Objects.equals(bpmKey,BpmConstants.OC_REPORT_TRACK) || Objects.equals(bpmKey,BpmConstants.OC_CHECK_DUTY))){
            claimUpdateDocumentFullDateService.updateDocFullDate(reportNo,caseTimes,bpmKey);
        }
        // 判断是否是零注 是的话提示用户删除费用
        if(EndCaseConstValues.APPLY_TYPE_CANCEL.equals(zeroCancelDTO.getApplyType())) {
            //获取理赔费用信息
            FeePayDTO feePayDTO = new FeePayDTO();
            feePayDTO.setReportNo(reportNo);
            feePayDTO.setCaseTimes(caseTimes);
            feePayDTO.setClaimType(SettleConst.CLAIM_TYPE_PAY);
            List<FeeInfoDTO> feeInfoList = Optional.ofNullable(feePayService.getFeePayByParam(feePayDTO)).orElse(new ArrayList<>());
            if (!CollectionUtils.isEmpty(feeInfoList)){
                throw new GlobalBusinessException("注销案件不可录入理赔费用！请退回删除相关信息。");
            }
        }
        //保存零注申请信息
        this.addCaseZeroCancelApply(zeroCancelDTO);
        String userId = StringUtils.isEmptyStr(WebServletContext.getUserId()) ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();

        //工作流，挂起原流程，启动零注审批，更新案件流程
        if (EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING.equals(status)){
            PersonTranceRequestVo personTranceRequestVo = new PersonTranceRequestVo();
            personTranceRequestVo.setCaseTimes(zeroCancelDTO.getCaseTimes());
            personTranceRequestVo.setFlag("L");
            personTranceRequestVo.setReportNo(zeroCancelDTO.getReportNo());
            bpmService.newSuspendOrActiveTask_oc(reportNo,caseTimes,taskDefinitionKey,true);
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT, "发起", null, userId);
            bpmService.startProcess_oc(reportNo,caseTimes, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
            caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CANCEL_WAIT_APPROVING.getCode());
            //如下申请原因，审批自动通过
            //不在保期 007/非指定医院就诊 009/无可赔付费用 015/发票姓名不符 016/超索赔天数 022
            if(autoPassCancelReason.contains(zeroCancelDTO.getReasonCode())
                    || zeroCancelDTO.getVerifyPass()
                    || (Arrays.asList("001","002","101","014","020","023","024").contains(zeroCancelDTO.getReasonCode()) && autoZeroCancelPackage.contains(productPackageType))) {
                UserInfoDTO userInfoDTO = WebServletContext.getUser();
                if (!StringUtils.isEqualStr(userInfoDTO.getUserCode(), ConstValues.SYSTEM_UM)) {
                    UserInfoDTO sysTemUserInfoDTO = new UserInfoDTO();
                    sysTemUserInfoDTO.setUserCode(ConstValues.SYSTEM_UM);
                    sysTemUserInfoDTO.setUserName(ConstValues.SYSTEM_UM);
                    sysTemUserInfoDTO.setComCode(ConfigConstValues.HQ_DEPARTMENT);
                    Objects.requireNonNull(WebServletContext.getRequest()).getSession().setAttribute(Constants.CURR_USER,
                            sysTemUserInfoDTO);
                    WebServletContext.getRequest().getSession().setAttribute(Constants.CURR_COMCODE,
                            ConfigConstValues.HQ_DEPARTMENT);
                    MDC.put(BaseConstant.USER_ID, ConstValues.SYSTEM_UM);
                }
                this.zeroCancelAutoPass(zeroCancelDTO);
            }
        }

    }

    @Override
    @Transactional
    public void addCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO) throws GlobalBusinessException {

        String reportNo = zeroCancelDTO.getReportNo();
        Integer caseTimes = zeroCancelDTO.getCaseTimes();
        List<ReportInfoExEntity> reportInfoexs = reportInfoExService.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfoEx = reportInfoexs.get(0);
        String userId = WebServletContext.getUserId();
        TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK);
        if("1".equals(reportInfoEx.getClaimDealWay()) && ConstValues.SYSTEM_UM.equals(userId) ) {
            userId = tdto.getAssigner();
        }
        zeroCancelDTO.setApplyUm(userId);
        zeroCancelDTO.setUpdatedBy(userId);
        zeroCancelDTO.setCreatedBy(userId);
        // 查询暂存状态的零注信息id
        String idAhcsZeroCancelApply = caseZeroCancelDao.getIdZeroCancelApplyByReportNo(reportNo, caseTimes, EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_TEMP);
        if (StringUtils.isNotEmpty(idAhcsZeroCancelApply)) {
            zeroCancelDTO.setIdAhcsZeroCancelApply(idAhcsZeroCancelApply);
            LogUtil.audit("#更新零注申请信息# reportNo={},userId={},idAhcsZeroCancelApply{}", reportNo, userId, idAhcsZeroCancelApply);
            caseZeroCancelDao.modifyCaseZeroCancelApply(zeroCancelDTO);
        } else {
            idAhcsZeroCancelApply = UuidUtil.getUUID();
            zeroCancelDTO.setIdAhcsZeroCancelApply(idAhcsZeroCancelApply);
            LogUtil.audit("#新增零注申请信息# reportNo={},userId={}, idAhcsZeroCancelApply{}", reportNo, userId, idAhcsZeroCancelApply);
            caseZeroCancelDao.addCaseZeroCancelApply(zeroCancelDTO);
        }
    }

    @Transactional
    @Override
    public void saveCaseZeroCancelAudit(CaseZeroCancelDTO caseZeroCancelDTO) throws GlobalBusinessException {
        String reportNo = caseZeroCancelDTO.getReportNo();
        Integer caseTimes = caseZeroCancelDTO.getCaseTimes();
        String taskDefinitionKey = caseZeroCancelDTO.getTaskDefinitionKey();
        String verifyOptions = caseZeroCancelDTO.getVerifyOptions();//审批意见(1-同意;2-不同意)
        String applyType = caseZeroCancelDTO.getApplyType();//申请类型(1-零结，2-注销)
        String userId = StringUtils.isEmptyStr(WebServletContext.getUserId()) ? ConstValues.SYSTEM_UM : WebServletContext.getUserId();
        String userName = WebServletContext.getUserName();
        String taskId = taskInfoMapper.getTaskId(reportNo,caseTimes,BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
        boolean verifyPass = false;
        LogUtil.audit("#零结注销审批发送#入参# reportNo:{},caseTimes:{},taskDefinitionKey={}", reportNo, caseTimes, taskDefinitionKey);

        if (EndCaseConstValues.AUDIT_AGREE_CODE.equals(verifyOptions)) {
            verifyPass = true;
            String currStatus = caseProcessService.getCaseProcessStatus(reportNo, caseTimes);
            boolean isCaseFinish = (ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(currStatus)) || (ConfigConstValues.PROCESS_STATUS_ZORE_CANCEL.equals(currStatus))
                    || (ConfigConstValues.PROCESS_STATUS_CANCELLATION.equals(currStatus));
            if (isCaseFinish) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,"案件已经结案，不允许在做零注操作");
            }
            // 案件注销 同步第三方系统 判断是否立案
            WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
            if(null != wholeCaseBaseDTO){
                String endCseNo =commonService.generateNo( NoConstants.END_CASE_NO, VoucherTypeEnum.END_CASE_NO,WebServletContext.getDepartmentCode()) ;
                wholeCaseBaseMapper.modifyWholeCaseEndCaseNo(endCseNo,reportNo, caseTimes);
            }
            LogUtil.audit("#判断案件是否立案#,整案信息wholeCaseBaseDTO={}", JSONObject.toJSONString(wholeCaseBaseDTO));

            if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
                throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能提交零注！");
            }
        }

        CaseZeroCancelDTO currentCaseZeroCancelInfo = this.getZeroCancelApplyInfo(reportNo, caseTimes, EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING);

        caseZeroCancelDTO.setUpdatedBy(userId);
        caseZeroCancelDTO.setApplyUm(currentCaseZeroCancelInfo.getApplyUm());
        caseZeroCancelDTO.setApplyType(applyType);
        caseZeroCancelDTO.setApplyDate(currentCaseZeroCancelInfo.getApplyDate());
        caseZeroCancelDTO.setVerifyUm(userId);
        caseZeroCancelDTO.setStatus(EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_APPROVED);
        LogUtil.audit("#零注审核更新案件数据, reportNo={},caseTimes={},verifyPass={}", reportNo, caseTimes, verifyPass);
        bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
        if (verifyPass) {
            //零结注销审批通过，该报案之前所有流程更新为已处理
            bpmService.closeAll_oc(reportNo, caseTimes);
            clmsPolicySurrenderInfoService.checkSurrenderIsOtherCase(reportNo,caseTimes);
            clmsPolicySurrenderInfoService.applyPolicySurrender(reportNo,caseTimes);
            caseZeroCancelDTO.setVerifyOptions(EndCaseConstValues.AUDIT_AGREE_CODE);
            //保存零注审批信息
            caseZeroCancelDao.saveCaseZeroCancelAudit(caseZeroCancelDTO);
            //删除赔付表数据、赔款支付项数据、批单
            policyPayService.deletePolicyPays(reportNo,caseTimes);
            //更新整案信息和案件流程
            asyncUpdateCaseData(reportNo, caseTimes);
            if (EndCaseConstValues.APPLY_TYPE_ZERO.equals(applyType)){
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CASE_CLOSED.getCode());

                // 零结和拒赔 判断是否有费用需要支付
                paymentItemService.checkAndPayFee(reportNo,caseTimes);

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 零结，回写渠道已结案
                        SyncCaseStatusDto dto = new SyncCaseStatusDto();
                        dto.setReportNo(reportNo);
                        dto.setCaseTimes(caseTimes);
                        dto.setCaseStatus(SyncCaseStatusEnum.ENDCASE);
                        dto.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED);
                        mqProducerSyncCaseStatusService.syncCaseStatus(dto);

                        // 零结，发送再保
                        RepayCalDTO repayCalDTO = new RepayCalDTO();
                        repayCalDTO.setReportNo(reportNo);
                        repayCalDTO.setCaseTimes(caseTimes);
                        repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
                        repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED);
                        reinsuranceService.sendReinsurance(repayCalDTO);

                        //重开案件无赔款不发短信
                        Boolean flag = true;
                        if(caseTimes > 1){
                            Integer count = paymentItemMapper.isPay(reportNo,caseTimes);
                            if(count == 0){
                                flag = false;
                            }
                        }
                        if(flag){
                            // 零注审批通过之后发送短信:实时发送不加businessId了
                            String requestId = MDC.get(BaseConstant.REQUEST_ID);
                            smsInfoService.sendBusinessSmsAsync(SmsTypeEnum.ZERO_CANCEL_REVIEW,reportNo,caseTimes,null,requestId);
                        }
                    }
                });
            }else if(EndCaseConstValues.APPLY_TYPE_CANCEL.equals(applyType)){
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.CASE_CANCELLED.getCode());
                //保存监管赔付中间表
                paymentItemService.saveCompensationIntermediateData(reportNo, caseTimes);

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 注销，回写渠道立案取消
                        SyncCaseStatusDto dto = new SyncCaseStatusDto();
                        dto.setReportNo(reportNo);
                        dto.setCaseTimes(caseTimes);
                        dto.setCaseStatus(SyncCaseStatusEnum.CANCEL);
                        mqProducerSyncCaseStatusService.syncCaseStatus(dto);

                        // 注销，发送再保
                        RepayCalDTO repayCalDTO = new RepayCalDTO();
                        repayCalDTO.setReportNo(reportNo);
                        repayCalDTO.setCaseTimes(caseTimes);
                        repayCalDTO.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
                        repayCalDTO.setIndemnityConclusion(ConfigConstValues.INDEMNITYCONCLUSION_CANCEL);
                        reinsuranceService.sendReinsurance(repayCalDTO);

                        //重开案件无赔款不发短信
                        Boolean flag = true;
                        if(caseTimes > 1){
                            Integer count = paymentItemMapper.isPay(reportNo,caseTimes);
                            if(count == 0){
                                flag = false;
                            }
                        }
                        if(flag) {
                            // 零注审批通过之后发送短信:实时发送不加businessId了
                            String requestId = MDC.get(BaseConstant.REQUEST_ID);
                            smsInfoService.sendBusinessSmsAsync(SmsTypeEnum.ZERO_CANCEL_REVIEW, reportNo, caseTimes, null, requestId);
                        }
                    }
                });
            }
            printService.sendPrintCore(reportNo,caseTimes);
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT, "通过", caseZeroCancelDTO.getVerifyRemark(), userId);
        } else {
            caseZeroCancelDTO.setVerifyOptions(EndCaseConstValues.AUDIT_DISAGREE_CODE);
            //保存零注审批信息
            caseZeroCancelDao.saveCaseZeroCancelAudit(caseZeroCancelDTO);
            //重启之前的流程
            bpmService.newSuspendOrActiveTask_oc(reportNo,caseTimes,taskDefinitionKey,false);
            caseProcessService.updateCaseProcess(reportNo, caseTimes, BpmConstants.TASK_PROCESS_MAP.get(taskDefinitionKey));
            //操作记录
            operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT, "不通过", caseZeroCancelDTO.getVerifyRemark(), userId);
            //零注审批不同意时添加消息提醒
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_ZERO_CANCEL_DEPT_AUDIT);
            noticesDTO.setReportNo(reportNo);
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(caseTimes);
            TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
            if (taskInfoDTO!=null){
                noticeService.saveNotices(noticesDTO,taskInfoDTO.getAssigner());
            }
        }
        //TPA全流程案件，调用TPA答复接口回复
        List<ReportInfoExEntity>  reportInfos = reportInfoExMapper.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfo = reportInfos.get(0);
        if("1".equals(reportInfo.getClaimDealWay())
                && !"channel".equals(reportInfo.getCompanyId())
                && 1 == caseTimes) {

            RequestData requestData = new RequestData();
            requestData.setRegistNo(reportNo);
            requestData.setProblemNo(taskId);
            requestData.setProblemType("04");
            requestData.setCaseConclusion("99");
            //TPA问题件答复接口增加沟通人
            requestData.setDealUser(ConstValues.SYSTEM_UM.equals(userId) ? userId : userName+"-"+userId);
            try {
                requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
            } catch (ParseException e) {
                LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
            }
            requestData.setCaseConclusionDetail(verifyOptions);
            requestData.setRemark(caseZeroCancelDTO.getVerifyRemark());

            if(caseZeroCancelDTO.getIsAutoPass()) {
                //因TPA自动通过时，会先收到答复后收到申请成功，谐筑无法解决这种时序问题，故改用mq
                mqProducerProblemCaseService.requestProblemCase(requestData);
            } else {
                ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
                problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
                problemCaseRequestDTO.setRequestData(requestData);
                LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",reportNo, JSON.toJSONString(problemCaseRequestDTO));
                ProblemCaseResponseDTO response = tpaFegin.response(problemCaseRequestDTO);
                LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",reportNo, JSON.toJSONString(response));

            }
        }
    }

    @Override
    public boolean getIsContinueByReportNo(String reportNo, Integer caseTimes) {
        return caseZeroCancelDao.getIsContinueByReportNo(reportNo, caseTimes) > 0;
    }


    @Override
    public void checkCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO) throws GlobalBusinessException{
        String reportNo = zeroCancelDTO.getReportNo();
        Integer caseTimes = 1;
        if (zeroCancelDTO.getCaseTimes() != null) {
            caseTimes = zeroCancelDTO.getCaseTimes();
        }
        LogUtil.audit("#校验异常案件零结注销申请#入参# reportNo={}, caseTimes", reportNo, caseTimes);
        //如果是重开赔案，且之前是赔付，协议赔付，通融赔付，不能拒赔，否则提升“重开案件不允许发起零注申请”
        Integer verfiyCodeCount = wholeCaseBaseMapper.getverfiyCodeCount(reportNo,caseTimes);
        if(caseTimes>1 && verfiyCodeCount>0){
            throw new GlobalBusinessException("重开案件不允许发起零注申请");
        }
        //校验当前流程是否有冲突
        bpmService.processCheck(reportNo,BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT,BpmConstants.OPERATION_INITIATE);

        /*  delete by zjtang 取消旧校验逻辑
        // 校验是否存在未完成的零注流程,若存在,提示前次零注未完成审批
        if (this.isExistApplyTaskNotCompleted(zeroCancelDTO)) {
            LogUtil.audit("报案号:{}存在零注未完成审批,请先完成审批!",reportNo );
            throw new GlobalBusinessException(ErrorCode.EndCase.ERROR_CANCEL_CHECK_NOT_COMPLETE);
        }
        //校验当前案件是否存在预赔记录，若存在，就不能零注
        if(prePayMapper.getPrePayCount(reportNo,caseTimes) > 0 && paymentItemMapper.getPrePayCount(reportNo, caseTimes) > 0){
            throw new GlobalBusinessException("当前案件有预赔记录！不能零注！");
        }
        //校验当前案件是否存在除过主任务外其他未完成的流程,若存在，就不能发起零注
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setCaseTimes(caseTimes);
        taskInfoDTO.setReportNo(reportNo);
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getUndoTaskInfoList(taskInfoDTO);
        if(ListUtils.isNotEmpty(taskInfoVOList) && taskInfoVOList.size() > 0) {
            throw new GlobalBusinessException("当前案件存在其他未处理任务，不能发起零注！");
        }

        if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
            throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能发起零注！");
        }
         */

        // 查询整案信息，若已结案则不能零注申请
        String wholeCaseStatus = wholeCaseBaseService.getWholeCaseStatus(reportNo, caseTimes);
        LogUtil.audit("案件已经结案(0是已结案,1是已报案)={}", wholeCaseStatus);
        if (EndCaseConstValues.WHOLE_CASE_END_STATUS.equals(wholeCaseStatus)) {
            throw new GlobalBusinessException(ErrorCode.EndCase.ERROR_CANCEL_CHECK_CASE_IS_END);
        }
    }

    @Override
    public boolean isExistApplyTaskNotCompleted(CaseZeroCancelDTO zeroCancelDTO) {
        return caseZeroCancelDao.getCaseZeroCancelApplyCount(zeroCancelDTO.getReportNo(),
                zeroCancelDTO.getCaseTimes(), EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING) > 0;
    }

    @Override
    public List<ZeroCancelAuditInfoVO> getAuditInfoList(String reportNo, Integer caseTimes) {
        // 查询零注审批信息
        List<ZeroCancelAuditInfoVO> zeroCancelAuditInfoVOList = caseZeroCancelDao.getAuditInfoList(reportNo, caseTimes);
        if (ListUtils.isNotEmpty(zeroCancelAuditInfoVOList)) {
            // 从基表查询零注原因
            Map<String, String> reasonMap = this.getReasonMap();
            String applyReason;
            //将意见编码转换成对应的中文
            for (ZeroCancelAuditInfoVO zeroCancelAuditInfoVO : zeroCancelAuditInfoVOList) {
                String verifyOptionsCode = zeroCancelAuditInfoVO.getVerifyOptionsCode();
                if (EndCaseConstValues.AUDIT_AGREE_CODE.equals(verifyOptionsCode)) {
                    zeroCancelAuditInfoVO.setVerifyOptions(EndCaseConstValues.AUDIT_AGREE_NAME);
                } else if (EndCaseConstValues.AUDIT_DISAGREE_CODE.equals(verifyOptionsCode)) {
                    zeroCancelAuditInfoVO.setVerifyOptions(EndCaseConstValues.AUDIT_DISAGREE_NAME);
                }
                applyReason = reasonMap.get(zeroCancelAuditInfoVO.getApplyReasonCode());
                zeroCancelAuditInfoVO.setApplyReason(applyReason);

            }
        }
        return zeroCancelAuditInfoVOList;
    }


    @Override
    public Map<String, String> getReasonMap() throws GlobalBusinessException {
        List<CommonParameterTinyDTO> reasonList = commonParameterService.getCommonParameterList(new String[]{EndCaseConstValues.ZERO_CANCEL_COLLECTION_CODE});
        return reasonList.stream().collect(Collectors.toMap(CommonParameterTinyDTO::getValueCode,CommonParameterTinyDTO::getValueChineseName));
    }

    @Override
    public CaseZeroCancelDTO getLastZeroCancelInfo(String reportNo, Integer caseTimes) {
        List<CaseZeroCancelDTO> caseZeroCancelInfoList = caseZeroCancelDao.getZeroCancelInfoList(reportNo, caseTimes);
        if (ListUtils.isNotEmpty(caseZeroCancelInfoList)) {
            return caseZeroCancelInfoList.get(0);
        }
        LogUtil.audit("#获取最新的零注信息, reportNo={}, caseTimes={}#", reportNo, caseTimes);
        return null;
    }


    public void asyncUpdateCaseData(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        CaseZeroCancelDTO caseZeroCancelDTO = getLastZeroCancelInfo(reportNo, caseTimes);
        if(caseZeroCancelDTO !=null){
            String verifyOptions = caseZeroCancelDTO.getVerifyOptions();
            String status = caseZeroCancelDTO.getStatus();
            LogUtil.audit("#零注审核后进行结案处理#入参#reportNo=" + reportNo + CASE_TIMES_MARK + caseTimes
                     + VERIFY_OPTIONS + verifyOptions + ",status=" + status);
            if(EndCaseConstValues.AUDIT_AGREE_CODE.equals(verifyOptions)){
                if(EndCaseConstValues.APPLY_TYPE_ZERO.equals(caseZeroCancelDTO.getApplyType())){
                    endCaseService.endCaseZeroCancel(reportNo, caseTimes, EndCaseConstValues.APPLY_TYPE_ZERO);
                }else if(EndCaseConstValues.APPLY_TYPE_CANCEL.equals(caseZeroCancelDTO.getApplyType())){
                    endCaseService.endCaseZeroCancel(reportNo, caseTimes, EndCaseConstValues.APPLY_TYPE_CANCEL);
                }
            }
        }
    }

    /**
     * 自动案件零注流程处理（包含自动零注审核）
     *
     * @param reportNo
     * @param caseTimes
     * @param applyType
     * @param applyReasonCode
     * @param applyReasonDetails
     * @param verifyPass
     */
    @Override
    @Transactional
    public void autoCaseZeroCancelProcess(String reportNo, Integer caseTimes, String applyType,
                                          String applyReasonCode, String applyReasonDetails, Boolean verifyPass) {
        RapeCheckUtil.checkParamEmpty(reportNo, "报案号");
        RapeCheckUtil.checkParamEmpty(caseTimes, "赔付次数");
        RapeCheckUtil.checkParamEmpty(applyType, "申请类型");
        RapeCheckUtil.checkParamEmpty(applyReasonCode, "申请原因");
        RapeCheckUtil.checkParamEmpty(verifyPass, "是否审核通过");

        // 更新上下文信息中的用户信息
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        String departmentCode = WebServletContext.getDepartmentCode();
        if (!StringUtils.isEqualStr(userInfoDTO.getUserCode(), ConstValues.SYSTEM_UM)) {
            UserInfoDTO sysTemUserInfoDTO = new UserInfoDTO();
            sysTemUserInfoDTO.setUserCode(ConstValues.SYSTEM_UM);
            sysTemUserInfoDTO.setUserName(ConstValues.SYSTEM_UM);
            sysTemUserInfoDTO.setComCode(ConfigConstValues.HQ_DEPARTMENT);
            Objects.requireNonNull(WebServletContext.getRequest()).getSession().setAttribute(Constants.CURR_USER,
                    sysTemUserInfoDTO);
            WebServletContext.getRequest().getSession().setAttribute(Constants.CURR_COMCODE,
                    ConfigConstValues.HQ_DEPARTMENT);
            MDC.put(BaseConstant.USER_ID, ConstValues.SYSTEM_UM);
        }

        // 1、自动发起零注申请
        CaseZeroCancelDTO zeroCancelDTO = new CaseZeroCancelDTO();
        zeroCancelDTO.setReportNo(reportNo);
        zeroCancelDTO.setCaseTimes(caseTimes);
        zeroCancelDTO.setApplyType(applyType);
        zeroCancelDTO.setReasonCode(applyReasonCode);
        zeroCancelDTO.setApplyReasonDetails(applyReasonDetails);
        zeroCancelDTO.setStatus(EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_PROCESSING);
        zeroCancelDTO.setVerifyPass(verifyPass);
        this.saveCaseZeroCancelApply(zeroCancelDTO);
        // 自动零注是在收单理算环节，暂时不用MQ通知TPA平台了
        // 还原上下文用户信息
        Objects.requireNonNull(WebServletContext.getRequest()).getSession().setAttribute(Constants.CURR_USER,
                userInfoDTO);
        WebServletContext.getRequest().getSession().setAttribute(Constants.CURR_COMCODE, departmentCode);
        MDC.put(BaseConstant.USER_ID, userInfoDTO.getUserCode());
    }

    private void zeroCancelAutoPass(CaseZeroCancelDTO zeroCancelDTO) {
        // 2、零注审核进行一次点位
        TaskInfoDTO taskInfo = taskInfoMapper.findLatestByReportNoAndBpmKey(zeroCancelDTO.getReportNo(), zeroCancelDTO.getCaseTimes(), BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
        taskInfoMapper.reAssign(taskInfo.getTaskId(),ConstValues.SYSTEM_UM,ConstValues.SYSTEM_UM,taskInfo.getDepartmentCode());

        // 3、自动零注审核通过
        zeroCancelDTO.setVerifyPass(true);
        zeroCancelDTO.setStatus(EndCaseConstValues.ZERO_CANCEL_APPLY_STATUS_APPROVED);
        zeroCancelDTO.setVerifyOptions(EndCaseConstValues.AUDIT_AGREE_CODE);
        zeroCancelDTO.setVerifyRemark(zeroCancelDTO.getApplyReasonDetails());
        zeroCancelDTO.setIsAutoPass(true);
        this.saveCaseZeroCancelAudit(zeroCancelDTO);
    }
}
