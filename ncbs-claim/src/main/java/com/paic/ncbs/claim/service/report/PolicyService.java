package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.PolicyFilesInfoVO;
import com.paic.ncbs.claim.model.dto.report.PageDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyParamDTO;
import com.paic.ncbs.claim.model.vo.nbs.MiniOrderInfo;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.model.vo.report.PolicyRiskQueryVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 保单服务
 */
public interface PolicyService {

    /**
     * 获取保单信息
     */
    PageDTO<OcasPolicyDTO> getPolicyList(String departmentCode, PolicyQueryVO queryVO);

    /**
     * 获取保单附件信息
     */
    List<PolicyFilesInfoVO> getPolicyFileList(PolicyQueryVO queryVO);

    /**
     * 获取保单附件信息
     */
    List<PolicyFilesInfoVO> getPolicyFileListNew(PolicyQueryVO queryVO);

    /**
     * 获取保单保费信息
     */
    BigDecimal getPremiumByPolicyNo(PolicyQueryVO queryVO);
    /**
     * 获取保单被保人信息
     */
    PageDTO<OcasInsuredDTO> getPolicyInsuredList(String departmentCode, PolicyQueryVO queryVO);

    /**
     * 获取保单标的信息
     */
    PageDTO<PolicyRiskSubPropDTO> getPoliyRiskSubPropList(String departmentCode, PolicyQueryVO queryVO);

    /**
     * ES获取保单标的信息
     */
    List<PolicyRiskSubPropDTO> getEsPlyRiskSubPropList(PolicyRiskQueryVO riskPropQueryVO) throws Exception;

    List<RiskPropertyParamDTO> getRiskPropertyParamByPolicyNo(String policyNo);

    /**
     * 获取保单下的小订单
     * @param riskPropQueryVO
     * @return
     */
    List<MiniOrderInfo> getPoliyMinOrderList(PolicyRiskQueryVO riskPropQueryVO);

    /**
     * 获取保单下标类型
     * @param policyNo
     * @return
     */
    String getRiskGroupType(String policyNo);
}
