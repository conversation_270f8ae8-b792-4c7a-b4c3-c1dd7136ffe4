package com.paic.ncbs.claim.model.vo.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * SOP详情VO
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
@Data
@ApiModel("SOP详情")
public class SopDetailVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String idSopDetail;

    @ApiModelProperty("SOP主表ID")
    private String idSopMain;

    @ApiModelProperty("适用环节列表")
    private List<String> taskBpmKey;

    @ApiModelProperty("适用环节名称（逗号分隔）")
    private String taskBpmKeyNames;

    @ApiModelProperty("SOP规则内容")
    private String sopContent;

    @ApiModelProperty("排序号")
    private Integer sortOrder;

    @ApiModelProperty("文件信息列表")
    private List<SopFileVO> fileList;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("修改人")
    private String updatedBy;
}