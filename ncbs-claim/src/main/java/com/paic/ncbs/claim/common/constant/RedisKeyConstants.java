package com.paic.ncbs.claim.common.constant;

public class RedisKeyConstants {

    public static final String COMMON = "ncbs-claim:";

    public static final String ACCIDENT_CODE_LOCK = COMMON + "accidentCodeLock";

    public static final String ACCIDENT_CODE_SEQ = COMMON + "accidentCodeSeq:";

    public static final String BATCH_CLOSE_CASE_LOCK = COMMON + "batchCloseCaseLock:";

    public static final String REGISTER_SAVE_LOCK = COMMON + "registerSaveLock:";
    public static final String VERIFY_SAVE_LOCK = COMMON + "addVerifyLock:";
    public static final String MERGE_PAY_LOCK = COMMON + "mergePayLock:";
    public static final String RELEVY_FEE_SAVE_LOCK = COMMON + "relevyFeeSaveLock:";
    public static final String RELEVY_SAVE_LOCK = COMMON + "relevySaveLock:";

    /**
     * 重灾自增序号
     */
    public static String getAccidentCodeSeq(String year) {
        return ACCIDENT_CODE_SEQ + year;
    }

    /**
     * 批量结案锁
     */
    public static String getBatchCloseCaseLock(String thirdBatchNo) {
        return ACCIDENT_CODE_SEQ + thirdBatchNo;
    }
}
