package com.paic.ncbs.claim.controller.report;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyStatusEnum;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentUserMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.ahcs.PolicyCustomerVo;
import com.paic.ncbs.claim.model.dto.ocas.*;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.PageDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskGroupDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyParamDTO;
import com.paic.ncbs.claim.model.vo.nbs.MiniOrderInfo;
import com.paic.ncbs.claim.model.vo.ocas.OcasPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.ocas.OcasReportQueryVO;
import com.paic.ncbs.claim.model.vo.policy.OCASPolicyVO;
import com.paic.ncbs.claim.model.vo.report.*;
import com.paic.ncbs.claim.model.vo.user.DepartmentUserVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.ahcs.AhcsQueryPolicyService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.PolicyService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "保单信息获取")
@RestController
@RequestMapping("/report")
@RefreshScope
public class PolicyController {

    @Autowired
    private AhcsQueryPolicyService ahcsQueryPolicyService;

    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;

    @Autowired
    private OcasMapper ocasMapper;


    @Autowired
    private OcasRequest ocasRequest;

    @Autowired
    private TaskListService taskListService;

    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private PolicyService policyService;

    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private DepartmentUserMapper departmentUserMapper;

    @Value("${pos.api.enable:true}")
    private boolean posApiEnable;

    @Value("${ocas.policyTreatmentURL:https://atlas.inspci.com/report?id=1813854758692261978}")
    private String policyTreatmentURL;

    @GetMapping(value = "/getPolicyInfoByPolicyNoAndName")
    @ApiOperation(value = "根据保单号查询保单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "policyNo", value = "保单号", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "policyCerNo", value = "电子保单号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "selfCardNo", value = "自助卡号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "clientNo", value = "客户号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query")
    })
    public ResponseResult<Map<String, Object>> getPolicyDomainInfoByPolicyNo(String reportNo, String policyNo, String policyCerNo, String selfCardNo, String clientNo) {

        AhcsPolicyDomainDTO ahcsPolicyDomainDTO = null;
        Map<String, String> param = new HashMap<>();
        param.put("policyNo", policyNo);
        if(posApiEnable){
            CopyPolicyQueryVO queryVO = new CopyPolicyQueryVO(policyNo,null);
            ahcsPolicyDomainDTO = customerInfoStoreSAO.getPolicyDomainInfo(queryVO);
        }else{
            ahcsPolicyDomainDTO = customerInfoStoreSAO.getPolicyDomainInfoByPolicyNo(param);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("AhcsPolicyDomainDTO", ahcsPolicyDomainDTO);
        List<ReportCustomerInfoVO> insuredList = customerInfoStoreSAO.getInsuredList(param);
        List<ReportCustomerInfoVO> applicantInfoList = customerInfoStoreSAO.getApplicantInfoList(param);
        resultMap.put("holderPersonInfo", applicantInfoList.get(0));
        resultMap.put("insuredPersonInfo", insuredList);
        return ResponseResult.success(resultMap);
    }

    @GetMapping(value = "/policyCopyAndSendDetail")
    @ApiOperation(value = "保单抄送详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required =true , dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "policyNo", value = "保单号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = true, dataType = "Integer",dataTypeClass=Integer.class, paramType = "Query"),
            @ApiImplicitParam(name = "insuredName", value = "被保人", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "certificateNo", value = "证件号", required = true, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "mobileTelephone", value = "手机号", required = false, dataType = "String", dataTypeClass=String.class,  paramType = "Query"),
            @ApiImplicitParam(name = "certificateType", value = "证件类型", required = false, dataType = "String", dataTypeClass=String.class,  paramType = "Query")
    })
    public ResponseResult<Map<String, Object>> getPolicyDomainInfoByPolicyNo(String reportNo, String policyNo,Integer caseTimes, Pager pager
                               ,String insuredName ,String certificateNo,String mobileTelephone ,String certificateType) throws UnsupportedEncodingException {

        insuredName = URLDecoder.decode(insuredName,"UTF-8");
        Map<String, Object> resultMap= ahcsQueryPolicyService.policyCopyAndSendDetail(reportNo,policyNo,caseTimes,insuredName,certificateNo,pager,mobileTelephone,certificateType);
        return ResponseResult.success(resultMap);
    }

    @GetMapping(value = "/getCustomerPolicyInfo")
    @ApiOperation(value = "根据用户基本信息查询保单信息")
    public ResponseResult getCustomerPolicyInfo(CustomerQueryVo customerQueryVo, Pager pager) {
        checkcustomerQueryParam(customerQueryVo);
        OcasDTO param = new OcasDTO();
        param.setCertificateNo(customerQueryVo.getCertificateNo());
        param.setPolicyNo(customerQueryVo.getPolicyNo());
        //被保险人姓名
        param.setInsuredName(customerQueryVo.getInsuredName());
        //投保人姓名
        param.setApplicantName(customerQueryVo.getApplicantName());
        LogUtil.info("根据用户基本信息查询保单信息入参customerQueryVo{}", JSON.toJSONString(param));
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        List<OcasDTO> insuredList = ocasMapper.getInsuredList(param);
        List<CustomerPolicyVO> list = new ArrayList<>();
        for (OcasDTO dto : insuredList) {
            ReportCustomerInfoVO customerInfo = new ReportCustomerInfoVO();
            //前端客户号不为空,暂时固定用0
            customerInfo.setClientNo(dto.getClientNo());
            customerInfo.setSexCode(dto.getSexCode());
            customerInfo.setCertificateNo(dto.getCertificateNo());
            customerInfo.setName(dto.getInsuredName());
            customerInfo.setPolicyNo(dto.getPolicyNo());
            //被保险人、客户类型、职业类别、证件类型、证件号码
            RatingQueryVO ratingQueryVO = new RatingQueryVO(dto.getInsuredName());
            ratingQueryVO.setPhoneNumber(dto.getMobileTelephone());
            if(dto.getCertificateType()!= null && CertificateTypeEnum.ID_CARD.getType().equals(dto.getCertificateType())){
                ratingQueryVO.setIdentificationNumber(dto.getCertificateNo());
            }
            customerInfo.setClientType(customerInfoStoreSAO.queryCustomerRating(ratingQueryVO));
            customerInfo.setCertificateTypeName(CertificateTypeEnum.getName(dto.getCertificateType()));
            customerInfo.setProfessionClass(dto.getProfessionClass());
            CustomerPolicyVO vo = new CustomerPolicyVO();
            vo.setCustomerInfo(customerInfo);
            list.add(vo);
        }
        PageInfo<OcasDTO> pageInfo = new PageInfo<>(insuredList);
        pager.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();
        return ResponseResult.success(list, pager);
    }


    private void checkcustomerQueryParam(CustomerQueryVo customerQueryVo) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getCertificateNo()) && org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getPolicyNo())) {
            throw new GlobalBusinessException("证件号或保单号至少需要输入一个");
        }
        if (!org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getInsuredName()) && org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getPolicyNo())) {
            throw new GlobalBusinessException("参数被保险人姓名需和保单号一起使用");
        }
        if (!org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getApplicantName()) && org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getPolicyNo())) {
            throw new GlobalBusinessException("参数投保人姓名需和保单号一起使用");
        }
    }


    @ApiOperation(value = "根据身份证号查询保单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "certificateNo", value = "身份证号", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query")
    })
    @RequestMapping(value = "/getPolicyListByClientNo", method = RequestMethod.GET)
    public ResponseResult<List<OCASPolicyVO>> getPolicyListByClientNo(@RequestParam String certificateNo) {
        List<OCASPolicyVO> ocasPolicyVOS = customerInfoStoreSAO.getPolicyListByCertificationNo(certificateNo);
        return ResponseResult.success(ocasPolicyVOS);
    }

    @GetMapping(value = "/getPolicyCustomerInfoByPolicyNoStore")
    public ResponseResult getPolicyCustomerInfoByPolicyNoStore(PolicyCustomerVo policyCustomerVo, Pager pager) {
        Map<String, String> param = new HashMap<>();
        param.put("policyNo", policyCustomerVo.getDocumentNo());
        List<ReportCustomerInfoVO> customerList = customerInfoStoreSAO.getInsuredList(param);
        if (ListUtils.isEmptyList(customerList)) {
            return ResponseResult.success(new ArrayList<>(), pager);
        }

        List<CustomerPolicyVO> list = new ArrayList<>();
        for (int i = 0; i < customerList.size(); i++) {
            CustomerPolicyVO vo = new CustomerPolicyVO();
            vo.setCustomerInfo(customerList.get(i));
            list.add(vo);
        }

        return ResponseResult.success(list, pager);
    }

    @GetMapping(value = "/getCustomerPolicyInfoStore")
    public ResponseResult<List<CustomerPolicyVO>> getCustomerPolicyInfoStore(CustomerVO customerVO) {
        LogUtil.info("根据身份证号和姓名查询入参={}", JSON.toJSONString(customerVO));
        List<CustomerPolicyVO> listVO = getCustomerInfoStore(customerVO);
        LogUtil.info("根据身份证号和姓名查询客服系统出参={}", JSON.toJSONString(listVO));
        return ResponseResult.success(listVO);
    }

    private List<CustomerPolicyVO> getCustomerInfoStore(CustomerVO customerVO) {
        Set<String> policyNoSet = customerInfoStoreSAO.getPolicyByCertificationNo(customerVO.getCertificateNo());
        List<CustomerPolicyVO> list = new ArrayList<>();
        if (policyNoSet.size() < 1) {
            return list;
        }
        for (String ply : policyNoSet) {
            Map<String, String> param2 = new HashMap<>();
            param2.put("policyNo", ply);
            List<ReportCustomerInfoVO> insuredList = customerInfoStoreSAO.getInsuredList(param2);
            if (ListUtils.isEmptyList(insuredList)) {
                continue;
            }
            for (ReportCustomerInfoVO insu : insuredList) {
                CustomerPolicyVO vo = new CustomerPolicyVO();
                vo.setCustomerInfo(insu);
                list.add(vo);
            }
        }
        return list;
    }

    @GetMapping(value = "/getPolicyDomainInfoByPolicyNoStore")
    @ApiOperation(value = "根据保单号查询保单详情")
    public ResponseResult<Map<String, Object>> getPolicyDomainInfoByPolicyNoStore(String reportNo, String policyNo, String policyCerNo, String selfCardNo, String clientNo) {
        Map<String, Object> map = ahcsQueryPolicyService.getPolicyDomainInfoByPolicyNo(reportNo, policyNo, policyCerNo, selfCardNo, clientNo);
        return ResponseResult.success(map);
    }

    @GetMapping(value = "/getPolicyCustomerInfoByPolicyNo")
    @ApiOperation(value = "根据保单号查询客户信息")
    public ResponseResult getPolicyCustomerInfoByPolicyNo(PolicyCustomerVo policyCustomerVo, Pager pager) {
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        Map<String, String> param = new HashMap<>();
        param.put("policyNo", policyCustomerVo.getDocumentNo());
        List<ReportCustomerInfoVO> insuredList = customerInfoStoreSAO.getInsuredList(param);
        PageInfo<ReportCustomerInfoVO> pageInfo = new PageInfo<>(insuredList);
        pager.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();
        return ResponseResult.success(insuredList, pager);
    }

    @PostMapping("/getPolicyPlanDuty")
    public ResponseResult<List<OcasPolicyDTO>> getPolicyPlanDuty(@RequestBody OcasPolicyQueryVO policyVO) {
        if (CollectionUtil.isEmpty(policyVO.getPolicyList())) {
            return ResponseResult.success(Collections.emptyList());
        }
        List<OcasPolicyPlanDutyDTO> policyPlanDutyDTOList = null;
        if(StringUtils.isEmptyStr(policyVO.getIdPlyRiskProperty())){
            policyPlanDutyDTOList = ocasMapper.getPolicyPlanDuty(policyVO.getPolicyList(), policyVO.getCertificateNo(), policyVO.getInsuredName());
        }else{
            policyVO.setPolicyNo(policyVO.getPolicyList().get(0).getPolicyNo());
            policyPlanDutyDTOList = ocasMapper.getPolicyPlanDutyList(policyVO);
        }
        if (CollectionUtil.isEmpty(policyPlanDutyDTOList)) {
            return ResponseResult.success(Collections.emptyList());
        }

        List<OcasPolicyDTO> policyList = new ArrayList<>();
        Map<String, List<OcasPolicyPlanDutyDTO>> policyMap = policyPlanDutyDTOList.stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPolicyNo));
        if (riskPropertyService.displayRiskProperty(null, policyVO.getPolicyList().get(0).getPolicyNo())) {
            for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> policyEntry : policyMap.entrySet()) {
                Map<String, List<OcasPolicyPlanDutyDTO>> riskGroupMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getRiskGroupNo));
                List<OcasRiskGroupDTO> riskGroupList = new ArrayList<>();
                for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> riskGroupEntry : riskGroupMap.entrySet()) {
                    OcasPolicyPlanDutyDTO riskGroup = riskGroupEntry.getValue().get(0);
                    Map<String, List<OcasPolicyPlanDutyDTO>> planMap = riskGroupEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPlanCode));
                    List<OcasPlanDTO> planList = new ArrayList<>();
                    for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> planEntry : planMap.entrySet()) {
                        String planCode = planEntry.getKey();
                        List<OcasDutyDTO> dutyList = new ArrayList<>();
                        for (OcasPolicyPlanDutyDTO duty : planEntry.getValue()) {
                            OcasPolicyPlanDutyDTO dutyDTO = ocasMapper.getPlyRiskDutyRelation(duty.getIdPlyDuty(),duty.getIdRiskClass());
                            dutyList.add(new OcasDutyDTO(duty.getDutyCode(), duty.getDutyName(), duty.getDutyAmount(), ObjectUtil.isNotEmpty(dutyDTO) ? dutyDTO.getDutyInsuranceBeginDate() : null, ObjectUtil.isNotEmpty(dutyDTO) ? dutyDTO.getDutyInsuranceEndDate() : null));
                        }
                        planList.add(new OcasPlanDTO(planCode, planMap.get(planCode).get(0).getPlanName(), dutyList));
                    }
                    riskGroupList.add(new OcasRiskGroupDTO(riskGroup.getRiskGroupNo(), riskGroup.getRiskGroupName(),
                            riskGroup.getRiskGroupType(), planList));
                }

                String policyNo = policyEntry.getKey();
                OcasPolicyPlanDutyDTO policy = policyMap.get(policyNo).get(0);
                OcasPolicyDTO ocasPolicy = new OcasPolicyDTO(policyNo, policy, null);
                ocasPolicy.setRiskGroupList(riskGroupList);
                policyList.add(ocasPolicy);
            }
        } else {
            for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> policyEntry : policyMap.entrySet()) {
                Map<String, List<OcasPolicyPlanDutyDTO>> planMap = policyEntry.getValue().stream().collect(Collectors.groupingBy(OcasPolicyPlanDutyDTO::getPlanCode));
                List<OcasPlanDTO> planList = new ArrayList<>();
                for (Map.Entry<String, List<OcasPolicyPlanDutyDTO>> planEntry : planMap.entrySet()) {
                    List<OcasDutyDTO> dutyList = new ArrayList<>();
                    String planCode = planEntry.getKey();
                    for (OcasPolicyPlanDutyDTO duty : planEntry.getValue()) {
                        OcasPolicyPlanDutyDTO dutyDTO = ocasMapper.getPlyRiskDutyRelation(duty.getIdPlyDuty(),duty.getIdRiskClass());
                        dutyList.add(new OcasDutyDTO(duty.getDutyCode(), duty.getDutyName(), duty.getDutyAmount(), ObjectUtil.isNotEmpty(dutyDTO) ? dutyDTO.getDutyInsuranceBeginDate() : null, ObjectUtil.isNotEmpty(dutyDTO) ? dutyDTO.getDutyInsuranceEndDate() : null));
                    }
                    planList.add(new OcasPlanDTO(planCode, planMap.get(planCode).get(0).getPlanName(), dutyList));
                }

                String policyNo = policyEntry.getKey();
                OcasPolicyPlanDutyDTO policy = policyMap.get(policyNo).get(0);
                policyList.add(new OcasPolicyDTO(policyNo, policy, planList));

            }
        }
        return ResponseResult.success(policyList);

    }

    @PostMapping("/getReportInsuredList")
    public  ResponseResult getReportInsuredList(@RequestBody OcasReportQueryVO reportQueryVO) {
        LogUtil.info("报案查询客户信息入参={}", JSON.toJSONString(reportQueryVO));
        //校验参数
        checkReportQueryParam(reportQueryVO);
        // 包含下级机构
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
        PageHelper.startPage(reportQueryVO.getPager().getPageIndex(),reportQueryVO.getPager().getPageRows(),true);
        //查询承保表

        reportQueryVO.setDepartmentCodes(departmentCodes);
        List<OcasInsuredDTO> insuredList = ocasMapper.getReportInsuredList(reportQueryVO);
        PageInfo<OcasInsuredDTO>  pageInfo =new PageInfo<>(insuredList);
        reportQueryVO.getPager().setTotalRows((int)pageInfo.getTotal());
        LogUtil.info("报案查询客户信息出参={}", JSON.toJSONString(insuredList));
        if(ListUtils.isNotEmpty(insuredList)){
            List<RatingQueryVO> ratingQueryList = new ArrayList<>();
            for(OcasInsuredDTO insured : insuredList){
                insured.setCertificateName(CertificateTypeEnum.getName(insured.getCertificateType()));
                RatingQueryVO queryVO = new RatingQueryVO(insured.getInsuredName());
                queryVO.setPhoneNumber(insured.getMobileTelephone());
                if(insured.getCertificateType() != null && CertificateTypeEnum.ID_CARD.getType().equals(insured.getCertificateType())){
                    queryVO.setIdentificationNumber(insured.getCertificateNo());
                }

                ratingQueryList.add(queryVO);
            }
            try {
                List<String> ratingList = customerInfoStoreSAO.queryCustomerRating(ratingQueryList);
                if(ListUtils.isNotEmpty(ratingList)){
                    for(int i=0;i<insuredList.size();i++){
                        insuredList.get(i).setCustomerType(ratingList.get(i));
                    }
                }
            }catch (Exception e){

            }

        }
        LogUtil.info("报案查询客户信息出参={}",JSON.toJSONString(insuredList));
        PageMethod.clearPage();
        return ResponseResult.success(insuredList,reportQueryVO.getPager());
    }

    @PostMapping("/getReportPolicyList")
    public ResponseResult getReportPolicyList(@RequestBody OcasPolicyQueryVO policyQueryVO) {
        LogUtil.info("报案查询保单列表入参={}", JSON.toJSONString(policyQueryVO));
        List<OcasPolicyDTO> policyList = getOcasPolicyDTOS(policyQueryVO,true);
        return ResponseResult.success(policyList,policyQueryVO.getPager());
    }

    @PostMapping("/getReportPolicyListByCopy")
    public ResponseResult getReportPolicyListByCopy(@RequestBody OcasPolicyQueryVO policyQueryVO) {
        LogUtil.info("报案查询保单列表入参={}", JSON.toJSONString(policyQueryVO));
        List<OcasPolicyDTO> policyList = getOcasPolicyDTOS(policyQueryVO,false);
        return ResponseResult.success(policyList,policyQueryVO.getPager());
    }

    private List<OcasPolicyDTO> getOcasPolicyDTOS(OcasPolicyQueryVO policyQueryVO, boolean b) {

        if (b){
            // 包含下级机构
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(WebServletContext.getDepartmentCode());
            policyQueryVO.setDepartmentCodes(departmentCodes);
        }

        Set<String> repeatSet = new HashSet<>();
        //查询承保表
        List<OcasPolicyDTO> policyList = ocasMapper.getReportPolicyList(policyQueryVO);
        List<OcasPolicyDTO> result = new ArrayList<>();
        if(ListUtils.isNotEmpty(policyList)){
            for(OcasPolicyDTO policy : policyList){
                if(!repeatSet.add(policy.getPolicyNo()+policy.getCertificateNo()+policy.getInsuredName())){
                    //重复
                    continue;
                }
                policy.setStatusName(PolicyStatusEnum.getName(policy.getStatus()));
                Date now = DateUtils.now();
                boolean selectedBegin = false;
                boolean selectedEnd = false;
                if(policy.getInsuranceEndDate() != null){
                    //当前日期与保单截止日期比较，当前日期>截止日期说明保单失效
                    if(DateUtils.compareTimeBetweenDate(policy.getInsuranceEndDate(),now)){
                        policy.setActive(ConstValues.NO);
                    }
                    //往后一年
                    Date endDate = DateUtils.addMonth(now,12);
                    //往后一年日期 > 保单截止日期 + 保单截止日期 > 今天
                    selectedEnd = DateUtils.compareTimeBetweenDate(policy.getInsuranceEndDate(),endDate) && DateUtils.compareTimeBetweenDate(now,policy.getInsuranceEndDate());
                }

                if(policy.getInsuranceBeginDate() != null){
                    //往前一年
                    Date beginDate = DateUtils.addMonth(now,-12);
                    //往前一年日期<保单生效日 + 保单生效日 < 今天
                    selectedBegin = DateUtils.compareTimeBetweenDate(beginDate,policy.getInsuranceBeginDate()) && DateUtils.compareTimeBetweenDate(policy.getInsuranceBeginDate(),now);
                }
                // 和产品沟通 改成 或
                if(selectedBegin || selectedEnd){
                    //默认勾选“生效日为当前年月日往前1年，终止日为当前年月日往后1年”内保单
                    policy.setSelected(true);

                }

                result.add(policy);
            }
        }
        PageHelper.startPage(policyQueryVO.getPager().getPageIndex(),policyQueryVO.getPager().getPageRows(),true);
        PageInfo<OcasPolicyDTO> pageInfo =new PageInfo<>(result);
        policyQueryVO.getPager().setTotalRows((int)pageInfo.getTotal());
        LogUtil.info("报案查询保单列表出参={}", JSON.toJSONString(result));
        PageMethod.clearPage();
        return result;
    }

    void checkReportQueryParam(OcasReportQueryVO reportQueryVO) {
        if (null == reportQueryVO) {
            throw new GlobalBusinessException("参数不能为空");
        }
        if (StringUtils.isEmptyStr(reportQueryVO.getCertificateNo()) && StringUtils.isEmptyStr(reportQueryVO.getPolicyNo())) {
            throw new GlobalBusinessException("被保人证件号和保单号至少输入一个");
        }
    }

    void checkpolicyQueryParam(OcasPolicyQueryVO policyQueryVO) {
        if (null == policyQueryVO) {
            throw new GlobalBusinessException("参数不能为空");
        }
        if (StringUtils.isEmptyStr(policyQueryVO.getCertificateNo()) || StringUtils.isEmptyStr(policyQueryVO.getInsuredName())) {
            throw new GlobalBusinessException("被保人证件号和姓名不能为空");
        }
    }

    @GetMapping("/getInsuredBaseInfo")
    public ResponseResult<OcasInsuredDTO> getInsuredBaseInfo(@RequestParam("idPlyRiskPerson") String idPlyRiskPerson){
        if(StringUtils.isEmptyStr(idPlyRiskPerson)){
            throw new GlobalBusinessException("参数不能为空");
        }
        OcasInsuredDTO insuredDTO = ocasMapper.getInsuredBaseInfo(idPlyRiskPerson);
        if(null != insuredDTO){
            //客户类别暂时不适用,第三方系统要求保留该字段
            insuredDTO.setCustomerClass("-");
//            if("200".equals(insuredDTO.getPersonnelAttribute())){
//                insuredDTO.setRemark("虚拟被保人");
//            }
            insuredDTO.setCertificateName(CertificateTypeEnum.getName(insuredDTO.getCertificateType()));
            RatingQueryVO ratingQueryVO =  new RatingQueryVO(insuredDTO.getInsuredName());
            ratingQueryVO.setPhoneNumber(insuredDTO.getMobileTelephone());
            if(insuredDTO.getCertificateType()!= null && CertificateTypeEnum.ID_CARD.getType().equals(insuredDTO.getCertificateType())){
                ratingQueryVO.setIdentificationNumber(insuredDTO.getCertificateNo());
            }
            insuredDTO.setCustomerType(customerInfoStoreSAO.queryCustomerRating(ratingQueryVO));
            if(StringUtils.isNotEmpty(insuredDTO.getPolicyNo())){
                insuredDTO.setDisplayTargets(riskPropertyService.displayRiskProperty(null, insuredDTO.getPolicyNo()));
                ProductInfoDTO prudocutInfo = ocasMapper.getPrudocutInfo(insuredDTO.getPolicyNo());
                if (null != prudocutInfo) {
                    insuredDTO.setTargetType(prudocutInfo.getTargetType());// 标的类型
                }
            }
            insuredDTO.setPolicyTreatmentURL(policyTreatmentURL);
        }

        return ResponseResult.success(insuredDTO);
    }

    @GetMapping("/getPolicyForTest")
    public ResponseResult<JSONObject> getPolicyForTest(@RequestParam("policyNo") String policyNo){
        Map<String,String> param = new HashMap<>();
        param.put("policyNo", policyNo);
        param.put("productKind","0");
        param.put("isElecSubPolicyNo","0");
        param.put("umsDisplayFlag","claim");

        String str = ocasRequest.getPolicyInfoByPolicyNo(param);
        return ResponseResult.success(JSON.parseObject(str));

    }

    @PostMapping("/getPolicyForTest2")
    public ResponseResult<JSONObject> getPolicyForTest2(@RequestBody CopyPolicyQueryVO queryVO){
        String str = ocasRequest.getPolicyInfoByPolicyNoContract(queryVO);
        return ResponseResult.success(JSON.parseObject(str));

    }

    @PostMapping("/getPolicyForTest3")
    public ResponseResult<AhcsPolicyDomainDTO> getPolicyForTest3(@RequestBody CopyPolicyQueryVO queryVO){
        AhcsPolicyDomainDTO ahcsPolicyDomainDTO = customerInfoStoreSAO.getPolicyDomainInfo(queryVO);
        return ResponseResult.success(ahcsPolicyDomainDTO);
    }

    @GetMapping(value = "/getCustomerPolicyInfoPage")
    @ApiOperation(value = "根据用户基本信息查询保单信息列表")
    public ResponseResult getCustomerPolicyInfoPage(CustomerQueryVo customerQueryVo, Pager pager) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getCertificateNo()) && org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getPolicyNo())) {
            throw new GlobalBusinessException("证件号或保单号至少需要输入一个");
        }
        OcasDTO param = new OcasDTO();
        param.setCertificateNo(customerQueryVo.getCertificateNo());
        param.setPolicyNo(customerQueryVo.getPolicyNo());
        //被保险人姓名
        param.setInsuredName(customerQueryVo.getInsuredName());
        String dept = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && org.apache.commons.lang3.StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            dept = departmentUserVO.getDepartmentCode();
        }
        // 包含下级机构
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(dept);
        param.setDepartmentCodes(departmentCodes);
        LogUtil.info("根据用户基本信息查询保单信息入参customerQueryVo{}", JSON.toJSONString(param));
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        List<OcasDTO> insuredList = ocasMapper.getInsuredListPage(param);
        List<CustomerPolicyVO> list = new ArrayList<>();
        for (OcasDTO dto : insuredList) {
            ReportCustomerInfoVO customerInfo = new ReportCustomerInfoVO();
            //前端客户号不为空,暂时固定用0
            customerInfo.setClientNo(dto.getClientNo());
            customerInfo.setSexCode(dto.getSexCode());
            customerInfo.setCertificateNo(dto.getCertificateNo());
            customerInfo.setName(dto.getInsuredName());
            customerInfo.setPolicyNo(dto.getPolicyNo());
            customerInfo.setBirthday(dto.getBirthday());
            if(customerInfo.getBirthday()!=null){
                customerInfo.setBirthday(customerInfo.getBirthday().replace(" 00:00:00",""));
            }
            //被保险人、客户类型、职业类别、证件类型、证件号码
            RatingQueryVO ratingQueryVO = new RatingQueryVO(dto.getInsuredName());
            ratingQueryVO.setPhoneNumber(dto.getMobileTelephone());
            if(dto.getCertificateType()!= null && CertificateTypeEnum.ID_CARD.getType().equals(dto.getCertificateType())){
                ratingQueryVO.setIdentificationNumber(dto.getCertificateNo());
            }
            customerInfo.setClientType(customerInfoStoreSAO.queryCustomerRating(ratingQueryVO));
            customerInfo.setProfessionChnName(dto.getProfessionChnName());
            customerInfo.setCertificateTypeName(CertificateTypeEnum.getName(dto.getCertificateType()));
            customerInfo.setCertificateType(dto.getCertificateType());
            customerInfo.setProfessionClass(dto.getProfessionClass());
            customerInfo.setPersonnelAttribute(dto.getPersonnelAttribute());
            CustomerPolicyVO vo = new CustomerPolicyVO();
            vo.setCustomerInfo(customerInfo);
            list.add(vo);
        }
        PageInfo<OcasDTO> pageInfo = new PageInfo<>(insuredList);
        pager.setTotalRows((int) pageInfo.getTotal());
        PageMethod.clearPage();
        return ResponseResult.success(list, pager);
    }

    /**
     * 根据标的查询险种
     * @param queryVO
     * @return
     */
    @PostMapping("/getRiskGroupPlanList")
    public ResponseResult<List<OcasPolicyDTO>> getRiskGroupPlanList(@RequestBody OcasPolicyQueryVO queryVO){
        return ResponseResult.success(ahcsQueryPolicyService.getRiskGroupPlanList(queryVO));
    }

    @PostMapping("/getRiskGroupList")
    public ResponseResult<List<RiskGroupDTO>> getRiskGroupList(@RequestBody OcasPolicyQueryVO queryVO){
        return ResponseResult.success(ahcsQueryPolicyService.getRiskGroupList(queryVO));
    }

    /**
     * 获取保单信息
     *
     * @param queryVO
     * @return
     */
    @PostMapping("/getPolicyList")
    public ResponseResult<Map<String, Object>> getPolicyList(@RequestBody PolicyQueryVO queryVO) {
        LogUtil.info("查询保单信息列表入参={}", JSON.toJSONString(queryVO));
        String dept = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && org.apache.commons.lang3.StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            dept = departmentUserVO.getDepartmentCode();
        }
        PageDTO<OcasPolicyDTO> pageDTO =  policyService.getPolicyList(dept, queryVO.buildPolicyQueryParam());
        LogUtil.info("查询保单信息列表出参={}",JSON.toJSONString(pageDTO));
        return ResponseResult.success(pageDTO.getList(),pageDTO.getPager());
    }

    /**
     * 获取保单被保人信息
     *
     * @param queryVO
     * @return
     */
    @PostMapping("/getPolicyInsuredList")
    public ResponseResult<Map<String, Object>> getPolicyInsuredList(@RequestBody PolicyQueryVO queryVO) {
        LogUtil.info("查询保单被保人信息列表入参={}", JSON.toJSONString(queryVO));
        String dept = WebServletContext.getDepartmentCode();
        //查询新非车理赔核心机构权限
        DepartmentUserVO departmentUserVO = departmentUserMapper.getDepartmentUser(WebServletContext.getUserId());
        if(departmentUserVO != null && org.apache.commons.lang3.StringUtils.isNotEmpty(departmentUserVO.getDepartmentCode())){
            //不为空，则以新非车理赔核心机构配置表为准，clm_department_user
            dept = departmentUserVO.getDepartmentCode();
        }
        PageDTO<OcasInsuredDTO> pageDTO =  policyService.getPolicyInsuredList(dept, queryVO.buildPolicyInsuredQueryParam());
        LogUtil.info("查询保单被保人信息列表出参={}",JSON.toJSONString(pageDTO));
        return ResponseResult.success(pageDTO.getList(),pageDTO.getPager());
    }

    /**
     * 获取保单标的信息
     *
     * @param queryVO
     * @return
     */
    @PostMapping("/getPoliyRiskSubPropList")
    public ResponseResult<Map<String, Object>> getPoliyRiskSubPropList(@RequestBody PolicyQueryVO queryVO) {
        LogUtil.info("查询保单标的信息列表入参={}", JSON.toJSONString(queryVO));
        PageDTO<PolicyRiskSubPropDTO> pageDTO =  policyService.getPoliyRiskSubPropList(WebServletContext.getDepartmentCode(), queryVO.buildPolicyRiskSubPropQueryParam());
        LogUtil.info("查询保单标的信息列表出参={}",JSON.toJSONString(pageDTO));
        return ResponseResult.success(pageDTO.getList(),pageDTO.getPager());
    }

    /**
     * ES获取保单标的信息
     *
     * @param riskPropQueryVO
     * @return
     */
    @PostMapping("/getEsPlyRiskSubPropList")
    public ResponseResult<Map<String, Object>> getPoliyRiskSubPropList(@RequestBody PolicyRiskQueryVO riskPropQueryVO) throws Exception {
        LogUtil.info("ES查询保单标的信息入参={}", JSON.toJSON(riskPropQueryVO));
        RapeCheckUtil.checkParamEmpty(riskPropQueryVO.getPolicyNo(), "保单号为空");
        //查询标的类型，标的类型为1000时，调出单接口查询
        String riskGroupType = policyService.getRiskGroupType(riskPropQueryVO.getPolicyNo());
        List<PolicyRiskSubPropDTO> plyRiskSubPropList = new ArrayList<>();
        if("1000".equals(riskGroupType)) {
            if (MapUtils.isNotEmpty(riskPropQueryVO.getParamMap())) {
                //小订单号、服务人员姓名二选一必录 2025-07-03版本
                // 使用Optional处理null值，避免NPE
                String insuranceName = Optional.ofNullable(riskPropQueryVO.getParamMap().get("insuranceName"))
                        .map(Object::toString)
                        .orElse("");
                String miniOrderNo = Optional.ofNullable(riskPropQueryVO.getParamMap().get("miniOrderNo"))
                        .map(Object::toString)
                        .orElse("");
                String searchType = Optional.ofNullable(riskPropQueryVO.getParamMap().get("searchType"))
                        .map(Object::toString)
                        .orElse("");

                if(StringUtils.isEmptyStr(insuranceName) && StringUtils.isEmptyStr(miniOrderNo) && !"all".equals(searchType)) {
                    throw new GlobalBusinessException("服务人员姓名或小订单号至少需要输入一个！！");
                }
            }
            List<MiniOrderInfo> miniOrderInfoList = policyService.getPoliyMinOrderList(riskPropQueryVO);
            miniOrderInfoList = miniOrderInfoList.stream()
                    .filter(i -> "U".equals(i.getOrderStatus()))
                    .collect(Collectors.toList());
            for (MiniOrderInfo miniOrderInfo : miniOrderInfoList) {
                PolicyRiskSubPropDTO plyRiskSubProp = new PolicyRiskSubPropDTO();
                plyRiskSubProp.setPolicyNo(miniOrderInfo.getPolicyNo());
                Map<String, Object> riskPropertyMap = BeanUtil.beanToMap(miniOrderInfo);
                if (Objects.nonNull(riskPropertyMap.get("startDate")) && StringUtils.isNotEmpty(riskPropertyMap.get("startDate").toString())) {
                    riskPropertyMap.put("startDate", DateUtil.format(DateUtil.parse(riskPropertyMap.get("startDate").toString()), DatePattern.NORM_DATETIME_FORMAT));
                }
                if (Objects.nonNull(riskPropertyMap.get("endDate")) && StringUtils.isNotEmpty(riskPropertyMap.get("endDate").toString())) {
                    riskPropertyMap.put("endDate", DateUtil.format(DateUtil.parse(riskPropertyMap.get("endDate").toString()), DatePattern.NORM_DATETIME_FORMAT));
                }

                if (Objects.nonNull(riskPropertyMap.get("bookStartTime")) && StringUtils.isNotEmpty(riskPropertyMap.get("bookStartTime").toString())) {
                    riskPropertyMap.put("bookStartTime", DateUtil.format(DateUtil.parse(riskPropertyMap.get("bookStartTime").toString()), DatePattern.NORM_DATETIME_FORMAT));
                }
                plyRiskSubProp.setRiskPropertyMap(riskPropertyMap);
                plyRiskSubPropList.add(plyRiskSubProp);
            }
        } else {
            plyRiskSubPropList = policyService.getEsPlyRiskSubPropList(riskPropQueryVO);
        }
        PageDTO<PolicyRiskSubPropDTO> pageDTO = PageDTO.build(plyRiskSubPropList, riskPropQueryVO.getPager());
        PageMethod.clearPage();
        LogUtil.info("ES查询保单标的信息出参={}",JSON.toJSON(pageDTO));
        return ResponseResult.success(pageDTO.getList(),pageDTO.getPager());
    }

    /**
     * 获取动态标的查询参数
     *
     * @param policyNo
     * @return
     */
    @GetMapping("/getRiskPropertyParamByPolicyNo")
    public ResponseResult<List<RiskPropertyParamDTO>> getRiskPropertyParamByPolicyNo(@RequestParam("policyNo") String policyNo) {
        LogUtil.info("查询保单标的信息列表入参={}", policyNo);
        List<RiskPropertyParamDTO> resp =  policyService.getRiskPropertyParamByPolicyNo(policyNo);
        LogUtil.info("查询保单标的信息列表出参={}",JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

}
