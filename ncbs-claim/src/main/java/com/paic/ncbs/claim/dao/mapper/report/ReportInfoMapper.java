package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryReportAgrsDTO;
import com.paic.ncbs.claim.model.dto.report.QueryAccidentVo;
import com.paic.ncbs.claim.model.dto.report.ReportQueryInfoDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.openapi.ClaimHistoryReportInfoVO;
import com.paic.ncbs.claim.model.vo.openapi.QueryClaimReportRequestVo;
import com.paic.ncbs.claim.model.vo.report.ReportQueryVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ReportInfoMapper extends BaseDao<ReportInfoEntity> {

    ReportInfoEntity getReportInfo(String reportNo);

    List<HistoryCaseDTO> getHistoryCaseByReportNo(@Param("reportNo") String reportNo, @Param("specialCaseType") String specialCaseType);

    List<HistoryCaseDTO> getHistoryCaseByReportNoFilter(@Param("reportNo") String reportNo, @Param("specialCaseType") String specialCaseType);

    List<HistoryCaseDTO> getHistoryCaseByCertificateNo(@Param("certificateNo") String certificateNo,@Param("name") String name);

    List<HistoryCaseDTO> getHistoryCaseBetweenTime(@Param("certificateNo") String certificateNo,@Param("name") String name,
                                                             @Param("certificateType") String certificateType,
                                                             @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<HistoryCaseDTO> getHistoryReportByCertificateNo(@Param("certificateNo") String certificateNo, @Param("specialCaseType") String specialCaseType);

    List<HistoryCaseDTO> getHistoryReportByPolicyNoAndName(@Param("policyNo") String policyNo, @Param("name") String name, @Param("specialCaseType") String specialCaseType);

    List<HistoryCaseDTO> getHistoryReportByBirthdayAndName(@Param("birthday") String birthday, @Param("name") String name);

    List<HistoryCaseDTO> getHistoryReportByElectronicNoNew(String electronicNo);

    List<HistoryCaseDTO> getHistoryReportByTelephoneNo(String telephoneNo);

    List<HistoryCaseDTO> getHistoryReportByDateAndDepartmentCode(HistoryReportAgrsDTO dto);

    List<HistoryCaseDTO> getHistoryReportByReportDateAndName(HistoryReportAgrsDTO dto);

    List<HistoryCaseDTO> getHistoryCaseByReportBatchNo(@Param("reportBatchNo") String reportBatchNo);

    List<String> getHistoryCasebyClientNoBetweenDateNew(@Param("certificateNo") String certificateNo, @Param("name") String name, @Param("reportNo") String reportNo,
                                                        @Param("reportDate") Date reportDate, @Param("accidentDate") Date accidentDate);

    List<HistoryCaseDTO> getHistoryReportFilter(HistoryReportAgrsDTO dto);

    List<HistoryCaseDTO> getHistoryReport(HistoryReportAgrsDTO dto);


    List<HistoryCaseDTO> getHistoryByPolicyNos(@Param("customerInfo") List<ReportCustomerInfoEntity> customerInfo, @Param("policyNo") String policyNo);
    List<HistoryCaseDTO> getHistoryCaseNew(WholeCaseVO wholeCase);


    List<String> selectReports (@Param("name") String name,@Param("certficateNo") String  certficateNo);

    void insertList(@Param("list") List<ReportInfoEntity> list);

    List<HistoryCaseDTO> getHistoryCaseNewByCopy(WholeCaseVO wholeCase);

    /**
     * 查询保单历史案件信息
     * @param dto
     * @return
     */
    List<ClaimHistoryReportInfoVO> getPolicyHistoryClaimInfo(QueryClaimReportRequestVo dto);

    /**
     * 根据报案号查询产品名称
     * @param reportNo
     * @return
     */
    List<String> getProductNames(String reportNo);

    BigDecimal getSumPayFee(String reportNo);

    /**
     * 查询未决案件：添加多证件列表与责任列表查询
     * @param param
     * @return
     */
    List<ReportQueryInfoDTO> queryReportInfoUnsettled(ReportQueryReqDTO param);

    /**
     * 查询有赔付结案列表：添加多证件列表与责任列表查询
     * @param param
     * @return
     */
    List<ReportQueryInfoDTO> queryReportInfoSettled(ReportQueryReqDTO param);

    /**
     * 查询零注，零结案列表：添加多证件列表与责任列表查询
     * @param param
     * @return
     */
    List<ReportQueryInfoDTO> queryReportInfoZeroCannel(ReportQueryReqDTO param);

    List<ReportQueryInfoDTO> listReportQueryInfoUnsettled(ReportQueryReqDTO param);

    List<ReportQueryInfoDTO> listReportQueryInfoSettled(ReportQueryReqDTO param);

    //PROCESS_STATUS
    String getProcessStatus(String reportNo);

    List<ClaimHistoryReportInfoVO> getCusPolicyHistoryClaimInfo(QueryClaimReportRequestVo requestVo);

    /**
     * TPA立案更新风险标识
     * @param entity
     */
    void updateReportRiskFlag(ReportInfoEntity entity);

	List<String> getReportNoList(ReportQueryVO queryVO);

    List<String> getReportInfoCount(QueryAccidentVo vo);
    Integer getIdentifyCaseCount(@Param("list") List<String> list);
    Integer getIdentifyCaseCountYesterday(@Param("list") List<String> list);
    Integer getInRecognitionCount(@Param("list") List<String> list);
    Integer getAllCaseCount(@Param("list") List<String> list);
    BigDecimal getCaseInputDuration(@Param("reportNo") String reportNo);
    String isAIModel(@Param("reportNo") String reportNo);
    Integer getReportCaseCount(@Param("list") List<String> list);
}