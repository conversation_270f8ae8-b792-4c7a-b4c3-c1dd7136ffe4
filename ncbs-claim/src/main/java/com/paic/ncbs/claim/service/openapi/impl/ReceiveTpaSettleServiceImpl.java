package com.paic.ncbs.claim.service.openapi.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsSendAmlRecordEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AmlNacMainMapper;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.ClmsAmlSendRecordMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyPayMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.settle.*;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoVerification;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.ClaimRuleResultDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicySumDTO;
import com.paic.ncbs.claim.model.dto.openapi.*;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.PermissionUserDTO;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.PaymentTransAmlData;
import com.paic.ncbs.claim.sao.AntiMoneyLaunderingSAO;
import com.paic.ncbs.claim.service.antimoneylaundering.AntiMoneyLaunderingService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.*;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.openapi.ReceiveTpaSettleService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.rule.AutoRuleRecordService;
import com.paic.ncbs.claim.service.rule.AutoRuleService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import com.paic.ncbs.claim.service.settle.*;
import com.paic.ncbs.claim.service.user.PermissionService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;

@RefreshScope
@Slf4j
@Service
public class ReceiveTpaSettleServiceImpl implements ReceiveTpaSettleService {
    @Autowired
    private PolicyPayMapper policyPayDao;
    @Autowired
    private PlanPayMapper planPayDao;
    @Autowired
    private DutyPayMapper dutyPayDao;

    @Autowired
    private DutyDetailPayMapper dutyDetailPayDao;
    @Autowired
    private RedisService redisService;

    @Autowired
    private MaxPayService maxPayService;
    @Autowired
    private EstimateService estimateService;
    @Resource(name = "endorsementService")
    private EndorsementService endorsementService;

    @Autowired
    private SettlePaymentService settlePaymentService;

    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    @Autowired
    private PaymentInfoMapper paymentInfoMapper;
    @Autowired
    private ClmsSecondUnderwritingService clmsSecondUnderwritingService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private BatchMapper batchDao;
    @Autowired
    @Lazy
    private SettleValidateService settleValidateService;
    @Autowired
    private RiskPropertyPayService riskPropertyPayService;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;

    @Autowired
    private EndorsementMapper endorsementMapper;

    @Autowired
    private PolicyBatchMapper policyBatchMapper;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private ClmBatchService clmBatchService;

    @Autowired
    private ClmsCommonVerifyService clmsCommonVerifyService;

    @Autowired
    private ClaimCommonPaymentService claimCommonPaymentService;
    @Autowired
    private ClmsReduceAmountService clmsReduceAmountService;
    @Autowired
    private ClmsCommonPolicyService clmsCommonPolicyService;

    @Autowired
    private ClmsQueryPolicyAllInfoService clmsQueryPolicyAllInfoService;

    @Autowired
    private MaxPayService maxPayServiceImpl;
    @Autowired
    private AutoRuleService autoRuleService;
    @Autowired
    private AutoRuleRecordService autoRuleRecordService;
    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    private AntiMoneyLaunderingService antiMoneyLaunderingService;
    @Autowired
    private AntiMoneyLaunderingSAO amlSAO;
    @Autowired
    private AmlNacMainMapper nacMainMapper;
    @Autowired
    private ClmsAmlSendRecordMapper sendRecordMapper;
    @Autowired
    private ReportInfoService reportInfoService;

    @Value("${autoVerify.productPackage}")
    private List<String> autoVerifyPackage;

    @Value("${investment.productCode}")
    private String investmentProductCode;

    /**
     * 处理TPA理算
     * @param dto
     */
    @Override
    @Transactional
    public void dealTpaSettle(ReceiveTpaSettleDTO dto) {
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        changeOperator(reportNo, caseTimes);
        checkInputData(dto);
        List<PolicyPayDTO> copyPolicyPays = clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo, caseTimes);
        //TPA全包理算初始化
        log.info("TPA全包理算报案号={},入参={},报案号查询得到保单信息={}",dto.getReportNo(),JsonUtils.toJsonString(dto),JsonUtils.toJsonString(copyPolicyPays));
        initpolicy(dto,copyPolicyPays);
        //TPA全包理算提交
        submitData(dto,copyPolicyPays);
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        // 微保的案件理算结束后开始自动核赔 异常捕获
        if("TencentHC".equals(WebServletContext.getUser().getUserCode())
                || dto.getIsAutoVerify()
                || (null != reportInfo && ReportSubModeEnum.MSH.getType().equals(reportInfo.getReportSubMode()))
                || autoVerifyPackage.contains(copyPolicyPays.get(0).getProductPackage())
                || investmentProductCode.contains(copyPolicyPays.get(0).getProductCode())) {
            try {
                LogUtil.audit("autoVerify:案件{} {} ,自动核赔开始：", reportNo, caseTimes);
//                List<PolicyInfoDTO> policyInfoListByReportNo = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
                ClaimRuleResultDTO claimRuleResultDTO;
                if(investmentProductCode.contains(copyPolicyPays.get(0).getProductCode())){
                    claimRuleResultDTO = autoRuleService.IndependentVerifyRule(reportNo, caseTimes
                            , copyPolicyPays.get(0).getInsuranceBeginTime()
                            , copyPolicyPays.get(0).getInsuranceEndTime());
                }else {
                    claimRuleResultDTO = autoRuleService.executeVerifySettleRule(reportNo, caseTimes);
                }
                LogUtil.audit("autoVerify:案件{} {} ,自核返回结果：{}", reportNo, caseTimes, claimRuleResultDTO.isAutoPass());
                if(claimRuleResultDTO.isAutoPass()){
                    LogUtil.audit("autoVerify:案件{} {} ,自动核赔业务开始：", reportNo, caseTimes);
                    verifyService.autoVerify(reportNo,caseTimes);
                    autoRuleRecordService.updateRuleResult(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW, ConstValues.YES);
                    LogUtil.audit("autoVerify:案件{} {} ,自动核赔业务结束：");
                }
            } catch (GlobalBusinessException ge){
                // 手动异常不要告警了
                LogUtil.audit("autoVerify:案件{} {} ,自动核赔校验异常：{}", reportNo, caseTimes,ge.getMessage());
                autoRuleRecordService.updateRuleResult(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW,ConstValues.NO);
            } catch (Exception e){
                LogUtil.audit("autoVerify:案件{} {} ,自动核赔异常：{}", reportNo, caseTimes,e.getMessage());
                LogUtil.error("自动核赔异常：", e);
                autoRuleRecordService.updateRuleResult(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW,ConstValues.NO);
            }
        }

    }



    /**
     * 理算初始化
     * return false:表示已经初始化过了 无需初始h
     * return true ：表示 没有初始化过 做了完初始化
     * @param dto
     */
    private boolean initpolicy(ReceiveTpaSettleDTO dto,List<PolicyPayDTO> copyPolicyPays) {
        TaskInfoDTO taskinfo=  taskInfoMapper.checkWorkflow(dto.getReportNo(),dto.getCaseTimes(),BpmConstants.OC_MANUAL_SETTLE,"0");
        if(Objects.isNull(taskinfo)){
            throw new GlobalBusinessException("案件不在理算岗位不能理算！");
        }
        if (this.checkExists(dto.getReportNo(), dto.getCaseTimes())) {
            LogUtil.audit("--已经处理过理算数据,不需要重复处理,报案号:{},赔付次数:{}", dto.getReportNo(), dto.getCaseTimes());
            return  false;
        }
        String lockValue = UuidUtil.getUUID();
        String lockName = String.format(BaseConstant.POLICY_PAY_INIT_LOCK,dto.getReportNo());
        if(!redisService.tryLock(lockName,lockValue)){
            LogUtil.audit("初始化中，请稍侯");
            return  false;
        }
        LogUtil.audit("--初始化赔付信息-从抄单表查询数据,报案号:{},请求参数={},核心保单信息={}", dto.getReportNo(), JsonUtils.toJsonString(dto),JsonUtils.toJsonString(copyPolicyPays));
        if (ListUtils.isEmptyList(copyPolicyPays)) {
            LogUtil.audit("--初始化赔付信息结束,报案号:{},赔付次数:{},保单列表为空，无需初始化自动理算", dto.getReportNo(), dto.getCaseTimes());
            return false;
        }
        LogUtil.audit("--初始化赔付信息-插入理算批次表,报案号：" + dto.getReportNo());
        String idClmBatch = clmBatchService.insertBatch(dto.getReportNo(), dto.getCaseTimes(), SettleConst.SETTLE_STATUS_ON);

        LogUtil.audit("--初始化赔付信息-生成赔付信息uuid,报案号：" + dto.getReportNo());
        SettleHelper.setPolicyPays(copyPolicyPays, idClmBatch);

        LogUtil.audit("--初始化赔付信息-计算剩余赔付额,报案号：" + dto.getReportNo());
        setRollback(copyPolicyPays);
        maxPayService.initPoliciesPayMaxPay(copyPolicyPays, null);

        LogUtil.audit("--初始化赔付信息-设置预估、预赔、费用,报案号：" + dto.getReportNo());
        setPolicyInfo(copyPolicyPays);
        //处理理算金额 理算金额由TPA计算录入 --开始
        setAmount(copyPolicyPays,dto);

        LogUtil.audit("--初始化赔付信息-统计保单理算金额总和,报案号：" + dto.getReportNo());
        SettleHelper.initSettleAmount(copyPolicyPays);
        LogUtil.audit("--初始化赔付信息-插入保单赔付批次明细表,报案号：" + dto.getReportNo());
        policyPayService.insertPolicyBatch(copyPolicyPays, idClmBatch);
        LogUtil.audit("--初始化赔付信息-删除保单赔付信息,报案号：" + dto.getReportNo());
        policyPayDao.deletePolicyPays(dto.getReportNo(), dto.getCaseTimes());

        LogUtil.audit("--初始化赔付信息-插入理算赔付数据,报案号：" + dto.getReportNo());
        policyPayService.insertPayInfo(copyPolicyPays);
        LogUtil.audit("--初始化赔付信息-自动批单,报案号：" + dto.getReportNo());
        endorsementService.autoGenerateEndorsement4TPA(dto.getReportNo(), dto.getCaseTimes(), copyPolicyPays);

        LogUtil.audit("--初始化赔付信息-自动生成支付项（赔付信息）,报案号：" + dto.getReportNo());
        settlePaymentService.autoGeneratePaymentItems(dto.getReportNo(),dto.getCaseTimes(),copyPolicyPays,idClmBatch);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisService.removeLock(lockName,lockValue);
            }
        });
        return true;
    }
    private boolean checkExists(String reportNo, Integer caseTime) {
        return policyPayDao.checkExists(reportNo, caseTime, SettleConst.CLAIM_TYPE_PAY) > 0;
    }

    /**
     * 校验入参
     * @param dto
     */
    private void checkInputData(ReceiveTpaSettleDTO dto) {
        //入参校验
        if(StringUtils.isEmptyStr(dto.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空");
        }
        if(Objects.isNull(dto.getCaseTimes())){
            throw new GlobalBusinessException("赔付次数不能为空");
        }
        if(CollectionUtils.isEmpty(dto.getPolicyList())){
            throw new GlobalBusinessException("保单信息列表不能为空");
        }
        checkPolicyInfo(dto.getPolicyList());
        //领款人信息校验
        checkClientRelation(dto.getReportNo(),dto.getCaseTimes());
        //反洗钱信息校验
        checkClmsAmlInfo(dto);
        //校验当前流程是否有冲突
        bpmService.processCheck(dto.getReportNo(),BpmConstants.OC_MANUAL_SETTLE,BpmConstants.OPERATION_SUBMIT);
    }

    /**
     * 反洗钱信息校验
     * @param dto
     */
    private void checkClmsAmlInfo(ReceiveTpaSettleDTO dto) {
        log.info("checkClmsAmlInfo dto: {}", JSON.toJSONString(dto));
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        BigDecimal payAmountDecimal = dto.getPaymentItemList().stream()
                .filter(p -> "P1".equals(p.getPaymentUsage()))
                .map(TpaPaymentItemDTO::getPaymentAmount)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal decimal = new BigDecimal(10000);
        BigDecimal quotient = new BigDecimal("0");
        //查询支付信息录入表
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setReportNo(reportNo);
        paymentInfoDTO.setCaseTimes(caseTimes);
        List<PaymentInfoDTO> list = paymentInfoMapper.getPaymentInfo(paymentInfoDTO);
        if(!list.isEmpty()){
            quotient = payAmountDecimal.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
        }

        ClmsAntiMoneyLaunderingInfoDto queryDTO = new ClmsAntiMoneyLaunderingInfoDto();
        queryDTO.setReportNo(reportNo);
        queryDTO.setCaseTimes(caseTimes);
        for(PaymentInfoDTO paymentInfo: list){
            //bankAccountAttribute="1"--个人账户时才会校验
            if("1".equals(paymentInfo.getBankAccountAttribute())){
                queryDTO.setCustomerNo(paymentInfo.getCustomerNo());
                ClmsAntiMoneyLaunderingInfoDto amlPersonalInfo = antiMoneyLaunderingService.getClmsAntiMoneyLaunderingInfo(queryDTO);
                if(amlPersonalInfo == null){
                    if(decimal.compareTo(quotient) <= 0){
                        String errMsg = "请完善领款人:" + paymentInfo.getClientName() + "反洗钱相关信息!";
                        throw new GlobalBusinessException(ErrorCode.Settle.CHECK_ANTI_MONEY_LAUNDERING, errMsg);
                    }
                } else {
                    //校验反洗钱信息是否完整
                    checkAntiMoneyLaundering(paymentInfo,queryDTO);
                    //送反洗钱系统
                    clmsSendAml(paymentInfo,payAmountDecimal);
                }
            }
        }
    }

    private void checkAntiMoneyLaundering(PaymentInfoDTO paymentInfoDTO,ClmsAntiMoneyLaunderingInfoDto queryDTO){
        StringBuilder builder = new StringBuilder();
        log.info("tpa回传反洗钱信息完整校验 queryDTO: {}", JSON.toJSONString(queryDTO));
        ClmsAntiMoneyLaunderingInfoDto amlPersonalInfo = antiMoneyLaunderingService.getClmsAntiMoneyLaunderingInfo(queryDTO);
        if (Objects.nonNull(amlPersonalInfo)) {
            // 校验数据是否完整
            boolean checkFail = antiMoneyLaunderingInfoIntegrityCheck(
                    amlPersonalInfo, paymentInfoDTO.getBankAccountAttribute());
            if (checkFail) {
                builder.append(paymentInfoDTO.getClientName()).append("、");
            }
        } else {
            // 未录入过反洗钱信息，需要判断反洗钱信息是否完整，如不完整，则依然提示需要完善信息
            builder.append(paymentInfoDTO.getClientName()).append("、");
        }
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(builder)){
            builder.setLength(builder.length() - 1);
            String errMsg = "请完善领款人:" + builder.toString() + "反洗钱相关信息!";
            log.info("tpa回传反洗钱信息完整校验 errMsg: {}", errMsg);
            throw new GlobalBusinessException(ErrorCode.Settle.CHECK_ANTI_MONEY_LAUNDERING, errMsg);
        }
    }

    /**
     * 校验个人反洗钱数据
     * @param amlInfo 反洗钱信息
     * @param bankAccountAttribute 帐号类型:个人帐号=1
     * @return 校验结果
     */
    private boolean antiMoneyLaunderingInfoIntegrityCheck(ClmsAntiMoneyLaunderingInfoDto amlInfo, String bankAccountAttribute) {
        // 校验个人反洗钱信息
        if (BaseConstant.STRING_1.equals(bankAccountAttribute)) {
            ClmsAntiMoneyLaunderingInfoVerification personalCheckDTO = new ClmsAntiMoneyLaunderingInfoVerification();
            BeanUtils.copyProperties(amlInfo, personalCheckDTO);
            log.info("antiMoneyLaunderingInfoIntegrityCheck personalCheckDTO: {}", JSON.toJSONString(personalCheckDTO));
            return !isAllFieldNotNull(personalCheckDTO);
        }

        return false;
    }

    /**
     * 校验对象属性是否都不为空，存在为空返回false，否则返回true
     * @param obj 入参对象
     * @return 校验结果
     */
    private static boolean isAllFieldNotNull(Object obj) {
        Class stuCla = obj.getClass();
        Field[] fs = stuCla.getDeclaredFields();
        for(Field f : fs){
            ReflectionUtils.makeAccessible(f);
            Object val;
            try {
                val = f.get(obj);
                if(val == null || "".equals(val)){
                    return false;
                }
            } catch (IllegalAccessException e) {
                log.info("校验异常");
            }

        }
        return true;
    }

    /**
     * 送反洗钱系统
     * @param paymentInfo
     */
    private void clmsSendAml(PaymentInfoDTO paymentInfo,BigDecimal payAmountDecimal){
        String clientName = paymentInfo.getClientName();
        String clientCertificateNo = paymentInfo.getClientCertificateNo();
        String clientCertificateType = paymentInfo.getClientCertificateType();
        String reportNo = paymentInfo.getReportNo();
        Integer caseTimes = paymentInfo.getCaseTimes();
        String policyNo = "";
        String caseNo = "";
        String userId = "SYSTEM";
        List<PolicyPayDTO> copyPolicyPays = clmsQueryPolicyAllInfoService.getPolicyAllInfo(reportNo, caseTimes);
        if(!copyPolicyPays.isEmpty()){
            policyNo = copyPolicyPays.get(0).getPolicyNo();
            caseNo = copyPolicyPays.get(0).getCaseNo();
        }
        List<PaymentTransAmlData> amlDataList = Lists.newArrayList();
        String nationCode = CommonConstant.CHINA_COUNTRY_CODE; // 国籍目前只有中国
        // 洗钱黑名单
        int moneyLaunderingBlackListCount = nacMainMapper.getMoneyLaunderingBlackListCount(clientName, clientCertificateNo);
        if (moneyLaunderingBlackListCount > 0) {
            ClmsSendAmlRecordEntity query = ClmsSendAmlRecordEntity.builder()
                    .reportNo(reportNo)
                    .caseTimes(caseTimes)
                    .amlCode(AmlCodeEnum.AML_CODE_00001.getType())
                    .clientName(clientName)
                    .clientCertificateNo(clientCertificateNo)
                    .clientCertificateType(clientCertificateType)
                    .build();
            ClmsSendAmlRecordEntity one = sendRecordMapper.selectOne(query);
            if (Objects.isNull(one)) {
                PaymentTransAmlData amlData = convertToAmlData(paymentInfo, AmlCodeEnum.AML_CODE_00001.getType(), policyNo,
                        caseNo, nationCode, payAmountDecimal, clientName, clientCertificateNo, clientCertificateType);
                amlDataList.add(amlData);
            } else {
                if (org.apache.commons.lang3.StringUtils.isBlank(one.getAuditFlag())) {
                    throw new GlobalBusinessException("黑名单待审批！");
                }

                if (ConstValues.YES.equals(one.getAuditFlag())) {
                    throw new GlobalBusinessException("当前案件涉及可疑交易！");
                }
            }
        }

        // 高风险国家地区黑名单
        int highRiskCountryBlackListCount = nacMainMapper.getHighRiskCountryBlackListCount(nationCode);
        if (highRiskCountryBlackListCount > 0) {
            ClmsSendAmlRecordEntity query = ClmsSendAmlRecordEntity.builder()
                    .reportNo(reportNo)
                    .caseTimes(caseTimes)
                    .amlCode(AmlCodeEnum.AML_CODE_00002.getType())
                    .clientName(clientName)
                    .clientCertificateNo(clientCertificateNo)
                    .clientCertificateType(clientCertificateType)
                    .nationCode(nationCode)
                    .build();
            ClmsSendAmlRecordEntity one = sendRecordMapper.selectOne(query);
            if (Objects.isNull(one)) {
                PaymentTransAmlData amlData = convertToAmlData(paymentInfo, AmlCodeEnum.AML_CODE_00002.getType(), policyNo,
                        caseNo, nationCode, payAmountDecimal, clientName, clientCertificateNo, clientCertificateType);
                amlDataList.add(amlData);
            }
        }

        // 领款人类型为其他
        if (ClientTypePersonalEnum.CLIENT_TYPE_09.getCode().equals(paymentInfo.getClientType())) {
            ClmsSendAmlRecordEntity query = ClmsSendAmlRecordEntity.builder()
                    .reportNo(reportNo)
                    .caseTimes(caseTimes)
                    .amlCode(AmlCodeEnum.AML_CODE_00008.getType())
                    .clientName(clientName)
                    .clientCertificateNo(clientCertificateNo)
                    .clientCertificateType(clientCertificateType)
                    .build();
            ClmsSendAmlRecordEntity one = sendRecordMapper.selectOne(query);
            if (Objects.isNull(one)) {
                PaymentTransAmlData amlData = convertToAmlData(paymentInfo, AmlCodeEnum.AML_CODE_00008.getType(), policyNo,
                        caseNo, nationCode, payAmountDecimal, clientName, clientCertificateNo, clientCertificateType);
                amlDataList.add(amlData);
            }
        }
        // 送反洗钱系统可疑数据并保存送反洗钱记录表
        if (CollectionUtils.isNotEmpty(amlDataList)) {
            // 送反洗钱系统
            amlSAO.reportAmlTarget(amlDataList);
            // 保存送反洗钱记录表
            List<ClmsSendAmlRecordEntity> entityList = Lists.newArrayList();
            boolean resultFail = false; // 调用反洗钱系统失败标记
            boolean flag = false; // 存在反洗钱名单标记
            for (PaymentTransAmlData amlData : amlDataList) {
                if (org.apache.commons.lang3.StringUtils.isBlank(amlData.getSerialNo()) || NcbsConstant.RESULT_FAIL.equals(amlData.getCode())) {
                    resultFail = true;
                    continue;
                }

                if (AmlCodeEnum.AML_CODE_00001.getType().equals(amlData.getAntimoneyCd())) {
                    flag = true;
                }

                ClmsSendAmlRecordEntity entity = new ClmsSendAmlRecordEntity();
                entity.setReportNo(reportNo);
                entity.setCaseTimes(caseTimes);
                entity.setSerialNo(amlData.getSerialNo());
                entity.setAmlCode(amlData.getAntimoneyCd());
                entity.setClientNo(amlData.getClientNo());
                entity.setClientName(amlData.getClientName());
                entity.setClientCertificateNo(amlData.getClientCertificateNo());
                entity.setClientCertificateType(amlData.getClientCertificateType());
                entity.setNationCode(amlData.getNationCode());
                entity.setCreatedBy(userId);
                entity.setUpdatedBy(userId);
                entityList.add(entity);
            }

            if (CollectionUtils.isNotEmpty(entityList)) {
                sendRecordMapper.batchInsert(entityList);
            }

            if (resultFail) {
                throw new GlobalBusinessException("请求反洗钱系统失败！");
            }
            if (flag) {
                log.info("tpa理算洗钱黑名单上报成功，reportNo:{}",reportNo);
            }
        }

    }
    /**
     * 转换反洗接口对象
     *
     * @param paymentInfo 支付信息
     * @param antimoneyCd 反洗钱监测标准代码
     * @param policyNo 保单号
     * @param caseNo 赔案号
     * @param nationCode 国籍
     * @param paymentAmount 支付金额
     * @param clientName 名称
     * @param clientCertificateNo 证件号
     * @param clientCertificateType 证件类型
     * @return 反洗接口对象
     */
    private PaymentTransAmlData convertToAmlData(PaymentInfoDTO paymentInfo, String antimoneyCd, String policyNo,
                                                 String caseNo, String nationCode, BigDecimal paymentAmount,
                                                 String clientName, String clientCertificateNo, String clientCertificateType) {
        PaymentTransAmlData amlData = new PaymentTransAmlData();
        amlData.setReportNo(paymentInfo.getReportNo());
        amlData.setCaseTimes(paymentInfo.getCaseTimes());
        amlData.setAntimoneyCd(antimoneyCd);
        amlData.setPolicyNo(policyNo);
        amlData.setCaseNo(caseNo);
        amlData.setNationCode(nationCode);
        amlData.setPaymentAmount(paymentAmount);
        amlData.setClientNo(paymentInfo.getCustomerNo());
        amlData.setClientName(clientName);
        amlData.setClientCertificateNo(clientCertificateNo);
        amlData.setClientCertificateType(clientCertificateType);
        amlData.setClientMobile(paymentInfo.getClientMobile());
        amlData.setClientBankCode(paymentInfo.getClientBankCode());
        amlData.setClientBankName(paymentInfo.getClientBankName());
        amlData.setClientBankAccount(paymentInfo.getClientBankAccount());
        amlData.setBankAccountAttribute(paymentInfo.getBankAccountAttribute());
        return amlData;
    }
    /**
     * 保单信息校验
     * @param policyList
     */
    private void checkPolicyInfo(List<TpaPolicyDTO> policyList) {
        for (TpaPolicyDTO dto : policyList) {
            if(StringUtils.isEmptyStr(dto.getPolicyNo())){
                throw new GlobalBusinessException("保单号不能为空");
            }
            if(CollectionUtils.isEmpty(dto.getPlanPayList())){
                throw new GlobalBusinessException("保单号"+dto.getPolicyNo()+"的险种信息不能为空");
            }
            checkPlanInfo(dto.getPlanPayList(),dto.getPolicyNo());
        }
    }

    /**
     * 险种信息校验
     *
     * @param planPayList
     * @param policyNo
     */
    private void checkPlanInfo(List<TpaPlanDTO> planPayList, String policyNo) {
        for (TpaPlanDTO plandto: planPayList) {
            if(StringUtils.isEmptyStr(plandto.getPlanCode())){
                throw new GlobalBusinessException("保单号"+policyNo+"下险种编码不能为空");
            }
            if(CollectionUtils.isEmpty(plandto.getDutyPayList())){
                throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+plandto.getPlanCode()+"下责任信息不能为空");
            }
            checkDutyInfo(plandto.getDutyPayList(),policyNo,plandto.getPlanCode());
        }

    }

    /**
     * 责任信息校验
     *
     * @param dutyPayList
     * @param policyNo
     * @param planCode
     */
    private void checkDutyInfo(List<TpaDutyDTO> dutyPayList, String policyNo, String planCode) {
        for (TpaDutyDTO dutyDto : dutyPayList) {
            if(StringUtils.isEmptyStr(dutyDto.getDutyCode())){
                throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+planCode+"下责任编码不能为空");
            }
            if(StringUtils.isEmptyStr(dutyDto.getSettleReason())){
                throw new GlobalBusinessException("保单号"+policyNo+"下险种编码"+planCode+"下责任编码"+dutyDto.getDutyCode()+"理算依据不能为空");
            }
            if(CollectionUtils.isNotEmpty(dutyDto.getDutyBillLimitInfoList())){
                for (DutyBillLimitInfo billLimitDto: dutyDto.getDutyBillLimitInfoList()) {
                    if(StringUtils.isEmptyStr(billLimitDto.getBillDate())){
                        throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+planCode+"下责任编码"+dutyDto.getDutyCode()+"下日限额账单日期不能为空");
                    }
                    if(StringUtils.isEmptyStr(billLimitDto.getSettleClaimAmount())){
                        throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+planCode+"下责任编码"+dutyDto.getDutyCode()+"下日限额不能为空");
                    }
                }
            }
            if(CollectionUtils.isEmpty(dutyDto.getDutyDetailPayList())){
                throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+planCode+"下责任编码"+dutyDto.getDutyCode()+"下责任明细不能为空");
            }
            checkDutyDetail(dutyDto, policyNo, planCode);
        }

    }

    /**
     * 责任明细校验
     * @param dutyDto
     * @param policyNo
     * @param planCode
     */
    private void checkDutyDetail(TpaDutyDTO dutyDto, String policyNo, String planCode) {
        for (TpaDutyDetailDTO detailDTO: dutyDto.getDutyDetailPayList()) {
            if(StringUtils.isEmptyStr(detailDTO.getDutyDetailCode())){
                throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+planCode+"下责任编码"+dutyDto.getDutyCode()+"下责任明细编码不能为空");
            }
            if(StringUtils.isEmptyStr(detailDTO.getAutoSettleAmount())){
                throw new GlobalBusinessException("保单号"+policyNo+"险种编码"+planCode+"下责任编码"+dutyDto.getDutyCode()+"下责任明细编码"+detailDTO.getDutyDetailCode()+"金额不能为空");
            }
        }
    }

    private void setRollback(List<PolicyPayDTO> copyPolicyPays){
        if(ListUtils.isEmptyList(copyPolicyPays)){
            return;
        }
        copyPolicyPays.get(0).setRollback(true);
    }
    /**
     * 设置保单总预估金额、总预赔、初始化费用金额
     */
    private void setPolicyInfo(List<PolicyPayDTO> policyPays) {
        //设置保单总预估金额
        setEstimatePay(policyPays);
        //设置保单总预赔
        setPrePayInfo(policyPays);
        //初始化费用（都为0）
        policyPayService.setPolicyFeePay(policyPays);
    }
    private void setEstimatePay(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        PolicyPayDTO policy = policyPays.get(0);
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        List<EstimatePolicySumDTO> estimatePolicys = estimateService.getEstimatePolicySum(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(estimatePolicys)) {
            return;
        }
        for (PolicyPayDTO policyPay : policyPays) {
            for (EstimatePolicySumDTO estimatePolicy : estimatePolicys) {
                if (policyPay.getPolicyNo().equals(estimatePolicy.getPolicyNo()) && policyPay.getCaseNo().equals(estimatePolicy.getCaseNo())) {
                    policyPay.setPolicySumEstimate(estimatePolicy.getPolicySumEstimate());
                }
            }
        }
    }
    private void setPrePayInfo(List<PolicyPayDTO> policyPays) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        PolicyPayDTO policy = policyPays.get(0);
        String reportNo = policy.getReportNo();
        Integer caseTimes = policy.getCaseTimes();
        List<PolicyPayDTO> oldPolicyPays = policyPayDao.getPrePolicyPays(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(oldPolicyPays)) {
            return;
        }
        for (PolicyPayDTO policyPay : policyPays) {
            for (PolicyPayDTO oldPolicyPay : oldPolicyPays) {
                if (policyPay.getPolicyNo().equals(oldPolicyPay.getPolicyNo()) && policyPay.getCaseNo().equals(oldPolicyPay.getCaseNo())) {
                    policyPay.setPolicyPrePay(oldPolicyPay.getPolicyPrePay());
                    policyPay.setPolicyPreFee(oldPolicyPay.getPolicyPreFee());
                }
            }
        }
    }

    /**
     * 处理金额
     * @param copyPolicyPays
     * @param dto
     */
    private void setAmount(List<PolicyPayDTO> copyPolicyPays, ReceiveTpaSettleDTO dto) {
        List<TpaPolicyDTO> tpaPolicyList=dto.getPolicyList();
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        for (PolicyPayDTO policyDto : copyPolicyPays) {
            BigDecimal policySum=BigDecimal.ZERO;
            List<TpaPolicyDTO> tpaPolicyDTOList =  tpaPolicyList.stream().filter(tpaPolicyDTO -> Objects.equals(policyDto.getPolicyNo(),tpaPolicyDTO.getPolicyNo())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tpaPolicyDTOList)){
               continue;
            }
            List<PlanPayDTO> planPayDTOList = policyDto.getPlanPayArr();
            List<TpaPlanDTO> tpaPlanPayList = tpaPolicyDTOList.get(0).getPlanPayList();
            clmsDutyDetailBillSettleMapper.deleteByReportNo(reportNo, caseTimes);
            for (PlanPayDTO planDto :planPayDTOList) {
                List<TpaPlanDTO>  tpaPlanList =  tpaPlanPayList.stream().filter(tpaPlanDTO ->Objects.equals(planDto.getPlanCode(),tpaPlanDTO.getPlanCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(tpaPlanList)){
                  continue;
                }
                List<DutyPayDTO> dutyPayDTOS =planDto.getDutyPayArr();
                List<TpaDutyDTO>  tpaDutyDTOS = tpaPlanList.get(0).getDutyPayList();
                for (DutyPayDTO dutyDto : dutyPayDTOS) {
                    List<TpaDutyDTO>  tpaDutyList =  tpaDutyDTOS.stream().filter(tpaDutyDTO->Objects.equals(dutyDto.getDutyCode(),tpaDutyDTO.getDutyCode())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(tpaDutyList)){
                       continue;
                    }
                    List<DutyDetailPayDTO>  detailPayDTOS = dutyDto.getDutyDetailPayArr();
                    List<TpaDutyDetailDTO>  tpaDutyDetailDTOList=tpaDutyList.get(0).getDutyDetailPayList();
                    for (DutyDetailPayDTO detail : detailPayDTOS) {
                        List<TpaDutyDetailDTO>  tpaDetailList =  tpaDutyDetailDTOList.stream().filter(tpaDutyDetailDTO -> Objects.equals(detail.getDutyDetailCode(),tpaDutyDetailDTO.getDutyDetailCode())).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(tpaDetailList)){
                           continue;
                        }
                        if(Objects.isNull(tpaDetailList.get(0).getAutoSettleAmount())){
                            continue;
                        }
                        detail.setAutoSettleAmount(BigDecimalUtils.getBigDecimal(tpaDetailList.get(0).getAutoSettleAmount()));
                        policySum = policySum.add(detail.getAutoSettleAmount());

                        //责任明细赔付分摊至发票维度
                        List<ClmsDutyDetailBillSettleDTO> detailBillSettleList = tpaDetailList.get(0).getDetailBillSettleList();
                        if(CollectionUtils.isNotEmpty(detailBillSettleList)) {
                            detailBillSettleList.stream().forEach(clmsDutyDetailBillSettleDTO -> {
                                clmsDutyDetailBillSettleDTO.setCaseTimes(caseTimes);
                                clmsDutyDetailBillSettleDTO.setId(UuidUtil.getUUID());
                                clmsDutyDetailBillSettleDTO.setApprovalStatus("0");
                                clmsDutyDetailBillSettleDTO.setIsDeleted("0");
                                if(Objects.isNull(clmsDutyDetailBillSettleDTO.getRemitAmount())){
                                    clmsDutyDetailBillSettleDTO.setRemitAmount(BigDecimal.ZERO);
                                }
                                if(Objects.isNull(clmsDutyDetailBillSettleDTO.getReasonableAmount())){
                                    clmsDutyDetailBillSettleDTO.setReasonableAmount(BigDecimal.ZERO);
                                }
                                clmsDutyDetailBillSettleDTO.setCreatedBy(com.paic.ncbs.claim.common.util.StringUtils.isEmptyStr(WebServletContext.getUserId()) ? "system" : WebServletContext.getUserId());
                                clmsDutyDetailBillSettleDTO.setUpdatedBy(com.paic.ncbs.claim.common.util.StringUtils.isEmptyStr(WebServletContext.getUserId()) ? "system" : WebServletContext.getUserId());
                            });
                            clmsDutyDetailBillSettleMapper.batchSaveData(detailBillSettleList);
                        }
                    }
                    dutyDto.setSettleReason(tpaDutyList.get(0).getSettleReason());
                    //处理日限额记录
                    if(CollectionUtils.isNotEmpty(tpaDutyList.get(0).getDutyBillLimitInfoList())){
                        saveDutyBillLimitInfo(policyDto,dto,planDto,dutyDto,tpaDutyList.get(0));
                    }
                }
            }
            policyDto.setPolicySumPay(policySum);
        }
    }

    /**
     * 保存日限额记录
     * @param policyDto
     * @param dto
     * @param planDto
     * @param dutyDto
     * @param tpaDutyDTO
     */
    private void saveDutyBillLimitInfo(PolicyPayDTO policyDto, ReceiveTpaSettleDTO dto, PlanPayDTO planDto, DutyPayDTO dutyDto, TpaDutyDTO tpaDutyDTO) {
        List<DutyBillLimitInfo>  dutyBillLimitInfoList = tpaDutyDTO.getDutyBillLimitInfoList();
        for (DutyBillLimitInfo dutyBill : dutyBillLimitInfoList) {
            DutyBillLimitInfoDTO dutyBillLimitInfoDTO = new DutyBillLimitInfoDTO();
            dutyBillLimitInfoDTO.setIdClmsDutyBillLimit(UuidUtil.getUUID());
            dutyBillLimitInfoDTO.setPolicyNo(policyDto.getPolicyNo());
            dutyBillLimitInfoDTO.setReportNo(dto.getReportNo());
            dutyBillLimitInfoDTO.setBillDate( DateUtils.formatStringToDate(dutyBill.getBillDate(),DateUtils.SIMPLE_DATE_STR));
            dutyBillLimitInfoDTO.setBillAmount(BigDecimalUtils.getBigDecimal(dutyBill.getSettleClaimAmount()));
            dutyBillLimitInfoDTO.setSettleClaimAmount(BigDecimalUtils.getBigDecimal(dutyBill.getSettleClaimAmount()));
            dutyBillLimitInfoDTO.setCaseTimes(dto.getCaseTimes());
            dutyBillLimitInfoDTO.setDutyCode(dutyDto.getDutyCode());
            dutyBillLimitInfoDTO.setPlanCode(planDto.getPlanCode());
            //默认为0 核赔完结案更新为1
            dutyBillLimitInfoDTO.setApprovalStatus("0");
            dutyBillLimitInfoDTO.setIsDeleted("0");
            dutyBillLimitInfoDTO.setCreatedBy("tpa");
            dutyBillLimitInfoDTO.setUpdatedBy("system");
            dutyBillLimitInfoDTO.setCreatedDate(new Date());
            dutyBillLimitInfoDTO.setUpdatedDate(new Date());
            dutyBillLimitInfoDTO.setLimitType(StringUtils.isNotEmpty(dutyBill.getLimitType()) ? dutyBill.getLimitType() :"2");
            dutyBillLimitInfoMapper.addDutyBillLimit(dutyBillLimitInfoDTO);
        }
        log.info("限额信息报案号={},保存数据={}",dto.getReportNo(),JsonUtils.toJsonString(dutyBillLimitInfoList));

    }

    /**
     * 检查与被保险人关系字段
     * @param reportNo
     * @param caseTimes
     */
    private void checkClientRelation(String reportNo, int caseTimes) {
        //查询支付信息录入表
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setReportNo(reportNo);
        paymentInfoDTO.setCaseTimes(caseTimes);
        List<PaymentInfoDTO> list = paymentInfoMapper.getPaymentInfo(paymentInfoDTO);
        //循环查询出的数据，判断当账号类型为个人时，与被保险人字段是否为空
        if(CollectionUtils.isEmpty(list)) {
            throw new GlobalBusinessException("领款人信息不能为空");
        }
        for (PaymentInfoDTO dto : list) {
            if(NcbsConstant.PERSON_ALACCOUNT.equals(dto.getBankAccountAttribute()) && org.apache.commons.lang3.StringUtils.isBlank(dto.getClientRelation())) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_CLIENT_RELATION, "请完善支付信息！");
            }
        }
    }
    /**
     * TPA全包理算提交
     *
     * @param dto
     * @param policyPays
     */
    private void submitData(ReceiveTpaSettleDTO dto, List<PolicyPayDTO> policyPays) {
        TaskInfoDTO taskinfo=  taskInfoMapper.checkWorkflow(dto.getReportNo(),dto.getCaseTimes(),BpmConstants.OC_MANUAL_SETTLE,"0");
        log.info("TPA全包理算提交报案号={}",dto.getReportNo());
        List<PaymentItemComData> paymentItems= paymentItemMapper.getTPAPaymentItemDTO(dto.getReportNo(), dto.getCaseTimes());
        EndorsementDTO endorsement = endorsementMapper.selectByReportNoAndCaseTime(dto.getReportNo(), dto.getCaseTimes());
        log.info("TPA全包理算剩余理赔额校验开始案号={}",dto.getReportNo());
        //剩余理赔额校验
        settleValidateService.checkSettle(policyPays, paymentItems, dto.getReportNo(), dto.getCaseTimes());
        //更新保单、险种、责任、责任明细信息
        clmsCommonPolicyService.updatePolicyPays(policyPays);
        log.info("TPA全包理算更新保单、险种、责任、责任明细信息 完成案号={}",dto.getReportNo());
        //更新批次表、保单赔付批次明细表
        updateSettleBatch(dto, policyPays, true,taskinfo);
        log.info("TPA全包理算更新批次表、保单赔付批次明细表报案号={}",dto.getReportNo());
        //合并批单
        mergeEndorsementInfo(dto,endorsement);
        log.info("TPA全包理算合并批单报案号={}",dto.getReportNo());
        //更新支付项
        claimCommonPaymentService.updatePaymentItems(paymentItems,policyPays,dto.getReportNo(), dto.getCaseTimes());
        log.info("TPA全包理算更新支付项报案号={}",dto.getReportNo());
        //操作记录
        operationRecordService.insertOperationRecordByLabour(dto.getReportNo(), BpmConstants.OC_MANUAL_SETTLE, "提交", null);
        //完成当前任务
        bpmService.completeTask_oc(dto.getReportNo(), dto.getCaseTimes(), BpmConstants.OC_MANUAL_SETTLE);
        //开启核赔任务
        startSettleReview(dto.getReportNo(), dto.getCaseTimes());
        //流程状态
        caseProcessService.updateCaseProcess(dto.getReportNo(), dto.getCaseTimes(), CaseProcessStatus.WAIT_VERIFICATION.getCode());
        //自动写核赔记录
        clmsCommonVerifyService.insertVerify(dto.getReportNo(), dto.getCaseTimes(),taskinfo.getAssigner());
        //案件状态更新为已理算
        this.policyPayService.updateCaseStatus(dto.getReportNo(), dto.getCaseTimes(), WholeCaseStatusEnum.WHOLE_CASE_STATUS_TWO.getCode());
        log.info("TPA全包理算完成案号={}",dto.getReportNo());
    }
    private void checkSecondUWIsFinish(String reportNo, Integer caseTimes) {

        List<ClmsSecondUnderwritingEntity> clmsSecondUnderwritingEntities = clmsSecondUnderwritingService.queryByReportNo(reportNo, caseTimes);
        if (!CollectionUtils.isEmpty(clmsSecondUnderwritingEntities)){
            for (ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity : clmsSecondUnderwritingEntities) {
                if ("02".equals(clmsSecondUnderwritingEntity.getLettersCancelStatus()) && !"26".equals(clmsSecondUnderwritingEntity.getConclusion())){
                    // 前一次函件未回销不可重复发起
                    throw new GlobalBusinessException("该案件有二核任务函件未回销，不可提交理算");
                }
                if ("01".equals(clmsSecondUnderwritingEntity.getUnderwritingStatus())){
                    // 前一次任务未完成不可重复发起
                    throw new GlobalBusinessException("该案件有二核任务未核保通过，不可提交理算");
                }
            }
        }
    }
    private void startSettleReview(String reportNo,Integer caseTimes){
        /*  delete zjtang 刪除旧流程校验逻辑
        String bpmKey = taskInfoMapper.getPendingTaskCount(reportNo, caseTimes);
        if (com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(bpmKey)){
            throw new GlobalBusinessException("该案件还有其它待处理的任务！");
        }
         */
        TaskInfoDTO tdto= taskInfoMapper.getTaskAssignerName(reportNo,caseTimes,BpmConstants.OC_MANUAL_SETTLE);
        String departmentCode = policyInfoMapper.getPolicyDeptByReportNo(reportNo);

        String userId = tdto.getAssigner();
        TaskInfoDTO startTask = new TaskInfoDTO();
        startTask.setIdAhcsTaskInfo(UuidUtil.getUUID());
        startTask.setTaskId(UuidUtil.getUUID());
        startTask.setReportNo(reportNo);
        startTask.setCaseTimes(caseTimes);
        startTask.setTaskDefinitionBpmKey(BpmConstants.OC_SETTLE_REVIEW);
        startTask.setAssigneeTime(new Date());
        startTask.setStatus(BpmConstants.TASK_STATUS_PENDING);
        startTask.setCreatedBy(userId);
        startTask.setUpdatedBy(userId);
        startTask.setApplyer(userId);
        startTask.setApplyerName(tdto.getAssigneeName());
        //理算金额+理算费用
        BigDecimal totalAmount = getSumPayFee(reportNo,caseTimes);
        LogUtil.audit("案件总金额={}",totalAmount);
        Integer taskGrade = permissionService.getPermissionGrade(Constants.PERMISSION_VERIFY, ConfigConstValues.HQ_DEPARTMENT,totalAmount);
        if(taskGrade == null){
            //查不到等级，默认给4级
            LogUtil.audit("使用默认案件等级");
            taskGrade = 4;
        }

        PermissionUserDTO permissionUser = permissionService.getLatestGrade(Constants.PERMISSION_VERIFY,departmentCode,1);
        Integer auditGrade = permissionUser.getGrade();
        if(auditGrade == null){
            LogUtil.audit("使用默认审批等级");
            auditGrade = 1;
        }
        startTask.setTaskGrade(taskGrade);
        startTask.setAuditGrade(auditGrade);
        startTask.setDepartmentCode(permissionUser.getComCode());
        taskInfoMapper.addTaskInfo(startTask);
    }
    private BigDecimal getSumPayFee(String reportNo, Integer caseTimes) {
        BigDecimal b = policyPayDao.getSumPayFee(reportNo, caseTimes);
        b = b.setScale(2, BigDecimal.ROUND_DOWN);
        return b;
    }
    /**
     * 人工理算校验
     */
    private void checkSettle(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPays, List<PaymentItemComData> paymentItems,
                             EndorsementDTO endorsement) throws GlobalBusinessException {
        long start = System.currentTimeMillis();

        // 校验赔款金额是否大于立案金额，如果大于则无法提交完成
        settleValidateService.checkPolicyPay(policyPays,reportNo,caseTimes);

        if (caseTimes > 1) {
            // 重开单个人的赔付金额需大于上一次赔付金额 否则应该走追偿
            settleValidateService.checkLastPaymentItem(paymentItems,reportNo,caseTimes);
        }

        // 赔付金额就算是 0 也得录入领款人信息
        if(CollectionUtils.isEmpty(paymentItems)){
            throw new GlobalBusinessException("赔付信息不能为空，请填写后提交！");
        }
        paymentItems.forEach(paymentItemComData -> {
            if (org.apache.commons.lang3.StringUtils.isEmpty(paymentItemComData.getIdClmPaymentInfo())){
                throw new GlobalBusinessException("赔付信息里的领款人信息不能为空，请选择后提交！");
            }
        });

        // 如果赔付金额为0，没有必须进行下面的校验
        if (!settleValidateService.hasSettlePay(policyPays)) {
            return;
        }

        Map<String,PaymentInfoDTO> paymentInfoMap = getPaymentInfoMap(paymentItems);

        // 单个银行账户金额大于100万的时候开户行明细必填校验
        settleValidateService.checkBankDetail(paymentItems,paymentInfoMap);

        //校验保单是否有没有理算金额或费用项目
//        settleValidateService.checkSettled(policyPays, paymentItems);

        //校验批单信息是否为空
        settleValidateService.checkEndorsement(policyPays, endorsement);
        //校验限额
//        riskPropertyPayService.checkRiskPropertyPay(policyPays);
        //设置、校验最大赔付额
        setRollback(policyPays);
        settleValidateService.checkSettle(policyPays, paymentItems, reportNo, caseTimes);
        LogUtil.audit("checkSettle人工理算校验耗时:{},报案号:{},赔付次数:{}", System.currentTimeMillis() - start, reportNo, caseTimes);
    }
    private Map<String, PaymentInfoDTO> getPaymentInfoMap(List<PaymentItemComData> paymentItems) {
        Map<String, PaymentInfoDTO> paymentInfoMap = new HashMap<>();
        paymentItems.forEach(p -> paymentInfoMap.put(p.getIdClmPaymentInfo(), paymentInfoService.getPaymentInfoById(p.getIdClmPaymentInfo())));
        return paymentInfoMap;
    }

    /**
     * 更新批次表、保单赔付批次明细表
     */
    private void updateSettleBatch(ReceiveTpaSettleDTO dto, List<PolicyPayDTO> policyPays, boolean isSend,TaskInfoDTO taskinfo) {
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        BatchDTO batchDTO = batchDao.getBatchInfo(reportNo, caseTimes);
        if (isSend) {
            //发送
            batchDTO.setSettleStatus(SettleConst.SETTLE_STATUS_DONE);
        } else {
            //保存
            batchDTO.setSettleStatus(SettleConst.SETTLE_STATUS_ON);
        }
        batchDTO.setBatchSettleType(SettleConst.BATCH_SETTLE_TYPE_WHOLE_CASE);
        batchDTO.setSettleUserUm(taskinfo.getAssigner());
        batchDTO.setUpdatedBy(taskinfo.getAssigner());
        batchDao.updateBatch(batchDTO);

        //更新保单赔付批次明细表
        List<PolicyBatchPayDTO> policyBatchs = policyBatchMapper.listPolicyBatchs(reportNo, caseTimes);
        updatePolicyBatch(policyPays, policyBatchs);
        if (!CollectionUtils.isEmpty(policyBatchs)) {
            for (PolicyBatchPayDTO policyBatch : policyBatchs) {
                policyBatchMapper.bathUpdatePolicyBath(policyBatch);
            }
        }

    }
    private void updatePolicyBatch(List<PolicyPayDTO> policyPays, List<PolicyBatchPayDTO> policyBatchs) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return;
        }
        for (PolicyPayDTO policyPayInfo : policyPays) {
            BigDecimal policyPay = nvl(policyPayInfo.getSettleAmount(), 0);
            BigDecimal policyFee = nvl(policyPayInfo.getPolicySumFee(), 0);
            BigDecimal policyDecreaseFee = nvl(policyPayInfo.getPolicyDecreaseFee(), 0);
            PolicyBatchPayDTO policyBatch = getPolicyBatch(policyBatchs, policyPayInfo.getCaseNo());
            policyBatch.setPolicyFee(policyFee);
            policyBatch.setPolicyPayAmount(policyPay);
            policyBatch.setUpdatedBy("system");
            policyBatch.setPolicyDecreaseFee(policyDecreaseFee);
            BigDecimal prePolicyFee = getPrePolicyFee(policyBatchs, policyPayInfo.getCaseNo());
            policyBatch.setFinalFee(policyFee.subtract(prePolicyFee));
            policyBatch.setFinalPayAmount(policyPay.subtract(nvl(policyPayInfo.getPolicyPrePay(), 0)));
        }
    }
    private PolicyBatchPayDTO getPolicyBatch(List<PolicyBatchPayDTO> policyBatchs, String caseNo) {
        for (PolicyBatchPayDTO policyBatchPay : policyBatchs) {
            if (!SettleConst.CLAIM_TYPE_PAY.equals(policyBatchPay.getClaimType())) {
                continue;
            }
            if (caseNo.equals(policyBatchPay.getCaseNo())) {
                return policyBatchPay;
            }
        }
        return new PolicyBatchPayDTO();
    }
    private BigDecimal getPrePolicyFee(List<PolicyBatchPayDTO> policyBatchs, String caseNo) {
        BigDecimal prePolicyFee = BigDecimal.ZERO;
        for (PolicyBatchPayDTO policyBatchPay : policyBatchs) {
            if (!SettleConst.CLAIM_TYPE_PRE_PAY.equals(policyBatchPay.getClaimType())) {
                continue;
            }
            if (caseNo.equals(policyBatchPay.getCaseNo())) {
                prePolicyFee = sum(prePolicyFee, policyBatchPay.getPolicyFee());
            }
        }
        return prePolicyFee;
    }
    /**
     * 合并批单
     */
    private void mergeEndorsementInfo(ReceiveTpaSettleDTO dto, EndorsementDTO endorsement) {
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        String userId = WebServletContext.getUserId();
        EndorsementDTO endorInfo = endorsement;
        LogUtil.audit("mergeEndorsementInfo合并批单报案号{}，入参{}",reportNo, JSONObject.toJSONString(endorInfo));
        if (endorInfo == null) {
            return;
        }
        endorInfo.setIdAhcsEndorsement(UuidUtil.getUUID());
        endorInfo.setCaseTimes(caseTimes);
        endorInfo.setReportNo(reportNo);
        endorInfo.setCreatedBy(userId);
        endorInfo.setUpdatedBy(userId);
        endorsementService.mergeEndorsementInfo(endorInfo);
    }

    private void changeOperator(String reportNo, Integer caseTimes){
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        if (StringUtils.isEqualStr(userInfoDTO.getUserCode(), ConstValues.SYSTEM_UM)) {
            TaskInfoDTO tdto=  taskInfoMapper.getTaskAssignerName(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK);
            UserInfoDTO sysTemUserInfoDTO = new UserInfoDTO();
            sysTemUserInfoDTO.setUserCode(tdto.getAssigner());
            sysTemUserInfoDTO.setUserName(tdto.getAssigneeName());
            sysTemUserInfoDTO.setComCode(ConfigConstValues.HQ_DEPARTMENT);
            Objects.requireNonNull(WebServletContext.getRequest()).getSession().setAttribute(Constants.CURR_USER,
                    sysTemUserInfoDTO);
            WebServletContext.getRequest().getSession().setAttribute(Constants.CURR_COMCODE,
                    ConfigConstValues.HQ_DEPARTMENT);
            MDC.put(BaseConstant.USER_ID, tdto.getAssigner());
        }
    }

}
