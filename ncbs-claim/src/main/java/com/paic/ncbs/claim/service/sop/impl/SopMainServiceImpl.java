package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopMainMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopFileMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopConfigMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopDetailMapper;
import org.apache.commons.lang3.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.dto.sop.SopFileDTO;
import com.paic.ncbs.claim.model.dto.sop.SopDetailDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.sop.SopHistoryMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopFileVO;
import com.paic.ncbs.claim.model.vo.sop.SopDetailVO;
import com.paic.ncbs.claim.service.sop.SopMainService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.common.RedisService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.UserCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;

/**
 * SOP管理Service实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopMainServiceImpl implements SopMainService {

    @Autowired
    private ClmsSopMainMapper clmsSopMainMapper;

    @Autowired
    private ClmsSopFileMapper clmsSopFileMapper;

    @Autowired
    private ClmsSopConfigMapper clmsSopConfigMapper;

    @Autowired
    private ClmsSopDetailMapper clmsSopDetailMapper;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Autowired
    private RedisService redisService;
    
    @Autowired
    private UserCommonService userCommonService;


    @Override
    public List<SopMainVO> getSopList(SopQueryVO queryVO) {
        log.info("查询SOP列表，查询条件：{}", queryVO);

        // 调用查询方法
        List<SopMainVO> sopList = clmsSopMainMapper.selectSopList(queryVO);

        // 处理taskBpmKeys转义和产品方案信息关联
        if (!RapeCollectionUtils.isEmpty(sopList)) {
            for (SopMainVO sopMainVO : sopList) {
                processConfigInfoFromDatabase(sopMainVO);
                processTaskBpmKeysTranslation(sopMainVO);
                processProductGroupPlanInfo(sopMainVO);
            }
        }

        return sopList;
    }

    @Override
    public SopMainVO getSopDetail(String idSopMain) {
        log.info("获取SOP详情，idSopMain：{}", idSopMain);

        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");

        SopMainVO sopMainVO = clmsSopMainMapper.selectSopDetailById(idSopMain);
        if (sopMainVO == null) {
            throw new GlobalBusinessException("SOP不存在");
        }

        // 查询历史版本信息
        List<SopHistoryMainVO> sopHistoryMainVOS = clmsSopMainMapper.selectVersionListByBatchNo(sopMainVO.getBatchNo());
        sopMainVO.setSopHistoryMainVOS(sopHistoryMainVOS);

        // 查询详情列表
        List<ClmsSopDetail> detailEntityList = clmsSopDetailMapper.selectByIdSopMain(idSopMain);
        if (!RapeCollectionUtils.isEmpty(detailEntityList)) {
            List<SopDetailVO> detailList = new ArrayList<>();

            for (ClmsSopDetail detailEntity : detailEntityList) {
                SopDetailVO detailVO = new SopDetailVO();
                BeanUtils.copyProperties(detailEntity, detailVO);

                // 处理适用环节
                if (StringUtils.isNotEmpty(detailEntity.getTaskBpmKeys())) {
                    String[] taskBpmKeysArray = detailEntity.getTaskBpmKeys().split(",");
                    detailVO.setTaskBpmKey(Arrays.asList(taskBpmKeysArray));

                    // 转换环节名称
                    StringBuilder taskBpmKeyNames = new StringBuilder();
                    for (int i = 0; i < taskBpmKeysArray.length; i++) {
                        String key = taskBpmKeysArray[i].trim();
                        String translatedValue = BpmConstants.TASK_MAP.get(key);
                        if (StringUtils.isNotEmpty(translatedValue)) {
                            taskBpmKeyNames.append(translatedValue);
                        } else {
                            taskBpmKeyNames.append(key);
                        }
                        if (i < taskBpmKeysArray.length - 1) {
                            taskBpmKeyNames.append(",");
                        }
                    }
                    if (StringUtils.isEmpty(taskBpmKeyNames.toString())) {
                        detailVO.setTaskBpmKeyNames("全流程");
                    } else {
                        detailVO.setTaskBpmKeyNames(taskBpmKeyNames.toString());
                    }
                }

                // 查询关联的文件信息
                if (StringUtils.isNotEmpty(detailEntity.getFileIds())) {
                    String[] fileIds = detailEntity.getFileIds().split(",");
                    List<SopFileVO> fileList = new ArrayList<>();

                    for (String fileId : fileIds) {
                        ClmsSopFile fileEntity = clmsSopFileMapper.selectByFileId(fileId.trim());
                        if (fileEntity != null) {
                            SopFileVO fileVO = new SopFileVO();
                            BeanUtils.copyProperties(fileEntity, fileVO);
                            try {
                                String downloadUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileEntity.getFileId(), fileEntity.getFileName());
                                fileVO.setFileUrl(downloadUrl);
                            } catch (Exception e) {
                                log.error("获取文件下载地址失败，fileId：{}", fileEntity.getFileId(), e);
                            }
                            fileList.add(fileVO);
                        }
                    }
                    detailVO.setFileList(fileList);
                }

                detailList.add(detailVO);
            }

            sopMainVO.setDetailList(detailList);
        }

        // 处理taskBpmKeys转义和产品方案信息关联
        processConfigInfoFromDatabase(sopMainVO);
        processTaskBpmKeysTranslation(sopMainVO);
        processProductGroupPlanInfo(sopMainVO);

        log.info("获取SOP详情完成，SOP名称：{}", sopMainVO.getSopName());
        return sopMainVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateSop(SopMainDTO sopMainDTO) {
        log.info("SOP操作，SOP名称：{}，操作类型：{}", sopMainDTO.getSopName(), sopMainDTO.getInitFlag());
        String initFlag = sopMainDTO.getInitFlag();

        RapeStringUtils.checkIsEmpty(sopMainDTO.getSopName(), "SOP名称不能为空");
        RapeStringUtils.checkIsEmpty(initFlag, "操作类型不能为空");

        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();

        // 当id为空时，无法幂等校验，使用redis缓存防止重复新增，5秒过期
        if(StringUtils.isEmpty(sopMainDTO.getIdSopMain())){
            String duplicateKey = "sop:duplicate:submit:" + sopMainDTO.getSopName() + ":" + userInfo.getUserCode();
            if (redisService.checkDuplicateSubmission(duplicateKey)) {
                throw new GlobalBusinessException("SOP规则生成中，5秒内请勿重复提交：" + sopMainDTO.getSopName());
            }
            boolean setSuccess = redisService.setDuplicateSubmission(duplicateKey, 5L);
            if (!setSuccess) {
                log.error("SOP规则redis缓存设置失败");
            }
        }

        if ("01".equals(initFlag)) { // 暂存操作
            return handleSaveOperation(sopMainDTO, userInfo, now);
        } else if ("02".equals(initFlag)) { // 发布操作
            return handlePublishOperation(sopMainDTO, userInfo, now);
        } else if ("03".equals(initFlag)) { // 停用操作
            return handleDisableOperation(sopMainDTO.getIdSopMain(), userInfo, now);
        } else {
            throw new GlobalBusinessException("不支持的操作类型：" + initFlag);
        }
    }

    /**
     * 处理暂存操作
     */
    @Transactional(rollbackFor = Exception.class)
    public String handleSaveOperation(SopMainDTO sopMainDTO, UserInfoDTO userInfo, LocalDateTime now) {
        String idSopMain = sopMainDTO.getIdSopMain();
        boolean isUpdate = false;
        String batchNo = null;

        if (StringUtils.isNotEmpty(idSopMain)) {
            ClmsSopMain existingSop = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
            if (existingSop == null) {
                throw new GlobalBusinessException("SOP不存在，idSopMain：" + idSopMain);
            }
            batchNo = existingSop.getBatchNo();
            sopMainDTO.setBatchNo(batchNo);

            // 通过批次号查询最新一条数据
            ClmsSopMain existingEntity = clmsSopMainMapper.selectLatestByBatchNo(batchNo);
            if (existingEntity != null) {
                if ("01".equals(existingEntity.getStatus())) {
                    // 状态为01时更新原有数据，使用最新数据的idSopMain
                    idSopMain = existingEntity.getIdSopMain();
                    sopMainDTO.setIdSopMain(idSopMain);
                    isUpdate = true;
                } else if ("02".equals(existingEntity.getStatus()) || "03".equals(existingEntity.getStatus())) {
                    // 状态为02或03时新增一条数据
                    idSopMain = UuidUtil.getUUID();
                    sopMainDTO.setIdSopMain(idSopMain);
                    setupNewSopData(sopMainDTO, batchNo, generateNewVersionNo(existingEntity.getVersionNo()), userInfo, now);
                }
            }
        } else {
            idSopMain = UuidUtil.getUUID();
            batchNo = UuidUtil.getUUID();
            sopMainDTO.setIdSopMain(idSopMain);
            setupNewSopData(sopMainDTO, batchNo, "1.0", userInfo, now);
        }

        sopMainDTO.setStatus("01"); // 暂存状态
        sopMainDTO.setUpdatedBy(userInfo.getUserCode());

        ClmsSopMain entity = new ClmsSopMain();
        BeanUtils.copyProperties(sopMainDTO, entity);

        if (isUpdate) {
            entity.setSysUtime(now);
            clmsSopMainMapper.updateByPrimaryKeySelective(entity);
        } else {
            entity.setSysCtime(now);
            clmsSopMainMapper.insertSelective(entity);
        }

        // 清理旧的配置、详情和文件数据
        clmsSopConfigMapper.deleteByIdSopMain(idSopMain);
        clmsSopDetailMapper.deleteByIdSopMain(idSopMain);
        clmsSopFileMapper.deleteByIdSopMain(idSopMain);

        // 保存新的配置和详情数据
        saveConfigData(sopMainDTO, userInfo.getUserCode(), now, idSopMain);
        saveDetailData(sopMainDTO, userInfo.getUserCode(), now, idSopMain);

        log.info("暂存SOP完成，idSopMain：{}", idSopMain);
        return idSopMain;
    }

    /**
     * 处理发布操作
     */
    @Transactional(rollbackFor = Exception.class)
    public String handlePublishOperation(SopMainDTO sopMainDTO, UserInfoDTO userInfo, LocalDateTime now) {
        // 先执行暂存操作
        String idSopMain = handleSaveOperation(sopMainDTO, userInfo, now);

        // 查询当前SOP信息
        ClmsSopMain currentEntity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        String batchNo = currentEntity.getBatchNo();

        // 根据SOP名称查询最新数据，判断是否需要更新版本号
        ClmsSopMain latestEntity = clmsSopMainMapper.selectBySopName(currentEntity.getSopName());
        String newVersionNo = currentEntity.getVersionNo();

        if (latestEntity != null && ("02".equals(latestEntity.getStatus()) || "03".equals(latestEntity.getStatus()))) {
            // 如果最新数据状态为02或03，需要在原版本号基础上加0.1
            newVersionNo = generateNewVersionNo(latestEntity.getVersionNo());

            // 使同批次号的其他数据失效
            invalidateOtherVersionsByBatchNo(batchNo, idSopMain, userInfo.getUserCode(), now);
        }

        // 更新状态为发布
        ClmsSopMain entity = new ClmsSopMain();
        entity.setIdSopMain(idSopMain);
        entity.setVersionNo(newVersionNo);
        entity.setStatus("02");
        entity.setPublisherCode(userInfo.getUserCode());
        entity.setPublisherName(userInfo.getUserName());
        entity.setPublishTime(now);
        entity.setEffectiveDate(now);
        entity.setUpdatedBy(userInfo.getUserCode());
        entity.setSysUtime(now);

        clmsSopMainMapper.updateByPrimaryKeySelective(entity);

        // 发布成功后，将同批次的其他版本置为无效
        clmsSopMainMapper.updateInvalidDateByBatchNo(batchNo, idSopMain, userInfo.getUserCode(), now);

        log.info("发布SOP完成，idSopMain：{}，版本号：{}", idSopMain, newVersionNo);
        return idSopMain;
    }

    /**
     * 处理停用操作
     */
    @Transactional(rollbackFor = Exception.class)
    public String handleDisableOperation(String idSopMain, UserInfoDTO userInfo, LocalDateTime now) {
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");

        ClmsSopMain entity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        if (entity == null) {
            throw new GlobalBusinessException("SOP不存在");
        }

        if (!"02".equals(entity.getStatus())) {
            throw new GlobalBusinessException("只有有效状态的SOP才能停用");
        }

        // 更新状态为停用
        entity.setStatus("03");
        entity.setInvalidDate(now);
        entity.setUpdatedBy(userInfo.getUserCode());
        entity.setSysUtime(now);

        clmsSopMainMapper.updateByPrimaryKeySelective(entity);

        log.info("停用SOP完成，idSopMain：{}", idSopMain);
        return idSopMain;
    }



    /**
     * 生成新版本号（在原版本号基础上加0.1）
     */
    private String generateNewVersionNo(String currentVersionNo) {
        try {
            double version = Double.parseDouble(currentVersionNo);
            return String.format("%.1f", version + 0.1);
        } catch (NumberFormatException e) {
            log.warn("版本号格式异常，使用默认版本号：{}", currentVersionNo);
            return "1.0";
        }
    }

    /**
     * 使同批次号的其他数据失效
     */
    private void invalidateOtherVersionsByBatchNo(String batchNo, String excludeId, String updatedBy, LocalDateTime now) {
        if (StringUtils.isEmpty(batchNo)) {
            return;
        }

        // 更新同批次号的其他数据失效日期
        clmsSopMainMapper.updateInvalidDateByBatchNo(batchNo, excludeId, updatedBy, now);
        log.info("批次号{}的其他版本已失效，排除ID：{}", batchNo, excludeId);
    }

    @Override
    public boolean checkSopNameExists(String sopName) {
        int count = clmsSopMainMapper.countBySopName(sopName, null);
        return count > 0;
    }

    @Override
    public List<Object> getPlanInfoList() {
        return clmsSopMainMapper.selectPlanInfoList();
    }

    @Override
    public List<SopMainVO> matchSopRulesByCase(String reportNo, Integer caseTimes, String taskBpmKey) {
        return clmsSopMainMapper.selectMatchingSopRulesByCase(reportNo, caseTimes, taskBpmKey);
    }

    /**
     * 保存详情数据
     */
    private void saveDetailData(SopMainDTO sopMainDTO, String userId, LocalDateTime now, String idSopMain) {
        try {
            List<SopDetailDTO> detailList = sopMainDTO.getDetailList();
            if (RapeCollectionUtils.isEmpty(detailList)) {
                log.warn("SOP详情列表为空，跳过详情数据保存");
                return;
            }

            List<ClmsSopDetail> detailEntityList = new ArrayList<>();
            List<ClmsSopFile> allFileList = new ArrayList<>();

            for (SopDetailDTO detailDTO : detailList) {
                // 创建详情实体
                ClmsSopDetail detailEntity = new ClmsSopDetail();
                detailEntity.setIdSopDetail(UuidUtil.getUUID());
                detailEntity.setIdSopMain(idSopMain);
                detailEntity.setSopContent(detailDTO.getSopContent());
                detailEntity.setSortOrder(detailDTO.getSortOrder());
                detailEntity.setRemark(detailDTO.getRemark());
                detailEntity.setValidFlag("Y");
                detailEntity.setCreatedBy(userId);
                detailEntity.setSysCtime(now);
                detailEntity.setUpdatedBy(userId);
                detailEntity.setSysUtime(now);

                // 处理适用环节
                if (!RapeCollectionUtils.isEmpty(detailDTO.getTaskBpmKey())) {
                    String taskBpmKeysStr = String.join(",", detailDTO.getTaskBpmKey());
                    detailEntity.setTaskBpmKeys(taskBpmKeysStr);
                }

                // 处理文件列表
                List<SopFileDTO> fileList = detailDTO.getFileList();
                if (!RapeCollectionUtils.isEmpty(fileList)) {
                    List<String> fileIds = new ArrayList<>();

                    for (SopFileDTO fileDTO : fileList) {
                        // 创建文件实体
                        ClmsSopFile fileEntity = new ClmsSopFile();
                        fileEntity.setIdSopFile(UuidUtil.getUUID());
                        fileEntity.setIdSopMain(idSopMain);
                        fileEntity.setFileId(fileDTO.getFileId());
                        fileEntity.setFileUrl(fileDTO.getFileId());
                        fileEntity.setFileName(fileDTO.getFileName());
                        fileEntity.setFileFormat(fileDTO.getFileFormat());
                        fileEntity.setFileType(StringUtils.isNotEmpty(sopMainDTO.getFileType()) ? sopMainDTO.getFileType() : "01");
                        fileEntity.setUploadTime(now);
                        fileEntity.setValidFlag("Y");
                        fileEntity.setCreatedBy(userId);
                        fileEntity.setSysCtime(now);
                        fileEntity.setUpdatedBy(userId);
                        fileEntity.setSysUtime(now);

                        allFileList.add(fileEntity);
                        fileIds.add(fileDTO.getFileId());
                    }

                    // 设置文件ID列表
                    detailEntity.setFileIds(String.join(",", fileIds));
                }

                detailEntityList.add(detailEntity);
            }

            // 批量保存详情数据
            if (!RapeCollectionUtils.isEmpty(detailEntityList)) {
                clmsSopDetailMapper.batchInsert(detailEntityList);
            }

            // 批量保存文件数据
            if (!RapeCollectionUtils.isEmpty(allFileList)) {
                clmsSopFileMapper.batchInsert(allFileList);
            }

        } catch (Exception e) {
            log.error("保存详情数据失败", e);
            throw new GlobalBusinessException("保存详情数据失败：" + e.getMessage());
        }
    }

    /**
     * 保存配置数据
     */
    private void saveConfigData(SopMainDTO sopMainDTO, String userId, LocalDateTime now, String idSopMain) {
        // 获取产品、方案、险种、环节列表
        List<String> productCodes = sopMainDTO.getProductCode();
        List<String> groupCodes = sopMainDTO.getGroupCode();
        List<String> planCodes = sopMainDTO.getPlanCode();
        List<String> taskBpmKeys = sopMainDTO.getTaskBpmKey();

        List<ClmsSopConfig> configList = generateConfigList(idSopMain, productCodes, groupCodes, planCodes, taskBpmKeys, userId, now);

        if (!RapeCollectionUtils.isEmpty(configList)) {
            clmsSopConfigMapper.batchInsert(configList);
        }
    }

    /**
     * 生成配置列表
     */
    private List<ClmsSopConfig> generateConfigList(String idSopMain, List<String> productCodes, List<String> groupCodes, List<String> planCodes, List<String> taskBpmKeys, String userId, LocalDateTime now) {
        List<ClmsSopConfig> configList = new ArrayList<>();

        // 处理空值，设置为PUB
        if (RapeCollectionUtils.isEmpty(planCodes)) {
            planCodes = Arrays.asList("PUB");
        }
        if (RapeCollectionUtils.isEmpty(taskBpmKeys)) {
            taskBpmKeys = Arrays.asList("PUB");
        }

        List<ClmsSopConfig> productGroupConfigs = new ArrayList<>();

        // 根据不同的参数组合情况生成配置
        buildValidProductGroupPairs(idSopMain, productCodes, groupCodes, planCodes, taskBpmKeys, userId, now, productGroupConfigs);

        configList.addAll(productGroupConfigs);
        return configList;
    }
    
    /**
     * 构建有效的product-group配对列表
     */
    private List<ProductGroupPair> buildValidProductGroupPairs(String idSopMain, List<String> productCodes, List<String> groupCodes,
                                                               List<String> planCodes, List<String> taskBpmKeys,
                                                               String userId, LocalDateTime now, List<ClmsSopConfig> productGroupConfigs) {
        List<ProductGroupPair> validPairs = new ArrayList<>();

        // 当productCodes、groupCodes同时有值时，查询关联关系
        if (!RapeCollectionUtils.isEmpty(productCodes) && !RapeCollectionUtils.isEmpty(groupCodes)) {
            List<Map<String, Object>> productGroupAssociations = clmsSopMainMapper.selectProductGroupAssociations();

            if (!RapeCollectionUtils.isEmpty(productGroupAssociations)) {
                Set<String> matchedProducts = new HashSet<>();
                Set<String> matchedGroups = new HashSet<>();

                // 查找能够匹配上productCodes和groupCodes的关联关系
                for (Map<String, Object> association : productGroupAssociations) {
                    String associatedProductCode = (String) association.get("productCode");
                    String associatedGroupCode = (String) association.get("groupCode");

                    if (productCodes.contains(associatedProductCode) && groupCodes.contains(associatedGroupCode)) {
                        validPairs.add(new ProductGroupPair(associatedProductCode, associatedGroupCode));
                        matchedProducts.add(associatedProductCode);
                        matchedGroups.add(associatedGroupCode);
                    }
                }

                // 如果productCodes中有product没有匹配上groupCodes中的group，则用PUB填充group
                for (String productCode : productCodes) {
                    if (!matchedProducts.contains(productCode)) {
                        validPairs.add(new ProductGroupPair(productCode, "PUB"));
                    }
                }
            }
        }
        // 当productCodes有值、groupCodes没有值时，将groupCodes设置成PUB
        else if (!RapeCollectionUtils.isEmpty(productCodes) && RapeCollectionUtils.isEmpty(groupCodes)) {
            for (String productCode : productCodes) {
                validPairs.add(new ProductGroupPair(productCode, "PUB"));
            }
        }
        // 当groupCodes有值、productCodes没有值时，将productCodes设置成PUB
        else if (RapeCollectionUtils.isEmpty(productCodes) && !RapeCollectionUtils.isEmpty(groupCodes)) {
            for (String groupCode : groupCodes) {
                validPairs.add(new ProductGroupPair("PUB", groupCode));
            }
        }
        // 当productCodes、groupCodes同时没有值时，都设置成PUB
        else {
            validPairs.add(new ProductGroupPair("PUB", "PUB"));
        }

        // 为所有配对生成配置
        for (ProductGroupPair pair : validPairs) {
            for (String planCode : planCodes) {
                for (String taskBpmKey : taskBpmKeys) {
                    productGroupConfigs.add(createConfigEntity(idSopMain, pair.productCode, pair.groupCode, planCode, taskBpmKey, userId, now));
                }
            }
        }
        return validPairs;
    }
    
    /**
     * 产品-方案配对内部类
     */
    private static class ProductGroupPair {
        String productCode;
        String groupCode;
        
        public ProductGroupPair(String productCode, String groupCode) {
            this.productCode = productCode;
            this.groupCode = groupCode;
        }
    }

    /**
     * 创建配置实体
     */
    private ClmsSopConfig createConfigEntity(String idSopMain, String productCode, String groupCode, String planCode, String taskBpmKey, String userId, LocalDateTime now) {
        ClmsSopConfig config = new ClmsSopConfig();
        config.setIdSopConfig(UuidUtil.getUUID());
        config.setIdSopMain(idSopMain);
        config.setProductCode(productCode);
        config.setGroupCode(groupCode);
        config.setPlanCode(planCode);
        config.setTaskBpmKey(taskBpmKey);
        config.setCreatedBy(userId);
        config.setSysCtime(now);
        config.setUpdatedBy(userId);
        config.setSysUtime(now);
        return config;
    }

    /**
     * 设置新增SOP数据的基本信息
     */
    private void setupNewSopData(SopMainDTO sopMainDTO, String batchNo, String versionNo, UserInfoDTO userInfo, LocalDateTime now) {
        sopMainDTO.setIdSopMain(sopMainDTO.getIdSopMain());
        sopMainDTO.setBatchNo(batchNo);
        sopMainDTO.setVersionNo(versionNo);
        sopMainDTO.setValidFlag("Y");
        sopMainDTO.setCreatedBy(userInfo.getUserCode());
    }

    /**
     * 处理产品、方案、险种信息关联查询
     */
    private void processProductGroupPlanInfo(SopMainVO sopMainVO) {
        if (sopMainVO == null) {
            return;
        }

        // 处理产品信息
        String productCode = sopMainVO.getProductCode();
        if (StringUtils.isNotEmpty(productCode)) {
            List<String> productCodes = Arrays.asList(productCode.split(","));
            List<Map<String, Object>> productInfoList = clmsSopMainMapper.selectProductInfoByProductCodes(productCodes);
            if (!RapeCollectionUtils.isEmpty(productInfoList)) {
                StringBuilder productNames = new StringBuilder();
                for (int i = 0; i < productInfoList.size(); i++) {
                    Map<String, Object> productInfo = productInfoList.get(i);
                    productNames.append(productInfo.get("productName"));
                    if (i < productInfoList.size() - 1) {
                        productNames.append(",");
                    }
                }
                sopMainVO.setProductName(productNames.toString());
            }
        }

        // 处理方案信息
        String groupCode = sopMainVO.getGroupCode();
        if (StringUtils.isNotEmpty(groupCode)) {
            List<String> groupCodes = Arrays.asList(groupCode.split(","));
            List<Map<String, Object>> groupInfoList = clmsSopMainMapper.selectGroupInfoByGroupCodes(groupCodes);
            if (!RapeCollectionUtils.isEmpty(groupInfoList)) {
                StringBuilder groupNames = new StringBuilder();
                for (int i = 0; i < groupInfoList.size(); i++) {
                    Map<String, Object> groupInfo = groupInfoList.get(i);
                    groupNames.append(groupInfo.get("groupName"));
                    if (i < groupInfoList.size() - 1) {
                        groupNames.append(",");
                    }
                }
                sopMainVO.setGroupName(groupNames.toString());
            }
        }

        // 处理险种信息
        String planCode = sopMainVO.getPlanCode();
        if (StringUtils.isNotEmpty(planCode)) {
            List<String> planCodes = Arrays.asList(planCode.split(","));
            List<Map<String, Object>> planInfoList = clmsSopMainMapper.selectPlanInfoByPlanCodes(planCodes);
            if (!RapeCollectionUtils.isEmpty(planInfoList)) {
                StringBuilder planChineseNames = new StringBuilder();
                for (int i = 0; i < planInfoList.size(); i++) {
                    Map<String, Object> planInfo = planInfoList.get(i);
                    planChineseNames.append(planInfo.get("planChineseName"));
                    if (i < planInfoList.size() - 1) {
                        planChineseNames.append(",");
                    }
                }
                sopMainVO.setPlanChineseName(planChineseNames.toString());
            }
        }
    }

    /**
     * 通过idSopMain反查询clms_sop_config获取配置信息
     */
    private void processConfigInfoFromDatabase(SopMainVO sopMainVO) {
        if (sopMainVO == null || StringUtils.isEmpty(sopMainVO.getIdSopMain())) {
            return;
        }

        try {
            Map<String, String> configMap = clmsSopConfigMapper.selectAggregatedConfigByIdSopMain(sopMainVO.getIdSopMain());
            if (configMap != null) {
                sopMainVO.setTaskBpmKey(configMap.get("taskBmpKey"));
                sopMainVO.setProductCode(configMap.get("productCode"));
                sopMainVO.setGroupCode(configMap.get("groupCode"));
                sopMainVO.setPlanCode(configMap.get("planCode"));
            }
        } catch (Exception e) {
            log.error("查询SOP配置信息失败，idSopMain：{}", sopMainVO.getIdSopMain(), e);
        }
    }

    /**
     * 处理taskBpmKeys转义
     */
    private void processTaskBpmKeysTranslation(SopMainVO sopMainVO) {
        if (sopMainVO == null) {
            return;
        }

        String taskBpmKeys = sopMainVO.getTaskBpmKey();
        if (StringUtils.isNotEmpty(taskBpmKeys)) {
            String[] keys = taskBpmKeys.split(",");
            StringBuilder translatedKeys = new StringBuilder();

            for (int i = 0; i < keys.length; i++) {
                String key = keys[i].trim();
                String translatedValue = BpmConstants.TASK_MAP.get(key);
                if (StringUtils.isNotEmpty(translatedValue)) {
                    translatedKeys.append(translatedValue);
                } else {
                    translatedKeys.append(key);
                }

                if (i < keys.length - 1) {
                    translatedKeys.append(",");
                }
            }

            sopMainVO.setTaskBpmKeyName(translatedKeys.toString());
        }
    }

    /**
     * 获取当前用户信息
     */
    private UserInfoDTO getUserInfo() {
        return WebServletContext.getUser();
    }

    /**
     * 获取环节下拉框数据
     * 
     * @return 环节下拉框列表，包含taskBpmKey和taskBpmKeyName
     */
    @Override
    public List<Map<String, String>> getTaskDropdownList() {
        List<Map<String, String>> taskList = new ArrayList<>();
        
        // 遍历BpmConstants.TASK_MAP中的所有环节
        for (Map.Entry<String, String> entry : BpmConstants.TASK_MAP.entrySet()) {
            Map<String, String> taskMap = new HashMap<>();
            taskMap.put("taskBpmKey", entry.getKey());      // 码值
            taskMap.put("taskBpmKeyName", entry.getValue()); // 中文名称
            taskList.add(taskMap);
        }
        
        return taskList;
    }
    
    /**
     * 根据机构号和岗位权限查询人员信息列表
     *
     * @return 人员信息列表，包含userCode、userName和department信息
     */
    public List<Map<String, String>> getUserInfoBySOP() {
        List<Map<String, String>> userList = new ArrayList<>();
        
        try {
            // 调用用户服务接口查询符合机构和岗位条件的用户列表
            List<UserInfoDTO> userInfoList = userCommonService.queryUserInfoList(Constants.DEPARTMENT_CODE, "CLAIM-11");
            
            if (!RapeCollectionUtils.isEmpty(userInfoList)) {
                for (UserInfoDTO userInfo : userInfoList) {
                    Map<String, String> userMap = new HashMap<>();
                    userMap.put("userCode", userInfo.getUserCode());
                    userMap.put("userName", userInfo.getUserName());
                    userMap.put("departmentCode", userInfo.getComCode());
                    userMap.put("departmentName", userInfo.getComName());
                    userList.add(userMap);
                }
            }
        } catch (Exception e) {
            log.error("根据机构号和岗位权限查询人员信息失败，rolePermission:{}", "核赔岗-新", e);
        }
        
        return userList;
    }

    /**
     * 批量上传文件
     *
     * @param request HTTP请求（用于获取多个文件）
     * @return 文件信息列表，包含fileId、fileName、fileFormat
     */
    @Override
    public List<SopFileDTO> batchUploadFiles(HttpServletRequest request) {
        List<SopFileDTO> fileList = new ArrayList<>();

        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

            if (fileMap.isEmpty()) {
                log.warn("批量上传文件：未找到任何文件");
                return fileList;
            }

            for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                MultipartFile file = entry.getValue();
                if (file != null && !file.isEmpty()) {
                    String fileName = file.getOriginalFilename();
                    if (StringUtils.isEmpty(fileName)) {
                        log.warn("批量上传文件：文件名为空，跳过该文件");
                        continue;
                    }

                    String fileFormat = "";
                    if (fileName.contains(".")) {
                        fileFormat = fileName.substring(fileName.lastIndexOf(".") + 1);
                    }

                    // 上传文件到IOBS
                    String fileId = iobsFileUploadService.uploadFileToFilePlatform(fileName, file.getBytes());

                    // 创建文件DTO
                    SopFileDTO sopFileDTO = new SopFileDTO();
                    sopFileDTO.setFileId(fileId);
                    sopFileDTO.setFileName(fileName);
                    sopFileDTO.setFileFormat(fileFormat);

                    // 获取文件下载地址
                    try {
                        String fileUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileId, fileName);
                        sopFileDTO.setFileUrl(fileUrl);
                    } catch (Exception e) {
                        log.error("获取文件下载地址失败，fileId：{}", fileId, e);
                    }

                    fileList.add(sopFileDTO);
                    log.info("批量上传文件成功：fileName={}, fileId={}", fileName, fileId);
                }
            }

        } catch (Exception e) {
            log.error("批量上传文件失败", e);
            throw new GlobalBusinessException("批量上传文件失败：" + e.getMessage());
        }

        return fileList;
    }
}
