<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity">
        <id column="ID_AHCS_REPORT_INFO_EX" property="idAhcsReportInfoEx" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="CASE_CLASS" property="caseClass" jdbcType="VARCHAR"/>
        <result column="RELATION_WITH_REPORTER" property="relationWithReporter" jdbcType="VARCHAR"/>
        <result column="LINK_MAN_NAME" property="linkManName" jdbcType="VARCHAR"/>
        <result column="LINK_MAN_RELATION" property="linkManRelation" jdbcType="VARCHAR"/>
        <result column="SEND_MESSAGE" property="sendMessage" jdbcType="VARCHAR"/>
        <result column="REPORT_REMARK" property="reportRemark" jdbcType="VARCHAR"/>
        <result column="COST_ESTIMATE" property="costEstimate" jdbcType="DECIMAL"/>
        <result column="SUCCOR_SERVICE" property="succorService" jdbcType="VARCHAR"/>
        <result column="SUCCOR_SERVICE_CODE" property="succorServiceCode" jdbcType="VARCHAR"/>
        <result column="PARTNER_CODE" property="partnerCode" jdbcType="VARCHAR"/>
        <result column="WHETHER_AUTHORIZED" property="whetherAuthorized" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_GROUP_ID" property="documentGroupId" jdbcType="VARCHAR"/>
        <result column="IS_SPECIAL_REPORT" property="isSpecialReport" jdbcType="VARCHAR"/>
        <result column="CASE_SIGN" property="caseSign" jdbcType="VARCHAR"/>
        <result column="SUCCOR_COMPANY" property="succorCompany" jdbcType="VARCHAR"/>
        <result column="SUCCOR_COMPANY_NAME" property="succorCompanyName" jdbcType="VARCHAR"/>
        <result column="SUCCOR_NO" property="succorNo" jdbcType="VARCHAR"/>
        <result column="SUCCOR_SERVICE_LEVEL" property="succorServiceLevel" jdbcType="VARCHAR"/>
        <result column="SUCCOR_SERVICE_NAME" property="succorServiceName" jdbcType="VARCHAR"/>
        <result column="IS_REPEAT_REPORT" property="isRepeatReport" jdbcType="VARCHAR"/>
        <result column="REPORT_EXTEND" property="reportExtend" jdbcType="VARCHAR"/>
        <result column="CASE_TYPE" property="caseType" jdbcType="VARCHAR"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
        <result column="IS_QUICK_PAY" property="isQuickPay" jdbcType="VARCHAR"/>
        <result column="CLAIM_DEAL_WAY" property="claimDealWay" jdbcType="VARCHAR"/>
        <result column="ACCEPTANCE_NO" property="acceptanceNumber" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_REPORT_INFO_EX, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_CLASS, RELATION_WITH_REPORTER, LINK_MAN_NAME, LINK_MAN_RELATION, SEND_MESSAGE,
        REPORT_REMARK, COST_ESTIMATE, SUCCOR_SERVICE, SUCCOR_SERVICE_CODE, PARTNER_CODE,
        WHETHER_AUTHORIZED,DOCUMENT_GROUP_ID, IS_SPECIAL_REPORT, CASE_SIGN,
        IS_REPEAT_REPORT, REPORT_EXTEND,CASE_TYPE,COMPANY_ID,IS_QUICK_PAY,CLAIM_DEAL_WAY,ACCEPTANCE_NO
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_INFO_EX
        where ID_AHCS_REPORT_INFO_EX = #{idAhcsReportInfoEx,jdbcType=VARCHAR}
    </select>

    <select id="getAcceptanceNoByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from clms_report_info_ex
        where report_no = #{reportNo,jdbcType=VARCHAR}
        order by created_date desc
        limit 1
    </select>

    <select id="getReportInfoEx" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,SUCCOR_COMPANY,SUCCOR_COMPANY_NAME,SUCCOR_NO,SUCCOR_SERVICE_LEVEL,SUCCOR_SERVICE_NAME
        from CLMS_REPORT_INFO_EX
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_REPORT_INFO_EX
        where ID_AHCS_REPORT_INFO_EX = #{idAhcsReportInfoEx,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity">
        insert into CLMS_REPORT_INFO_EX (ID_AHCS_REPORT_INFO_EX, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_CLASS, RELATION_WITH_REPORTER, LINK_MAN_NAME,
        LINK_MAN_RELATION, SEND_MESSAGE, REPORT_REMARK,COST_ESTIMATE,SUCCOR_SERVICE,SUCCOR_SERVICE_CODE
        ,PARTNER_CODE,WHETHER_AUTHORIZED,DOCUMENT_GROUP_ID,IS_SPECIAL_REPORT,CASE_SIGN
        ,SUCCOR_COMPANY,SUCCOR_COMPANY_NAME,SUCCOR_NO,SUCCOR_SERVICE_LEVEL,SUCCOR_SERVICE_NAME,IS_REPEAT_REPORT
        ,REPORT_EXTEND,CASE_TYPE,RESIDENCE_PROVINCE,RESIDENCE_CITY,RESIDENCE_DISTRICT,RESIDENCE_ADDRESS,wesure_auto_claim
        ,risk_level_score,risk_level_desc,COMPANY_ID,IS_QUICK_PAY,CLAIM_DEAL_WAY,ACCEPTANCE_NO)
        values (#{idAhcsReportInfoEx,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
        #{caseClass,jdbcType=VARCHAR}, #{relationWithReporter,jdbcType=VARCHAR}, #{linkManName,jdbcType=VARCHAR},
        #{linkManRelation,jdbcType=VARCHAR}, #{sendMessage,jdbcType=VARCHAR}, #{reportRemark,jdbcType=VARCHAR},
        #{costEstimate,jdbcType=DECIMAL},#{succorService,jdbcType=VARCHAR},#{succorServiceCode,jdbcType=VARCHAR},
        #{partnerCode,jdbcType=VARCHAR},#{whetherAuthorized,jdbcType=VARCHAR},#{documentGroupId,jdbcType=VARCHAR},
        #{isSpecialReport,jdbcType=VARCHAR},#{caseSign,jdbcType=VARCHAR},#{succorCompany,jdbcType=VARCHAR},#{succorCompanyName,jdbcType=VARCHAR},
        #{succorNo,jdbcType=VARCHAR},#{succorServiceLevel,jdbcType=VARCHAR},#{succorServiceName,jdbcType=VARCHAR},
        #{isRepeatReport,jdbcType=VARCHAR},#{reportExtend,jdbcType=VARCHAR},#{caseType,jdbcType=VARCHAR},
        #{residenceProvince,jdbcType=VARCHAR},#{residenceCity,jdbcType=VARCHAR},#{residenceDistrict,jdbcType=VARCHAR},#{residenceAddress,jdbcType=VARCHAR},
        #{wesureAutoClaim,jdbcType=VARCHAR},
        #{riskLevelScore,jdbcType=INTEGER},
        #{riskLevelDesc,jdbcType=VARCHAR},
        #{companyId,jdbcType=VARCHAR},
        #{isQuickPay,jdbcType=VARCHAR},
        #{claimDealWay,jdbcType=VARCHAR},
        #{acceptanceNumber,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity">
        update CLMS_REPORT_INFO_EX
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseClass != null">
                CASE_CLASS = #{caseClass,jdbcType=VARCHAR},
            </if>
            <if test="relationWithReporter != null">
                RELATION_WITH_REPORTER = #{relationWithReporter,jdbcType=VARCHAR},
            </if>
            <if test="linkManName != null">
                LINK_MAN_NAME = #{linkManName,jdbcType=VARCHAR},
            </if>
            <if test="linkManRelation != null">
                LINK_MAN_RELATION = #{linkManRelation,jdbcType=VARCHAR},
            </if>
            <if test="sendMessage != null">
                SEND_MESSAGE = #{sendMessage,jdbcType=VARCHAR},
            </if>
            <if test="reportRemark != null">
                REPORT_REMARK = #{reportRemark,jdbcType=VARCHAR},
            </if>
            <if test="costEstimate != null">
                COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
            </if>
            <if test="succorService != null">
                SUCCOR_SERVICE = #{succorService,jdbcType=VARCHAR},
            </if>
            SUCCOR_SERVICE_CODE = #{succorServiceCode,jdbcType=VARCHAR},
            SUCCOR_COMPANY = #{succorCompany,jdbcType=VARCHAR},
            SUCCOR_COMPANY_NAME = #{succorCompanyName,jdbcType=VARCHAR},
            SUCCOR_SERVICE_LEVEL = #{succorServiceLevel,jdbcType=VARCHAR},
            SUCCOR_SERVICE_NAME = #{succorServiceName,jdbcType=VARCHAR},
            <if test="succorNo != null">
                SUCCOR_NO = #{succorNo,jdbcType=VARCHAR},
            </if>
            <if test="partnerCode != null">
                PARTNER_CODE = #{partnerCode,jdbcType=VARCHAR},
            </if>
            <if test="whetherAuthorized != null">
                WHETHER_AUTHORIZED = #{whetherAuthorized,jdbcType=VARCHAR},
            </if>
            <if test="documentGroupId != null">
                DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
            </if>
            <if test="isSpecialReport != null">
                IS_SPECIAL_REPORT = #{isSpecialReport,jdbcType=VARCHAR},
            </if>
            <if test="isSpecialReport != null">
                CASE_SIGN = #{caseSign,jdbcType=VARCHAR},
            </if>
            <if test="isRepeatReport != null">
                IS_REPEAT_REPORT = #{isRepeatReport,jdbcType=VARCHAR},
            </if>
            <if test="reportExtend != null">
                REPORT_EXTEND = #{reportExtend,jdbcType=VARCHAR},
            </if>
            <if test="claimDealWay != null">
                CLAIM_DEAL_WAY = #{claimDealWay,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_REPORT_INFO_EX = #{idAhcsReportInfoEx,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity">
        update CLMS_REPORT_INFO_EX
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        CASE_CLASS = #{caseClass,jdbcType=VARCHAR},
        RELATION_WITH_REPORTER = #{relationWithReporter,jdbcType=VARCHAR},
        LINK_MAN_NAME = #{linkManName,jdbcType=VARCHAR},
        LINK_MAN_RELATION = #{linkManRelation,jdbcType=VARCHAR},
        SEND_MESSAGE = #{sendMessage,jdbcType=VARCHAR},
        REPORT_REMARK = #{reportRemark,jdbcType=VARCHAR},
        COST_ESTIMATE = #{costEstimate,jdbcType=DECIMAL},
        SUCCOR_SERVICE = #{succorService,jdbcType=VARCHAR},
        SUCCOR_SERVICE_CODE = #{succorServiceCode,jdbcType=VARCHAR},
        PARTNER_CODE = #{partnerCode,jdbcType=VARCHAR},
        WHETHER_AUTHORIZED = #{whetherAuthorized,jdbcType=VARCHAR},
        DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
        IS_SPECIAL_REPORT = #{isSpecialReport,jdbcType=VARCHAR},
        CASE_SIGN = #{caseSign,jdbcType=VARCHAR},
        IS_REPEAT_REPORT = #{isRepeatReport,jdbcType=VARCHAR},
        REPORT_EXTEND = #{reportExtend,jdbcType=VARCHAR},
        CLAIM_DEAL_WAY = #{claimDealWay,jdbcType=VARCHAR}
        where ID_AHCS_REPORT_INFO_EX = #{idAhcsReportInfoEx,jdbcType=VARCHAR}
    </update>

    <update id="updataReportInfoExByReportNo">
        update CLMS_REPORT_INFO_EX
        set IS_REPEAT_REPORT = #{isRepeatReport,jdbcType=VARCHAR}
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>

    <update id="updatadDocumentGroupIdByReportNo">
        update CLMS_REPORT_INFO_EX
        set DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR}
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_REPORT_INFO_EX (ID_AHCS_REPORT_INFO_EX, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_CLASS, RELATION_WITH_REPORTER, LINK_MAN_NAME,
        LINK_MAN_RELATION, SEND_MESSAGE, REPORT_REMARK,COST_ESTIMATE,SUCCOR_SERVICE,SUCCOR_SERVICE_CODE
        ,PARTNER_CODE,WHETHER_AUTHORIZED,DOCUMENT_GROUP_ID,IS_SPECIAL_REPORT,CASE_SIGN
        ,SUCCOR_COMPANY,SUCCOR_COMPANY_NAME,SUCCOR_NO,SUCCOR_SERVICE_LEVEL,SUCCOR_SERVICE_NAME,IS_REPEAT_REPORT
        ,REPORT_EXTEND,CASE_TYPE,ACCEPTANCE_NO)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsReportInfoEx,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseClass,jdbcType=VARCHAR},
            #{item.relationWithReporter,jdbcType=VARCHAR},
            #{item.linkManName,jdbcType=VARCHAR},
            #{item.linkManRelation,jdbcType=VARCHAR},
            #{item.sendMessage,jdbcType=VARCHAR},
            #{item.reportRemark,jdbcType=VARCHAR},
            #{item.costEstimate,jdbcType=DECIMAL},
            #{item.succorService,jdbcType=VARCHAR},
            #{item.succorServiceCode,jdbcType=VARCHAR},
            #{item.partnerCode,jdbcType=VARCHAR},
            #{item.whetherAuthorized,jdbcType=VARCHAR},
            #{item.documentGroupId,jdbcType=VARCHAR},
            #{item.isSpecialReport,jdbcType=VARCHAR},
            #{item.caseSign,jdbcType=VARCHAR},
            #{item.succorCompany,jdbcType=VARCHAR},
            #{item.succorCompanyName,jdbcType=VARCHAR},
            #{item.succorNo,jdbcType=VARCHAR},
            #{item.succorServiceLevel,jdbcType=VARCHAR},
            #{item.succorServiceName,jdbcType=VARCHAR},
            #{item.isRepeatReport,jdbcType=VARCHAR},
            #{item.reportExtend,jdbcType=VARCHAR},
            #{item.caseType,jdbcType=VARCHAR},
            #{item.acceptanceNumber,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getReportInfoExByAcceptanceNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,SUCCOR_COMPANY,SUCCOR_COMPANY_NAME,SUCCOR_NO,SUCCOR_SERVICE_LEVEL,SUCCOR_SERVICE_NAME
        from CLMS_REPORT_INFO_EX
        where acceptance_no = #{acceptanceNo,jdbcType=VARCHAR}
    </select>

    <update id="updateCompanyIdByReportNo">
        update CLMS_REPORT_INFO_EX
        set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
        UPDATED_BY = 'SYSTEM',
        UPDATED_DATE = now()
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>
    <update id="updateCaseClassByReportNo">
        update CLMS_REPORT_INFO_EX
        set CASE_CLASS = #{caseClass,jdbcType=VARCHAR},
        UPDATED_BY = #{loginUm,jdbcType=VARCHAR},
        UPDATED_DATE = now()
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </update>
</mapper>