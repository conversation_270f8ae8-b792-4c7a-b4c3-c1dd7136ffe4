<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity">
        <id column="ID_AHCS_REPORT_CUSTOMER" property="idAhcsReportCustomer" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="CLIENT_NO" property="clientNo" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR"/>
        <result column="AGE" property="age" jdbcType="DECIMAL"/>
        <result column="SEX_CODE" property="sexCode" jdbcType="VARCHAR"/>
        <result column="BIRTHDAY" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="CLIENT_CATEGOTY" property="clientCategoty" jdbcType="VARCHAR"/>
        <result column="CLIENT_TYPE" property="clientType" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="CLIENT_CATEGOTY_NAME" property="clientCategotyName" jdbcType="VARCHAR"/>
        <result column="CLIENT_TYPE_NAME" property="clientTypeName" jdbcType="VARCHAR"/>
        <result column="LEVEL_DATE" property="levelDate" jdbcType="TIMESTAMP"/>
        <result column="GROUP_RISK_LEVEL" property="groupRiskLevel" jdbcType="VARCHAR"/>
        <result column="GROUP_LAST_ACCORDING" property="groupLastAccording" jdbcType="VARCHAR"/>
        <result column="OPEN_ID" property="openId" jdbcType="VARCHAR"/>
        <result column="IS_ORGANIZATION" property="isOrganization" jdbcType="VARCHAR"/>
        <result column="CLIENT_CLUSTER" property="clientCluster" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_REPORT_CUSTOMER, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, CLIENT_NO,
        NAME, CERTIFICATE_TYPE,
        CERTIFICATE_NO, AGE, SEX_CODE, BIRTHDAY,
        CLIENT_CATEGOTY,
        CLIENT_TYPE,
        REMARK, REPORT_NO, CLIENT_CATEGOTY_NAME, CLIENT_TYPE_NAME, LEVEL_DATE, GROUP_RISK_LEVEL,
        GROUP_LAST_ACCORDING, OPEN_ID, IS_ORGANIZATION,CLIENT_CLUSTER
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_REPORT_CUSTOMER
        where ID_AHCS_REPORT_CUSTOMER =
        #{idAhcsReportCustomer,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from
        CLMS_REPORT_CUSTOMER
        where ID_AHCS_REPORT_CUSTOMER =
        #{idAhcsReportCustomer,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity">
        insert into CLMS_REPORT_CUSTOMER
        (ID_AHCS_REPORT_CUSTOMER, CREATED_BY,
        CREATED_DATE, UPDATED_BY,
        UPDATED_DATE,
        CLIENT_NO, NAME, CERTIFICATE_TYPE,
        CERTIFICATE_NO, AGE,
        SEX_CODE,
        BIRTHDAY, CLIENT_CATEGOTY, CLIENT_TYPE, CLIENT_TYPE_NAME,
        CLIENT_CATEGOTY_NAME,LEVEL_DATE, GROUP_RISK_LEVEL, GROUP_LAST_ACCORDING,
        REMARK, REPORT_NO, OPEN_ID, IS_ORGANIZATION,CLIENT_CLUSTER)
        values
        (#{idAhcsReportCustomer,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{clientNo,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
        #{certificateType,jdbcType=VARCHAR},
        #{certificateNo,jdbcType=VARCHAR}, #{age,jdbcType=DECIMAL},
        #{sexCode,jdbcType=VARCHAR},
        #{birthday,jdbcType=TIMESTAMP},
        #{clientCategoty,jdbcType=VARCHAR}, #{clientType,jdbcType=VARCHAR},
        #{clientTypeName,jdbcType=VARCHAR},#{clientCategotyName,jdbcType=VARCHAR},
        #{levelDate,jdbcType=TIMESTAMP},#{groupRiskLevel,jdbcType=VARCHAR},
        #{groupLastAccording,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR},
        #{openId,jdbcType=VARCHAR},#{isOrganization,jdbcType=VARCHAR},
        #{clientCluster,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity">
        update CLMS_REPORT_CUSTOMER
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="clientNo != null">
                CLIENT_NO = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                AGE = #{age,jdbcType=DECIMAL},
            </if>
            <if test="sexCode != null">
                SEX_CODE = #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="clientCategoty != null">
                CLIENT_CATEGOTY = #{clientCategoty,jdbcType=VARCHAR},
            </if>
            <if test="clientType != null">
                CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="clientTypeName != null">
                CLIENT_TYPE_NAME = #{clientTypeName,jdbcType=VARCHAR},
            </if>
            <if test="clientCategotyName != null">
                CLIENT_CATEGOTY_NAME =
                #{clientCategotyName,jdbcType=VARCHAR},
            </if>
            <if test="levelDate != null">
                LEVEL_DATE = #{levelDate,jdbcType=TIMESTAMP},
            </if>
            <if test="groupRiskLevel != null">
                GROUP_RISK_LEVEL = #{groupRiskLevel,jdbcType=VARCHAR},
            </if>
            <if test="groupLastAccording != null">
                GROUP_LAST_ACCORDING = #{groupLastAccording,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                OPEN_ID = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="isOrganization != null">
                IS_ORGANIZATION = #{isOrganization,jdbcType=VARCHAR},
            </if>
            <if test="clientCluster != null">
                CLIENT_CLUSTER = #{clientCluster,jdbcType=VARCHAR},
            </if>

        </set>
        where ID_AHCS_REPORT_CUSTOMER =
        #{idAhcsReportCustomer,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity">
        update CLMS_REPORT_CUSTOMER
        set CREATED_BY =
        #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE =
        #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY =
        #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE =
        #{updatedDate,jdbcType=TIMESTAMP},
        CLIENT_NO =
        #{clientNo,jdbcType=VARCHAR},
        NAME = #{name,jdbcType=VARCHAR},
        CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
        CERTIFICATE_NO
        = #{certificateNo,jdbcType=VARCHAR},
        AGE = #{age,jdbcType=DECIMAL},
        SEX_CODE = #{sexCode,jdbcType=VARCHAR},
        BIRTHDAY =
        #{birthday,jdbcType=TIMESTAMP},
        CLIENT_CATEGOTY =
        #{clientCategoty,jdbcType=VARCHAR},
        CLIENT_TYPE =
        #{clientType,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        LEVEL_DATE = #{levelDate,jdbcType=TIMESTAMP},
        GROUP_RISK_LEVEL = #{groupRiskLevel,jdbcType=VARCHAR},
        GROUP_LAST_ACCORDING = #{groupLastAccording,jdbcType=VARCHAR},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        OPEN_ID = #{openId,jdbcType=VARCHAR},
        IS_ORGANIZATION = #{isOrganization,jdbcType=VARCHAR}
        where ID_AHCS_REPORT_CUSTOMER
        = #{idAhcsReportCustomer,jdbcType=VARCHAR}
    </update>

    <select id="getReportCustomerInfoByReportNo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM CLMS_REPORT_CUSTOMER
        WHERE REPORT_NO =
        #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getReportCustomerInfo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM CLMS_REPORT_CUSTOMER
        WHERE CERTIFICATE_NO =
        #{certificateNo,jdbcType=VARCHAR}
        AND REPORT_NO =
        #{reportNo,jdbcType=VARCHAR}
    </select>
    <select id="getHistoryByPolicyNo"
            resultType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity">
        SELECT distinct
        <include refid="Base_Column_List"/>
        FROM CLMS_REPORT_CUSTOMER
        WHERE REPORT_NO  IN  (select distinct  `REPORT_NO`  from  clms_policy_info where `POLICY_NO` = #{policyNo,jdbcType=VARCHAR} )
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_REPORT_CUSTOMER
        (ID_AHCS_REPORT_CUSTOMER, CREATED_BY,
        CREATED_DATE, UPDATED_BY,
        UPDATED_DATE,
        CLIENT_NO, NAME, CERTIFICATE_TYPE,
        CERTIFICATE_NO, AGE,
        SEX_CODE,
        BIRTHDAY, CLIENT_CATEGOTY, CLIENT_TYPE, CLIENT_TYPE_NAME,
        CLIENT_CATEGOTY_NAME,LEVEL_DATE, GROUP_RISK_LEVEL, GROUP_LAST_ACCORDING,
        REMARK, REPORT_NO, OPEN_ID, IS_ORGANIZATION,CLIENT_CLUSTER)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsReportCustomer,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.clientNo,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.certificateType,jdbcType=VARCHAR},
            #{item.certificateNo,jdbcType=VARCHAR}, #{item.age,jdbcType=DECIMAL},
            #{item.sexCode,jdbcType=VARCHAR},
            #{item.birthday,jdbcType=TIMESTAMP},
            #{item.clientCategoty,jdbcType=VARCHAR}, #{item.clientType,jdbcType=VARCHAR},
            #{item.clientTypeName,jdbcType=VARCHAR},#{item.clientCategotyName,jdbcType=VARCHAR},
            #{item.levelDate,jdbcType=TIMESTAMP},#{item.groupRiskLevel,jdbcType=VARCHAR},
            #{item.groupLastAccording,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR},
            #{item.openId,jdbcType=VARCHAR},#{item.isOrganization,jdbcType=VARCHAR},
            #{item.clientCluster,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <select id="getCustomerName" resultType="com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity">
        select name,sex_code sexCode from clms_report_customer where REPORT_NO = #{reportNo}
    </select>
    <select id="getCustomerIcdInfo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.openapi.IcdDto">
        select distinct a.REPORT_NO reportNo,c.policy_no policyNo,b.DIAGNOSE_CODE icDCode,a.CLIENT_NO
        from clms_report_customer a,CLMS_person_diagnose b,clms_policy_info c
        where a.REPORT_NO=b.REPORT_NO and a.REPORT_NO=c.REPORT_NO
        and a.CLIENT_NO=#{customerNo}
    </select>
    <select id="getHistoryReportInfo" parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO" resultType="com.paic.ncbs.claim.model.dto.endcase.CustomerReprotInfoDTO">
        select  a.report_no reportNo,b.WHOLE_CASE_STATUS caseStatus ,b.INDEMNITY_CONCLUSION indemnityConclusion
        from clms_report_customer a
        inner join  clm_whole_case_base b on a.report_no=b.report_no
        <if test="productCode!=null and productCode!='' ">
            inner join  clms_policy_info c  on a.report_no=c.report_no
        </if>
        where   name=#{name}
        and CERTIFICATE_TYPE=#{certificateType}
        and CERTIFICATE_NO=#{certificateNo}
        <if test="productCode!=null and productCode!=''">
            and c.PRODUCT_CODE=#{productCode}
        </if>
        and  b.case_times=1
    </select>
    <select id="getCustomerHistoryPlanInfo" parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO" resultType="java.lang.String">
        select distinct a.REPORT_NO
        from CLM_POLICY_PAY a,CLM_PLAN_PAY c ,clms_report_customer d,clms_policy_info b,clm_whole_case_base e
        where a.REPORT_NO=d.REPORT_NO  and a.REPORT_NO=b.REPORT_NO
        and a.CASE_NO=b.CASE_NO and a.POLICY_NO=b.POLICY_NO
        and   a.CASE_NO=c.case_no and a.case_times=c.CASE_TIMES
        and   a.report_no=e.report_no and a.case_times=e.case_times
        and   e.WHOLE_CASE_STATUS='0'
        and   c.PLAN_PAY_AMOUNT>0
        and 	d.name=#{name}
        and d.CERTIFICATE_TYPE=#{certificateType}
        and d.CERTIFICATE_NO=#{certificateNo}
        and b.PRODUCT_CODE=#{productCode}
        and c.PLAN_CODE in
        <foreach collection="planCodes"  open="(" close=")" item="item" separator="," >
            #{item}
        </foreach>

    </select>
</mapper>