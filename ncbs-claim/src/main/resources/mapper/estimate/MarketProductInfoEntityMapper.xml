<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.MarketProductInfoEntityMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity">
        <id column="ID_MARKETPRODUCT_INFO" property="idMarketproductInfo" jdbcType="VARCHAR"/>
        <result column="MARKETPRODUCT_CODE" property="marketproductCode" jdbcType="VARCHAR"/>
        <result column="MARKETPRODUCT_NAME" property="marketproductName" jdbcType="VARCHAR"/>
        <result column="MARKETPRODUCT_DESC" property="marketproductDesc" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="MARKETPRODUCT_TYPE" property="marketproductType" jdbcType="CHAR"/>
        <result column="IS_SELF_CARD" property="isSelfCard" jdbcType="CHAR"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR"/>
        <result column="PRODUCT_CLASS" property="productClass" jdbcType="VARCHAR"/>
        <result column="HAS_BATCH_TARGET" property="hasBatchTarget" jdbcType="CHAR"/>
        <result column="IS_COMBINED" property="isCombined" jdbcType="CHAR"/>
        <result column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="TARGET_TYPE" property="targetType" jdbcType="VARCHAR"/>
        <result column="ID_TECHNIC_PRODUCT_INFO" property="idTechnicProductInfo" jdbcType="VARCHAR"/>
        <result column="COMBIND_RELATION" property="combindRelation" jdbcType="CHAR"/>
        <result column="NOT_ALLOWED_SEP_SALE" property="notAllowedSepSale" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="SHARE_INSURED_AMOUNT" property="shareInsuredAmount" jdbcType="CHAR"/>
        <result column="POLICY_TYPE" property="policyType" jdbcType="CHAR"/>
        <result column="NOT_BATCH_POLICY" property="notBatchPolicy" jdbcType="CHAR"/>
        <result column="MASTER_MARKETPRODUCT" property="masterMarketproduct" jdbcType="VARCHAR"/>
        <result column="EFFECTIVE_DATE" property="effectiveDate" jdbcType="TIMESTAMP"/>
        <result column="INVALIDATE_DATE" property="invalidateDate" jdbcType="TIMESTAMP"/>
        <result column="PRODUCT_CATEGORY" property="productCategory" jdbcType="VARCHAR"/>
        <result column="IS_DUTY_FREE" property="isDutyFree" jdbcType="CHAR"/>
        <result column="RELATED_INSURED_PERSON" property="relatedInsuredPerson" jdbcType="CHAR"/>
        <result column="IS_ALL_PURPOSE_CARD" property="isAllPurposeCard" jdbcType="VARCHAR"/>
        <result column="POLICY_DOCUMENT" property="policyDocument" jdbcType="VARCHAR"/>
        <result column="ENDORSE_DOCUMENT" property="endorseDocument" jdbcType="VARCHAR"/>
        <result column="INSURANCE_DOCUMENT" property="insuranceDocument" jdbcType="VARCHAR"/>
        <result column="IS_ADD_ADDITIONAL_TARGET" property="isAddAdditionalTarget" jdbcType="VARCHAR"/>
        <result column="RECORD_TYPE" property="recordType" jdbcType="VARCHAR"/>
        <result column="RECORD_REMARK" property="recordRemark" jdbcType="VARCHAR"/>
        <result column="PRODUCT_PRINT_NAME" property="productPrintName" jdbcType="VARCHAR"/>
        <result column="IS_REMIT_QUOTE" property="isRemitQuote" jdbcType="CHAR"/>
        <result column="IS_SPECIFY_VEHICLE" property="isSpecifyVehicle" jdbcType="VARCHAR"/>
        <result column="AUTO_ADJUST" property="autoAdjust" jdbcType="CHAR"/>
        <result column="ALLOW_PACKAGE_COMBINE" property="allowPackageCombine" jdbcType="CHAR"/>
        <result column="PAGE_TYPE" property="pageType" jdbcType="VARCHAR"/>
        <result column="CLAIM_BEFORE_INSURANCE" property="claimBeforeInsurance" jdbcType="CHAR"/>
        <result column="IS_AUTO_CLAIM" property="isAutoClaim" jdbcType="CHAR"/>
        <result column="IS_FAMILY_ACCOUNT" property="isFamilyAccount" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_MARKETPRODUCT_INFO, MARKETPRODUCT_CODE, MARKETPRODUCT_NAME, MARKETPRODUCT_DESC,
        STATUS, MARKETPRODUCT_TYPE, IS_SELF_CARD, VERSION, PRODUCT_CLASS, HAS_BATCH_TARGET,
        IS_COMBINED, DEPARTMENT_CODE, TARGET_TYPE, ID_TECHNIC_PRODUCT_INFO, COMBIND_RELATION,
        NOT_ALLOWED_SEP_SALE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, SHARE_INSURED_AMOUNT,
        POLICY_TYPE, NOT_BATCH_POLICY, MASTER_MARKETPRODUCT, EFFECTIVE_DATE, INVALIDATE_DATE,
        PRODUCT_CATEGORY, IS_DUTY_FREE, RELATED_INSURED_PERSON, IS_ALL_PURPOSE_CARD, POLICY_DOCUMENT,
        ENDORSE_DOCUMENT, INSURANCE_DOCUMENT, IS_ADD_ADDITIONAL_TARGET, RECORD_TYPE, RECORD_REMARK,
        PRODUCT_PRINT_NAME, IS_REMIT_QUOTE, IS_SPECIFY_VEHICLE, AUTO_ADJUST, ALLOW_PACKAGE_COMBINE,
        PAGE_TYPE, CLAIM_BEFORE_INSURANCE, IS_AUTO_CLAIM, IS_FAMILY_ACCOUNT
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_MARKETPRODUCT_INFO
        where ID_MARKETPRODUCT_INFO = #{idMarketproductInfo}
    </select>
    <select id="getMarketProductInfoByProductCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_MARKETPRODUCT_INFO
        where MARKETPRODUCT_CODE = #{productCode}
        and VERSION = #{productVersion}
        and STATUS = '1'
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from BASE_MARKETPRODUCT_INFO
        where ID_MARKETPRODUCT_INFO = #{idMarketproductInfo}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity">
        insert into BASE_MARKETPRODUCT_INFO (ID_MARKETPRODUCT_INFO, MARKETPRODUCT_CODE,
        MARKETPRODUCT_NAME, MARKETPRODUCT_DESC, STATUS,
        MARKETPRODUCT_TYPE, IS_SELF_CARD, VERSION,
        PRODUCT_CLASS, HAS_BATCH_TARGET, IS_COMBINED,
        DEPARTMENT_CODE, TARGET_TYPE, ID_TECHNIC_PRODUCT_INFO,
        COMBIND_RELATION, NOT_ALLOWED_SEP_SALE, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        SHARE_INSURED_AMOUNT, POLICY_TYPE, NOT_BATCH_POLICY,
        MASTER_MARKETPRODUCT, EFFECTIVE_DATE, INVALIDATE_DATE,
        PRODUCT_CATEGORY, IS_DUTY_FREE, RELATED_INSURED_PERSON,
        IS_ALL_PURPOSE_CARD, POLICY_DOCUMENT, ENDORSE_DOCUMENT,
        INSURANCE_DOCUMENT, IS_ADD_ADDITIONAL_TARGET,
        RECORD_TYPE, RECORD_REMARK, PRODUCT_PRINT_NAME,
        IS_REMIT_QUOTE, IS_SPECIFY_VEHICLE, AUTO_ADJUST,
        ALLOW_PACKAGE_COMBINE, PAGE_TYPE, CLAIM_BEFORE_INSURANCE,
        IS_AUTO_CLAIM)
        values (#{idMarketproductInfo}, #{marketproductCode},
        #{marketproductName}, #{marketproductDesc}, #{status},
        #{marketproductType}, #{isSelfCard}, #{version},
        #{productClass}, #{hasBatchTarget}, #{isCombined},
        #{departmentCode}, #{targetType}, #{idTechnicProductInfo},
        #{combindRelation}, #{notAllowedSepSale}, #{createdBy},
        #{createdDate}, #{updatedBy}, #{updatedDate},
        #{shareInsuredAmount}, #{policyType}, #{notBatchPolicy},
        #{masterMarketproduct}, #{effectiveDate},
        #{invalidateDate},
        #{productCategory}, #{isDutyFree}, #{relatedInsuredPerson},
        #{isAllPurposeCard}, #{policyDocument}, #{endorseDocument},
        #{insuranceDocument}, #{isAddAdditionalTarget},
        #{recordType}, #{recordRemark}, #{productPrintName},
        #{isRemitQuote}, #{isSpecifyVehicle}, #{autoAdjust},
        #{allowPackageCombine}, #{pageType}, #{claimBeforeInsurance},
        #{isAutoClaim})
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity">
        insert into BASE_MARKETPRODUCT_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idMarketproductInfo != null">
                ID_MARKETPRODUCT_INFO,
            </if>
            <if test="marketproductCode != null">
                MARKETPRODUCT_CODE,
            </if>
            <if test="marketproductName != null">
                MARKETPRODUCT_NAME,
            </if>
            <if test="marketproductDesc != null">
                MARKETPRODUCT_DESC,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="marketproductType != null">
                MARKETPRODUCT_TYPE,
            </if>
            <if test="isSelfCard != null">
                IS_SELF_CARD,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="productClass != null">
                PRODUCT_CLASS,
            </if>
            <if test="hasBatchTarget != null">
                HAS_BATCH_TARGET,
            </if>
            <if test="isCombined != null">
                IS_COMBINED,
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE,
            </if>
            <if test="targetType != null">
                TARGET_TYPE,
            </if>
            <if test="idTechnicProductInfo != null">
                ID_TECHNIC_PRODUCT_INFO,
            </if>
            <if test="combindRelation != null">
                COMBIND_RELATION,
            </if>
            <if test="notAllowedSepSale != null">
                NOT_ALLOWED_SEP_SALE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="shareInsuredAmount != null">
                SHARE_INSURED_AMOUNT,
            </if>
            <if test="policyType != null">
                POLICY_TYPE,
            </if>
            <if test="notBatchPolicy != null">
                NOT_BATCH_POLICY,
            </if>
            <if test="masterMarketproduct != null">
                MASTER_MARKETPRODUCT,
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE,
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE,
            </if>
            <if test="productCategory != null">
                PRODUCT_CATEGORY,
            </if>
            <if test="isDutyFree != null">
                IS_DUTY_FREE,
            </if>
            <if test="relatedInsuredPerson != null">
                RELATED_INSURED_PERSON,
            </if>
            <if test="isAllPurposeCard != null">
                IS_ALL_PURPOSE_CARD,
            </if>
            <if test="policyDocument != null">
                POLICY_DOCUMENT,
            </if>
            <if test="endorseDocument != null">
                ENDORSE_DOCUMENT,
            </if>
            <if test="insuranceDocument != null">
                INSURANCE_DOCUMENT,
            </if>
            <if test="isAddAdditionalTarget != null">
                IS_ADD_ADDITIONAL_TARGET,
            </if>
            <if test="recordType != null">
                RECORD_TYPE,
            </if>
            <if test="recordRemark != null">
                RECORD_REMARK,
            </if>
            <if test="productPrintName != null">
                PRODUCT_PRINT_NAME,
            </if>
            <if test="isRemitQuote != null">
                IS_REMIT_QUOTE,
            </if>
            <if test="isSpecifyVehicle != null">
                IS_SPECIFY_VEHICLE,
            </if>
            <if test="autoAdjust != null">
                AUTO_ADJUST,
            </if>
            <if test="allowPackageCombine != null">
                ALLOW_PACKAGE_COMBINE,
            </if>
            <if test="pageType != null">
                PAGE_TYPE,
            </if>
            <if test="claimBeforeInsurance != null">
                CLAIM_BEFORE_INSURANCE,
            </if>
            <if test="isAutoClaim != null">
                IS_AUTO_CLAIM,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idMarketproductInfo != null">
                #{idMarketproductInfo},
            </if>
            <if test="marketproductCode != null">
                #{marketproductCode},
            </if>
            <if test="marketproductName != null">
                #{marketproductName},
            </if>
            <if test="marketproductDesc != null">
                #{marketproductDesc},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="marketproductType != null">
                #{marketproductType},
            </if>
            <if test="isSelfCard != null">
                #{isSelfCard},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="productClass != null">
                #{productClass},
            </if>
            <if test="hasBatchTarget != null">
                #{hasBatchTarget},
            </if>
            <if test="isCombined != null">
                #{isCombined},
            </if>
            <if test="departmentCode != null">
                #{departmentCode},
            </if>
            <if test="targetType != null">
                #{targetType},
            </if>
            <if test="idTechnicProductInfo != null">
                #{idTechnicProductInfo},
            </if>
            <if test="combindRelation != null">
                #{combindRelation},
            </if>
            <if test="notAllowedSepSale != null">
                #{notAllowedSepSale},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDate != null">
                #{createdDate},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDate != null">
                #{updatedDate},
            </if>
            <if test="shareInsuredAmount != null">
                #{shareInsuredAmount},
            </if>
            <if test="policyType != null">
                #{policyType},
            </if>
            <if test="notBatchPolicy != null">
                #{notBatchPolicy},
            </if>
            <if test="masterMarketproduct != null">
                #{masterMarketproduct},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate},
            </if>
            <if test="invalidateDate != null">
                #{invalidateDate},
            </if>
            <if test="productCategory != null">
                #{productCategory},
            </if>
            <if test="isDutyFree != null">
                #{isDutyFree},
            </if>
            <if test="relatedInsuredPerson != null">
                #{relatedInsuredPerson},
            </if>
            <if test="isAllPurposeCard != null">
                #{isAllPurposeCard},
            </if>
            <if test="policyDocument != null">
                #{policyDocument},
            </if>
            <if test="endorseDocument != null">
                #{endorseDocument},
            </if>
            <if test="insuranceDocument != null">
                #{insuranceDocument},
            </if>
            <if test="isAddAdditionalTarget != null">
                #{isAddAdditionalTarget},
            </if>
            <if test="recordType != null">
                #{recordType},
            </if>
            <if test="recordRemark != null">
                #{recordRemark},
            </if>
            <if test="productPrintName != null">
                #{productPrintName},
            </if>
            <if test="isRemitQuote != null">
                #{isRemitQuote},
            </if>
            <if test="isSpecifyVehicle != null">
                #{isSpecifyVehicle},
            </if>
            <if test="autoAdjust != null">
                #{autoAdjust},
            </if>
            <if test="allowPackageCombine != null">
                #{allowPackageCombine},
            </if>
            <if test="pageType != null">
                #{pageType},
            </if>
            <if test="claimBeforeInsurance != null">
                #{claimBeforeInsurance},
            </if>
            <if test="isAutoClaim != null">
                #{isAutoClaim},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity">
        update BASE_MARKETPRODUCT_INFO
        <set>
            <if test="marketproductCode != null">
                MARKETPRODUCT_CODE = #{marketproductCode},
            </if>
            <if test="marketproductName != null">
                MARKETPRODUCT_NAME = #{marketproductName},
            </if>
            <if test="marketproductDesc != null">
                MARKETPRODUCT_DESC = #{marketproductDesc},
            </if>
            <if test="status != null">
                STATUS = #{status},
            </if>
            <if test="marketproductType != null">
                MARKETPRODUCT_TYPE = #{marketproductType},
            </if>
            <if test="isSelfCard != null">
                IS_SELF_CARD = #{isSelfCard},
            </if>
            <if test="version != null">
                VERSION = #{version},
            </if>
            <if test="productClass != null">
                PRODUCT_CLASS = #{productClass},
            </if>
            <if test="hasBatchTarget != null">
                HAS_BATCH_TARGET = #{hasBatchTarget},
            </if>
            <if test="isCombined != null">
                IS_COMBINED = #{isCombined},
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE = #{departmentCode},
            </if>
            <if test="targetType != null">
                TARGET_TYPE = #{targetType},
            </if>
            <if test="idTechnicProductInfo != null">
                ID_TECHNIC_PRODUCT_INFO = #{idTechnicProductInfo},
            </if>
            <if test="combindRelation != null">
                COMBIND_RELATION = #{combindRelation},
            </if>
            <if test="notAllowedSepSale != null">
                NOT_ALLOWED_SEP_SALE = #{notAllowedSepSale},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate},
            </if>
            <if test="shareInsuredAmount != null">
                SHARE_INSURED_AMOUNT = #{shareInsuredAmount},
            </if>
            <if test="policyType != null">
                POLICY_TYPE = #{policyType},
            </if>
            <if test="notBatchPolicy != null">
                NOT_BATCH_POLICY = #{notBatchPolicy},
            </if>
            <if test="masterMarketproduct != null">
                MASTER_MARKETPRODUCT = #{masterMarketproduct},
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE = #{effectiveDate},
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE = #{invalidateDate},
            </if>
            <if test="productCategory != null">
                PRODUCT_CATEGORY = #{productCategory},
            </if>
            <if test="isDutyFree != null">
                IS_DUTY_FREE = #{isDutyFree},
            </if>
            <if test="relatedInsuredPerson != null">
                RELATED_INSURED_PERSON = #{relatedInsuredPerson},
            </if>
            <if test="isAllPurposeCard != null">
                IS_ALL_PURPOSE_CARD = #{isAllPurposeCard},
            </if>
            <if test="policyDocument != null">
                POLICY_DOCUMENT = #{policyDocument},
            </if>
            <if test="endorseDocument != null">
                ENDORSE_DOCUMENT = #{endorseDocument},
            </if>
            <if test="insuranceDocument != null">
                INSURANCE_DOCUMENT = #{insuranceDocument},
            </if>
            <if test="isAddAdditionalTarget != null">
                IS_ADD_ADDITIONAL_TARGET = #{isAddAdditionalTarget},
            </if>
            <if test="recordType != null">
                RECORD_TYPE = #{recordType},
            </if>
            <if test="recordRemark != null">
                RECORD_REMARK = #{recordRemark},
            </if>
            <if test="productPrintName != null">
                PRODUCT_PRINT_NAME = #{productPrintName},
            </if>
            <if test="isRemitQuote != null">
                IS_REMIT_QUOTE = #{isRemitQuote},
            </if>
            <if test="isSpecifyVehicle != null">
                IS_SPECIFY_VEHICLE = #{isSpecifyVehicle},
            </if>
            <if test="autoAdjust != null">
                AUTO_ADJUST = #{autoAdjust},
            </if>
            <if test="allowPackageCombine != null">
                ALLOW_PACKAGE_COMBINE = #{allowPackageCombine},
            </if>
            <if test="pageType != null">
                PAGE_TYPE = #{pageType},
            </if>
            <if test="claimBeforeInsurance != null">
                CLAIM_BEFORE_INSURANCE = #{claimBeforeInsurance},
            </if>
            <if test="isAutoClaim != null">
                IS_AUTO_CLAIM = #{isAutoClaim},
            </if>
        </set>
        where ID_MARKETPRODUCT_INFO = #{idMarketproductInfo}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity">
        update BASE_MARKETPRODUCT_INFO
        set MARKETPRODUCT_CODE = #{marketproductCode},
        MARKETPRODUCT_NAME = #{marketproductName},
        MARKETPRODUCT_DESC = #{marketproductDesc},
        STATUS = #{status},
        MARKETPRODUCT_TYPE = #{marketproductType},
        IS_SELF_CARD = #{isSelfCard},
        VERSION = #{version},
        PRODUCT_CLASS = #{productClass},
        HAS_BATCH_TARGET = #{hasBatchTarget},
        IS_COMBINED = #{isCombined},
        DEPARTMENT_CODE = #{departmentCode},
        TARGET_TYPE = #{targetType},
        ID_TECHNIC_PRODUCT_INFO = #{idTechnicProductInfo},
        COMBIND_RELATION = #{combindRelation},
        NOT_ALLOWED_SEP_SALE = #{notAllowedSepSale},
        CREATED_BY = #{createdBy},
        CREATED_DATE = #{createdDate},
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = #{updatedDate},
        SHARE_INSURED_AMOUNT = #{shareInsuredAmount},
        POLICY_TYPE = #{policyType},
        NOT_BATCH_POLICY = #{notBatchPolicy},
        MASTER_MARKETPRODUCT = #{masterMarketproduct},
        EFFECTIVE_DATE = #{effectiveDate},
        INVALIDATE_DATE = #{invalidateDate},
        PRODUCT_CATEGORY = #{productCategory},
        IS_DUTY_FREE = #{isDutyFree},
        RELATED_INSURED_PERSON = #{relatedInsuredPerson},
        IS_ALL_PURPOSE_CARD = #{isAllPurposeCard},
        POLICY_DOCUMENT = #{policyDocument},
        ENDORSE_DOCUMENT = #{endorseDocument},
        INSURANCE_DOCUMENT = #{insuranceDocument},
        IS_ADD_ADDITIONAL_TARGET = #{isAddAdditionalTarget},
        RECORD_TYPE = #{recordType},
        RECORD_REMARK = #{recordRemark},
        PRODUCT_PRINT_NAME = #{productPrintName},
        IS_REMIT_QUOTE = #{isRemitQuote},
        IS_SPECIFY_VEHICLE = #{isSpecifyVehicle},
        AUTO_ADJUST = #{autoAdjust},
        ALLOW_PACKAGE_COMBINE = #{allowPackageCombine},
        PAGE_TYPE = #{pageType},
        CLAIM_BEFORE_INSURANCE = #{claimBeforeInsurance},
        IS_AUTO_CLAIM = #{isAutoClaim}
        where ID_MARKETPRODUCT_INFO = #{idMarketproductInfo}
    </update>

    <select id="getAllMarketProductInfo" parameterType="java.util.Map" resultType="com.paic.ncbs.claim.model.vo.taskdeal.MarketProductInfoVO">
        SELECT
            t.marketproduct_code
                AS productCode,
            t.marketproduct_name productName
        FROM
            marketproduct_info t
        WHERE
            t.`STATUS` = '1'
            and t.marketproduct_name is not null
            <if test="productNameOrCode != null and productNameOrCode != ''">
                AND (t.marketproduct_name like CONCAT('%',#{productNameOrCode},'%') or t.marketproduct_code like CONCAT('%',#{productNameOrCode},'%'))
            </if>
        order by t.marketproduct_code
    </select>

    <select id="getAllRiskGroupInfo" resultType="com.paic.ncbs.claim.model.vo.taskdeal.GroupInfoVO" parameterType="java.util.Map">
        SELECT
        CONCAT_WS( '_', ROW_NUMBER() OVER ( ORDER BY t.packageCode ), t.packageCode,t.packageName ) AS packageCode,
        t.packageName
        FROM
        (
        SELECT DISTINCT pa.package_code as packageCode,
                        pa.package_name as packageName
        FROM
            package_info pa
                LEFT JOIN marketproduct_info m ON m.id_marketproduct_info = pa.id_marketproduct_info
        WHERE
            1 = 1
        <if test="productCode != null and productCode != ''" >
            <choose>
                <when test="productCode.indexOf(',') > -1">
                    and m.MARKETPRODUCT_CODE IN
                    <foreach collection="@java.util.Arrays@asList(productCode.split(','))" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                    and m.MARKETPRODUCT_CODE = #{productCode}
                </otherwise>
            </choose>
        </if>
        <if test="packageNameOrCode != null and packageNameOrCode != ''">
            AND (pa.package_code like CONCAT('%',#{packageNameOrCode},'%') or pa.package_name like CONCAT('%',#{packageNameOrCode},'%'))
        </if>
        ORDER BY
            pa.package_code) t
    </select>
</mapper>