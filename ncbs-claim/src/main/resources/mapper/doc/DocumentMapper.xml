<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.doc.DocumentMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO">
        <id column="DOCUMENT_GROUP_ITEMS_ID" jdbcType="VARCHAR" property="documentGroupItemsId"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_DATE" jdbcType="TIMESTAMP" property="updatedDate"/>
        <result column="DOCUMENT_GROUP_ID" jdbcType="DECIMAL" property="documentGroupId"/>
        <result column="DOCUMENT_ID" jdbcType="VARCHAR" property="documentId"/>
        <result column="DOCUMENT_ORDER" jdbcType="DECIMAL" property="documentOrder"/>
        <result column="DOCUMENT_STATUS" jdbcType="VARCHAR" property="documentStatus"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="DOCUMENT_TYPE" jdbcType="VARCHAR" property="documentType"/>
        <result column="DOCUMENT_NAME" jdbcType="VARCHAR" property="documentName"/>
        <result column="ORIGIN_NAME" jdbcType="VARCHAR" property="originName"/>
        <result column="DOCUMENT_FORMAT" jdbcType="VARCHAR" property="documentFormat"/>
        <result column="DOCUMENT_DESC" jdbcType="VARCHAR" property="documentDesc"/>
        <result column="UPLOAD_PERSONNEL" jdbcType="VARCHAR" property="uploadPersonnel"/>
        <result column="UPLOAD_DATE" jdbcType="TIMESTAMP" property="uploadDate"/>
        <result column="UPLOAD_PATH" jdbcType="VARCHAR" property="uploadPath"/>
        <result column="DOCUMENT_CLASS" jdbcType="VARCHAR" property="documentClass"/>
        <result column="DOCUMENT_SIZE" jdbcType="DECIMAL" property="documentSize"/>
        <result column="DOCUMENT_NO" jdbcType="DECIMAL" property="documentNo"/>
        <result column="UPLOAD_IS_DATE" jdbcType="TIMESTAMP" property="uploadIsDate"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="UPLOADFLAG" jdbcType="VARCHAR" property="uploadFlag"/>
        <result column="QUEUEID" jdbcType="VARCHAR" property="queueId"/>
        <result column="PAGE_COUNT" jdbcType="VARCHAR" property="pageCount"/>
        <result column="GENERATED_DATE" jdbcType="TIMESTAMP" property="generatedDate"/>
        <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude"/>
        <result column="LATITUDE" jdbcType="DECIMAL" property="latitude"/>
        <result column="DOCUMENT_SOURCE" jdbcType="VARCHAR" property="documentSource"/>
        <result column="DOCUMENT_PROPERTY" jdbcType="VARCHAR" property="documentProperty"/>
        <result column="CERTIFICATE_BEGIN_DATE" jdbcType="TIMESTAMP" property="certificateBeginDate"/>
        <result column="CERTIFICATE_END_DATE" jdbcType="TIMESTAMP" property="certificateEndDate"/>
        <result column="NETWORK_FLAG" jdbcType="VARCHAR" property="networkFlag"/>
    </resultMap>

    <resultMap id="documentGroupInfo"
               type="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentGroupDTO">
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_DATE" jdbcType="TIMESTAMP" property="updatedDate"/>
        <result column="DOCUMENT_GROUP_ID" jdbcType="DECIMAL" property="documentGroupId"/>
        <result column="DOCUMENT_GROUP_NAME" jdbcType="VARCHAR" property="documentGroupName"/>
        <result column="DOCUMENT_GROUP_TYPE" jdbcType="VARCHAR" property="documentGroupType"/>
        <result column="DOCUMENT_GROUP_DESC" jdbcType="VARCHAR" property="documentGroupDesc"/>
        <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode"/>
        <result column="DATA_SOURCE" jdbcType="VARCHAR" property="dataSource"/>
    </resultMap>

    <resultMap id="documentInfoDocument"
               type="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO">
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_DATE" jdbcType="TIMESTAMP" property="updatedDate"/>
        <result column="DOCUMENT_ID" jdbcType="TIMESTAMP" property="documentId"/>
        <result column="DOCUMENT_TYPE" jdbcType="VARCHAR" property="documentType"/>
        <result column="DOCUMENT_NAME" jdbcType="VARCHAR" property="documentName"/>
        <result column="ORIGIN_NAME" jdbcType="VARCHAR" property="originName"/>
        <result column="DOCUMENT_FORMAT" jdbcType="VARCHAR" property="documentFormat"/>
        <result column="DOCUMENT_DESC" jdbcType="VARCHAR" property="documentDesc"/>
        <result column="UPLOAD_PERSONNEL" jdbcType="VARCHAR" property="uploadPersonnel"/>
        <result column="UPLOAD_DATE" jdbcType="TIMESTAMP" property="uploadDate"/>
        <result column="UPLOAD_PATH" jdbcType="VARCHAR" property="uploadPath"/>
        <result column="DOCUMENT_CLASS" jdbcType="VARCHAR" property="documentClass"/>
        <result column="DOCUMENT_SIZE" jdbcType="VARCHAR" property="documentSize"/>
        <result column="DOCUMENT_NO" jdbcType="VARCHAR" property="documentNo"/>
        <result column="UPLOAD_IS_DATE" jdbcType="TIMESTAMP" property="uploadIsDate"/>


        <result column="DOCUMENT_GROUP_ITEMS_ID" jdbcType="VARCHAR" property="documentGroupItemsId"/>
        <result column="DOCUMENT_ORDER" jdbcType="VARCHAR" property="documentOrder"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="DOCUMENT_STATUS" jdbcType="VARCHAR" property="documentStatus"/>
        <result column="UPLOADFLAG" jdbcType="VARCHAR" property="uploadFlag"/>
        <result column="PAGE_COUNT" jdbcType="VARCHAR" property="pageCount"/>
        <result column="GENERATED_DATE" jdbcType="TIMESTAMP" property="generatedDate"/>
        <result column="LONGITUDE" jdbcType="VARCHAR" property="longitude"/>
        <result column="LATITUDE" jdbcType="VARCHAR" property="latitude"/>
        <result column="DOCUMENT_SOURCE" jdbcType="VARCHAR" property="documentSource"/>
        <result column="DOCUMENT_PROPERTY" jdbcType="VARCHAR" property="documentProperty"/>
        <result column="CERTIFICATE_BEGIN_DATE" jdbcType="TIMESTAMP" property="certificateBeginDate"/>
        <result column="CERTIFICATE_END_DATE" jdbcType="TIMESTAMP" property="certificateEndDate"/>
        <result column="NETWORK_FLAG" jdbcType="VARCHAR" property="networkFlag"/>
    </resultMap>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        DOCUMENT_GROUP_ITEMS_ID, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, DOCUMENT_GROUP_ID,
        DOCUMENT_ID, DOCUMENT_ORDER, DOCUMENT_STATUS, REMARK, DOCUMENT_TYPE, DOCUMENT_NAME,
        ORIGIN_NAME, DOCUMENT_FORMAT, DOCUMENT_DESC, UPLOAD_PERSONNEL, UPLOAD_DATE, UPLOAD_PATH,
        DOCUMENT_CLASS, DOCUMENT_SIZE, DOCUMENT_NO, UPLOAD_IS_DATE, STATUS, UPLOADFLAG, QUEUEID,
        PAGE_COUNT, GENERATED_DATE, LONGITUDE, LATITUDE, DOCUMENT_SOURCE, DOCUMENT_PROPERTY,
        CERTIFICATE_BEGIN_DATE, CERTIFICATE_END_DATE, NETWORK_FLAG
        FROM DOCUMENT
        WHERE DOCUMENT_GROUP_ITEMS_ID = #{documentGroupItemsId,jdbcType=VARCHAR}
    </select>

    <insert id="newDocumentGroup" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentGroupDTO">
        INSERT INTO document_group
        (created_by, created_date, updated_by, updated_date, document_group_id,document_group_name,
        document_group_type, document_group_desc, business_code, data_source)
        VALUES (#{createdBy,jdbcType=VARCHAR}, SYSDATE(),#{updatedBy,jdbcType=VARCHAR}, SYSDATE(),
        #{documentGroupId,jdbcType=VARCHAR},#{documentGroupName,jdbcType=VARCHAR},
        #{documentGroupType,jdbcType=VARCHAR},#{documentGroupDesc,jdbcType=VARCHAR},#{businessCode,jdbcType=VARCHAR},#{dataSource,jdbcType=VARCHAR})
    </insert>

    <update id="disableDocument" parameterType="java.lang.String">
        update document
        set document_status = 'N',updated_date=SYSDATE()
        where document_group_items_id in
        <foreach collection="documentGroupItemIds" item="documentGroupItemsId" open="(" separator="," close=")">
            #{documentGroupItemsId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="disableDocumentByUm" parameterType="java.lang.String">
        update document
        set    document_status = 'N',updated_date=sysdate(),UPDATED_BY=#{updatedBy,jdbcType=VARCHAR}
        where  document_group_items_id  in
        <foreach collection="documentGroupItemIds" item="documentGroupItemsId" open="(" separator="," close=")">
            #{documentGroupItemsId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateUploadedDocumentInfos" parameterType="java.lang.String">
        update document
        set    document_type = #{documentType,jdbcType=VARCHAR},updated_date=sysdate()
        where  document_group_items_id  in
        <foreach collection="documentGroupItemIds" item="documentGroupItemsId" open="(" separator="," close=")">
            #{documentGroupItemsId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <select id="selectByDocumentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        DOCUMENT_GROUP_ITEMS_ID, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, DOCUMENT_GROUP_ID,
        DOCUMENT_ID, DOCUMENT_ORDER, DOCUMENT_STATUS, REMARK, DOCUMENT_TYPE, DOCUMENT_NAME,
        ORIGIN_NAME, DOCUMENT_FORMAT, DOCUMENT_DESC, UPLOAD_PERSONNEL, UPLOAD_DATE, UPLOAD_PATH,
        DOCUMENT_CLASS, DOCUMENT_SIZE, DOCUMENT_NO, UPLOAD_IS_DATE, STATUS, UPLOADFLAG, QUEUEID,
        PAGE_COUNT, GENERATED_DATE, LONGITUDE, LATITUDE, DOCUMENT_SOURCE, DOCUMENT_PROPERTY,
        CERTIFICATE_BEGIN_DATE, CERTIFICATE_END_DATE, NETWORK_FLAG
        FROM DOCUMENT
        WHERE DOCUMENT_ID = #{documentId,jdbcType=VARCHAR}
        order by CREATED_DATE desc
        limit  1
    </select>

</mapper>