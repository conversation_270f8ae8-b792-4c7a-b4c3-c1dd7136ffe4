<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.pay.SendPaymentRecordMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord">
    <!--@mbg.generated-->
    <!--@Table clms_send_payment_record-->
    <id column="id" property="id" />
    <result column="report_no" property="reportNo" />
    <result column="case_times" property="caseTimes" />
    <result column="pay_serial_no" property="paySerialNo" />
    <result column="request_type" property="requestType" />
    <result column="request_type_desc" property="requestTypeDesc" />
    <result column="request_param" property="requestParam" />
    <result column="response_param" property="responseParam" />
    <result column="is_success" property="isSuccess" />
    <result column="remark" property="remark" />
    <result column="created_by" property="createdBy" />
    <result column="created_date" property="createdDate" />
    <result column="updated_by" property="updatedBy" />
    <result column="updated_date" property="updatedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_no, case_times, pay_serial_no, request_type, request_type_desc, request_param, 
    response_param, is_success, remark, created_by, created_date, updated_by, updated_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from clms_send_payment_record
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from clms_send_payment_record
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into clms_send_payment_record (report_no, case_times, pay_serial_no, request_type, request_type_desc, 
      request_param, response_param, is_success, remark, created_by, created_date, 
      updated_by, updated_date)
    values (#{reportNo}, #{caseTimes}, #{paySerialNo}, #{requestType}, #{requestTypeDesc}, 
      #{requestParam}, #{responseParam}, #{isSuccess}, #{remark}, #{createdBy}, #{createdDate}, 
      #{updatedBy}, #{updatedDate})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into clms_send_payment_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="caseTimes != null">
        case_times,
      </if>
      <if test="paySerialNo != null">
        pay_serial_no,
      </if>
      <if test="requestType != null">
        request_type,
      </if>
      <if test="requestTypeDesc != null">
        request_type_desc,
      </if>
      <if test="requestParam != null">
        request_param,
      </if>
      <if test="responseParam != null">
        response_param,
      </if>
      <if test="isSuccess != null">
        is_success,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedDate != null">
        updated_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo},
      </if>
      <if test="caseTimes != null">
        #{caseTimes},
      </if>
      <if test="paySerialNo != null">
        #{paySerialNo},
      </if>
      <if test="requestType != null">
        #{requestType},
      </if>
      <if test="requestTypeDesc != null">
        #{requestTypeDesc},
      </if>
      <if test="requestParam != null">
        #{requestParam},
      </if>
      <if test="responseParam != null">
        #{responseParam},
      </if>
      <if test="isSuccess != null">
        #{isSuccess},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="createdBy != null">
        #{createdBy},
      </if>
      <if test="createdDate != null">
        #{createdDate},
      </if>
      <if test="updatedBy != null">
        #{updatedBy},
      </if>
      <if test="updatedDate != null">
        #{updatedDate},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord">
    <!--@mbg.generated-->
    update clms_send_payment_record
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo},
      </if>
      <if test="caseTimes != null">
        case_times = #{caseTimes},
      </if>
      <if test="paySerialNo != null">
        pay_serial_no = #{paySerialNo},
      </if>
      <if test="requestType != null">
        request_type = #{requestType},
      </if>
      <if test="requestTypeDesc != null">
        request_type_desc = #{requestTypeDesc},
      </if>
      <if test="requestParam != null">
        request_param = #{requestParam},
      </if>
      <if test="responseParam != null">
        response_param = #{responseParam},
      </if>
      <if test="isSuccess != null">
        is_success = #{isSuccess},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy},
      </if>
      <if test="updatedDate != null">
        updated_date = #{updatedDate},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.pay.SendPaymentRecord">
    <!--@mbg.generated-->
    update clms_send_payment_record
    set report_no = #{reportNo},
      case_times = #{caseTimes},
      pay_serial_no = #{paySerialNo},
      request_type = #{requestType},
      request_type_desc = #{requestTypeDesc},
      request_param = #{requestParam},
      response_param = #{responseParam},
      is_success = #{isSuccess},
      remark = #{remark},
      created_by = #{createdBy},
      created_date = #{createdDate},
      updated_by = #{updatedBy},
      updated_date = #{updatedDate}
    where id = #{id}
  </update>

  <select id="selectByPaySerialNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from clms_send_payment_record
    where pay_serial_no = #{paySerialNo}
  </select>

  <insert id="insertSendMergePaymentRecord"
          parameterType="com.paic.ncbs.claim.model.dto.pay.SendMergePaymentRecordDTO"
          useGeneratedKeys="true" keyProperty="id">
    INSERT INTO clms_send_merge_payment_record(
    batch_no,
    request_param,
    response_param,
    is_success,
    created_by,
    updated_by,
    sys_ctime,
    sys_utime)
    VALUES(
    #{batchNo,jdbcType=VARCHAR},
    #{requestParam,jdbcType=VARCHAR},
    #{responseParam,jdbcType=VARCHAR},
    #{isSuccess,jdbcType=VARCHAR},
    #{createdBy,jdbcType=VARCHAR},
    #{updatedBy,jdbcType=VARCHAR},
    now(),
    now())
  </insert>
</mapper>