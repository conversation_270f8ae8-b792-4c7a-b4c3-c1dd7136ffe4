<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity">
        <id column="ID_CLM_COMMON_PARAMETER" property="idClmCommonParameter" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="COLLECTION_CODE" property="collectionCode" jdbcType="VARCHAR"/>
        <result column="COLLECTION_NAME" property="collectionName" jdbcType="VARCHAR"/>
        <result column="VALUE_CODE" property="valueCode" jdbcType="VARCHAR"/>
        <result column="VALUE_CHINESE_NAME" property="valueChineseName" jdbcType="VARCHAR"/>
        <result column="VALUE_CHINESE_ABBR_NAME" property="valueChineseAbbrName" jdbcType="VARCHAR"/>
        <result column="VALUE_ENGLISH_NAME" property="valueEnglishName" jdbcType="VARCHAR"/>
        <result column="VALUE_ENGLISH_ABBR_NAME" property="valueEnglishAbbrName" jdbcType="VARCHAR"/>
        <result column="EFFECTIVE_DATE" property="effectiveDate" jdbcType="TIMESTAMP"/>
        <result column="INVALIDATE_DATE" property="invalidateDate" jdbcType="TIMESTAMP"/>
        <result column="DISPLAY_NO" property="displayNo" jdbcType="DECIMAL"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_CLM_COMMON_PARAMETER, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, DEPARTMENT_CODE,
        COLLECTION_CODE, COLLECTION_NAME, VALUE_CODE, VALUE_CHINESE_NAME, VALUE_CHINESE_ABBR_NAME,
        VALUE_ENGLISH_NAME, VALUE_ENGLISH_ABBR_NAME, EFFECTIVE_DATE, INVALIDATE_DATE, DISPLAY_NO,
        REMARK
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_COMMON_PARAMETER
        where ID_CLM_COMMON_PARAMETER = #{idClmCommonParameter,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLM_COMMON_PARAMETER
        where ID_CLM_COMMON_PARAMETER = #{idClmCommonParameter,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity">
        insert into CLM_COMMON_PARAMETER (ID_CLM_COMMON_PARAMETER, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        DEPARTMENT_CODE, COLLECTION_CODE, COLLECTION_NAME,
        VALUE_CODE, VALUE_CHINESE_NAME, VALUE_CHINESE_ABBR_NAME,
        VALUE_ENGLISH_NAME, VALUE_ENGLISH_ABBR_NAME,
        EFFECTIVE_DATE, INVALIDATE_DATE, DISPLAY_NO,
        REMARK)
        values (#{idClmCommonParameter,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{departmentCode,jdbcType=VARCHAR}, #{collectionCode,jdbcType=VARCHAR}, #{collectionName,jdbcType=VARCHAR},
        #{valueCode,jdbcType=VARCHAR}, #{valueChineseName,jdbcType=VARCHAR}, #{valueChineseAbbrName,jdbcType=VARCHAR},
        #{valueEnglishName,jdbcType=VARCHAR}, #{valueEnglishAbbrName,jdbcType=VARCHAR},
        #{effectiveDate,jdbcType=TIMESTAMP}, #{invalidateDate,jdbcType=TIMESTAMP}, #{displayNo,jdbcType=DECIMAL},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity">
        insert into CLM_COMMON_PARAMETER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idClmCommonParameter != null">
                ID_CLM_COMMON_PARAMETER,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE,
            </if>
            <if test="collectionCode != null">
                COLLECTION_CODE,
            </if>
            <if test="collectionName != null">
                COLLECTION_NAME,
            </if>
            <if test="valueCode != null">
                VALUE_CODE,
            </if>
            <if test="valueChineseName != null">
                VALUE_CHINESE_NAME,
            </if>
            <if test="valueChineseAbbrName != null">
                VALUE_CHINESE_ABBR_NAME,
            </if>
            <if test="valueEnglishName != null">
                VALUE_ENGLISH_NAME,
            </if>
            <if test="valueEnglishAbbrName != null">
                VALUE_ENGLISH_ABBR_NAME,
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE,
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE,
            </if>
            <if test="displayNo != null">
                DISPLAY_NO,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idClmCommonParameter != null">
                #{idClmCommonParameter,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentCode != null">
                #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="collectionCode != null">
                #{collectionCode,jdbcType=VARCHAR},
            </if>
            <if test="collectionName != null">
                #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="valueCode != null">
                #{valueCode,jdbcType=VARCHAR},
            </if>
            <if test="valueChineseName != null">
                #{valueChineseName,jdbcType=VARCHAR},
            </if>
            <if test="valueChineseAbbrName != null">
                #{valueChineseAbbrName,jdbcType=VARCHAR},
            </if>
            <if test="valueEnglishName != null">
                #{valueEnglishName,jdbcType=VARCHAR},
            </if>
            <if test="valueEnglishAbbrName != null">
                #{valueEnglishAbbrName,jdbcType=VARCHAR},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="invalidateDate != null">
                #{invalidateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="displayNo != null">
                #{displayNo,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity">
        update CLM_COMMON_PARAMETER
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="collectionCode != null">
                COLLECTION_CODE = #{collectionCode,jdbcType=VARCHAR},
            </if>
            <if test="collectionName != null">
                COLLECTION_NAME = #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="valueCode != null">
                VALUE_CODE = #{valueCode,jdbcType=VARCHAR},
            </if>
            <if test="valueChineseName != null">
                VALUE_CHINESE_NAME = #{valueChineseName,jdbcType=VARCHAR},
            </if>
            <if test="valueChineseAbbrName != null">
                VALUE_CHINESE_ABBR_NAME = #{valueChineseAbbrName,jdbcType=VARCHAR},
            </if>
            <if test="valueEnglishName != null">
                VALUE_ENGLISH_NAME = #{valueEnglishName,jdbcType=VARCHAR},
            </if>
            <if test="valueEnglishAbbrName != null">
                VALUE_ENGLISH_ABBR_NAME = #{valueEnglishAbbrName,jdbcType=VARCHAR},
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE = #{effectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="displayNo != null">
                DISPLAY_NO = #{displayNo,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_CLM_COMMON_PARAMETER = #{idClmCommonParameter,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.other.CommonParameterEntity">
        update CLM_COMMON_PARAMETER
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        COLLECTION_CODE = #{collectionCode,jdbcType=VARCHAR},
        COLLECTION_NAME = #{collectionName,jdbcType=VARCHAR},
        VALUE_CODE = #{valueCode,jdbcType=VARCHAR},
        VALUE_CHINESE_NAME = #{valueChineseName,jdbcType=VARCHAR},
        VALUE_CHINESE_ABBR_NAME = #{valueChineseAbbrName,jdbcType=VARCHAR},
        VALUE_ENGLISH_NAME = #{valueEnglishName,jdbcType=VARCHAR},
        VALUE_ENGLISH_ABBR_NAME = #{valueEnglishAbbrName,jdbcType=VARCHAR},
        EFFECTIVE_DATE = #{effectiveDate,jdbcType=TIMESTAMP},
        INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP},
        DISPLAY_NO = #{displayNo,jdbcType=DECIMAL},
        REMARK = #{remark,jdbcType=VARCHAR}
        where ID_CLM_COMMON_PARAMETER = #{idClmCommonParameter,jdbcType=VARCHAR}
    </update>
    <select id="getCommonParameterByCollectionCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_COMMON_PARAMETER
        where COLLECTION_CODE = #{collectionCode,jdbcType=VARCHAR}
    </select>

    <select id="getClaimMadeProductAmount" parameterType="java.lang.String"
            resultType="java.lang.String">
        SELECT COUNT(1) AMOUNT
        FROM CLM_COMMON_PARAMETER
        where collection_CODE = 'CLAIM_MADE_FORM_PALN'
        AND VALUE_CODE = #{productNo,jdbcType=VARCHAR}
    </select>

    <select id="getValueCodeByCollectionCode" parameterType="java.lang.String" resultType="java.lang.String">
        select VALUE_CODE from CLM_COMMON_PARAMETER
        where COLLECTION_CODE = #{collectionCode,jdbcType=VARCHAR}
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO" id="commonParameter">
        <result property="collectionCode" column="COLLECTION_CODE"/>
        <result property="collectionName" column="COLLECTION_NAME"/>
        <result property="valueCode" column="VALUE_CODE"/>
        <result property="valueChineseName" column="VALUE_CHINESE_NAME"/>
    </resultMap>

    <select id="getCommonParameterList" resultMap="commonParameter" >
        select DEPARTMENT_CODE,COLLECTION_CODE,COLLECTION_NAME,VALUE_CODE,VALUE_CHINESE_NAME,DISPLAY_NO
          FROM CLM_COMMON_PARAMETER WHERE  COLLECTION_CODE in
        <foreach collection="collectionCodeArray" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
        and department_code ='2'
        order by  COLLECTION_CODE,display_no
    </select>

    <select id="getCommonParameterByCode" resultType="string" parameterType="string">
        select VALUE_CODE FROM CLM_COMMON_PARAMETER WHERE  COLLECTION_CODE = #{collectionCode}
    </select>

    <select id="getTotalSwitch" resultType="java.lang.String" >
        select t.value_code from clm_common_parameter t where t.collection_code='AHCS_SRVS_CLS_FLG'
    </select>

    <select id="getNameByCode" resultType="java.lang.String" >
        select VALUE_CHINESE_NAME from clm_common_parameter
        where value_code=#{valueCode}
        and COLLECTION_CODE = #{collectionCode}
        limit 1
    </select>

    <select id="getCommonParameterListByArraryList" resultMap="commonParameter" >
        select DEPARTMENT_CODE,COLLECTION_CODE,COLLECTION_NAME,VALUE_CODE,VALUE_CHINESE_NAME,DISPLAY_NO
        FROM CLM_COMMON_PARAMETER WHERE  COLLECTION_CODE in
        <foreach collection="collectionCodeArray" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
        and department_code ='2'
        order by  COLLECTION_CODE,display_no
    </select>

    <select id="getBankInfoList" resultType="com.paic.ncbs.claim.model.dto.report.BankInfoDTO">
        select distinct bank_name as bankName,bank_code as bankCode from clm_bank_info;
    </select>

    <select id="getBranchBankInfoList" parameterType="string" resultType="com.paic.ncbs.claim.model.dto.report.BankInfoDTO">
        select branch_bank_name as bankName,branch_bank_link_no as bankCode from clm_bank_info
        where bank_name = #{bankName}
    </select>

    <select id="getBankCodeByName" parameterType="string" resultType="java.lang.String">
        select distinct bank_code from clm_bank_info
        where bank_name = #{bankName}
    </select>

    <select id="getBankIfExistByBranchBankName" parameterType="string" resultType="java.lang.String">
        select branch_bank_link_no from clm_bank_info
        where branch_bank_name = #{branchBankName}
    </select>
    <!--查询出险原因下拉数据-->
    <select id="getAccidentReasonList" resultMap="commonParameter" >
        select COLLECTION_CODE,VALUE_CODE ,VALUE_CHINESE_NAME  from clm_common_parameter
        where COLLECTION_CODE in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <!--查询财产损失原因下拉数据-->
    <select id="getPropertyLossReasonList" resultMap="commonParameter" >
        select COLLECTION_CODE,VALUE_CODE ,VALUE_CHINESE_NAME  from clm_common_parameter
        where COLLECTION_CODE in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <!--查询救援类型数据-->
    <select id="getSuccourTypeList" resultMap="commonParameter" >
        select COLLECTION_CODE,VALUE_CODE ,VALUE_CHINESE_NAME  from clm_common_parameter
        where COLLECTION_CODE='SUCCOUR_TYPE'
    </select>
    <select id="getKeyProjectList" resultType="com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO">
        select COLLECTION_CODE,VALUE_CODE ,VALUE_CHINESE_NAME,DEPARTMENT_CODE  from clm_common_parameter
        where COLLECTION_CODE='KEY_PROJECTS'
    </select>

    <select id="getCommonParameter" parameterType="string" resultType="com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO">
        select
        COLLECTION_CODE,VALUE_CODE ,VALUE_CHINESE_NAME,DEPARTMENT_CODE
        from clm_common_parameter
        where 1=1
        <if test="collectionCode != null and collectionCode != ''">
            AND COLLECTION_CODE =  #{collectionCode}
        </if>
        <if test="searchStr != null and searchStr != ''.trim()">
            AND  VALUE_CHINESE_NAME like concat('%',#{searchStr},'%')
        </if>
    </select>

</mapper>