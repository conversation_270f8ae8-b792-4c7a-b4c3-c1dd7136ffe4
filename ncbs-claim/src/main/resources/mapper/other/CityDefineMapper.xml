<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.CityDefineMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.other.CityDefineDTO" id="cityDefineDTO">
		<result column="CITY_CODE" property="cityCode"/>
		<result column="PROVINCE_CODE" property="provinceCode"/>
		<result column="AREA_CODE" property="areaCode"/>
		<result column="POSTCODE" property="postCode"/>
		<result column="CITY_CHINESE_NAME" property="cityChineseName"/>
		<result column="CITY_ENGLISH_NAME" property="cityEnglishName"/>
		<result column="VEHICLE_LICENCE_PREFIX" property="vehicleLicencePrefix"/>
		<result column="CITY_SHORT_NAME" property="cityShortName"/>
		<result column="CITY_SPELL_NAME" property="citySpellName"/>
		<result column="CITY_INITIAL_NAME" property="cityInitialName"/>
	</resultMap>

	<select id="getCityDefineList" parameterType="string" resultMap="cityDefineDTO">
		SELECT CITY_CODE,
		       CITY_CHINESE_NAME,
		       PROVINCE_CODE,
		       POSTCODE,
		       CITY_ENGLISH_NAME,
		       AREA_CODE,
		       VEHICLE_LICENCE_PREFIX,
		       CITY_SHORT_NAME,
		       CITY_SPELL_NAME,
		       CITY_INITIAL_NAME
		  FROM city_define
		  <where>
		  	<if test='isArea=="0"'>
		  		substr(CITY_CODE, -2) = '00'
		  		and CONCAT(SUBSTR(city_code, 1, 2), '0000') = #{cityCode}
		  	</if>
		  	<if test='isArea=="1"'>
		  		substr(CITY_CODE, -2) != '00'
		  		and CONCAT(SUBSTR(city_code, 1, 4), '00') = #{cityCode}
		  	</if>
			  and city_code not in ('310000','110000','120000','500000')
		  </where>
	</select>

	<!-- 根据城市名称，查询城市code -->
	<select id="getCityCodeByCityName" parameterType="string" resultType="string">
		select cd.CITY_CODE
		  from city_define cd
		 where cd.PROVINCE_CODE = #{provinceCode}
		       and cd.CITY_CHINESE_NAME = #{cityName}
		       limit 1
	</select>

	<!-- 根据城市名称，查询城市code -->
	<select id="getCityCodeByCityNameType" parameterType="string" resultType="string">
		select cd.CITY_CODE
		  from city_define cd
		 where cd.PROVINCE_CODE = #{provinceCode}
		       and cd.CITY_CHINESE_NAME = #{cityName}
		       <if test='cityType=="city"'>
		           and substr(CITY_CODE, -2) = '00'
		       </if>
		       <if test='cityType=="county"'>
		           and substr(CITY_CODE, -2) = '01'
		       </if>
		       limit 1
	</select>

	<!-- 根据cityCode查询 cityCode下一级的市或区/县code -->
	<select id="getCityCodeList" parameterType="string" resultType="string">
		select CITY_CODE
		  from city_define
		  <where>
		  	<if test='isArea=="0"'>
		  		substr(CITY_CODE, -2) = '00'
		  		and CONCAT(SUBSTR(city_code, 1, 2), '0000') = #{cityCode}
		  	</if>
		  	<if test='isArea=="1"'>
		  		substr(CITY_CODE, -2) != '00'
		  		and CONCAT(SUBSTR(city_code, 1, 4), '00') = #{cityCode}
		  	</if>
		  </where>
	</select>

    <select id="getCountryByContinent" resultType="com.paic.ncbs.claim.model.vo.other.CountryVO">
		select
				t.country_code countryCode,
				t.country_name countryName
		from
				CLMS_CONTINENT_COUNTRY t
		where  1=1
				<if test="continentCode != null and continentCode != '' ">
					and t.continent_code=#{continentCode}
			  	</if>
	</select>

	  <select id="getContinentByCountry" resultType="com.paic.ncbs.claim.model.vo.other.CountryVO">
		  select
		  		t1.country_code countryCode,
		  		t1.country_name countryName,
		  		t2.value_code continentCode,
		  		t2.value_chinese_name continentName
          from CLMS_continent_country t1,
          	   clm_common_parameter t2
          where t1.country_name like concat('%',#{countryName},'%')
          		and t1.continent_code=t2.value_code
          		and t2.collection_code='AHCS_CONTINENT_CODE'
          	    <![CDATA[	 limit 20 ]]>
	</select>
	<select id="getProvinceByCity" resultType="com.paic.ncbs.claim.model.vo.other.CityDefineVO">
		select
				distinct t1.city_code cityCode,
				t1.city_chinese_name cityChineseName,
				t1.city_initial_name cityInitialName,
				t2.value_code provinceCode,
				t2.value_chinese_name provinceName
		from city_define t1 ,
			 clm_common_parameter t2
		where substr( t1.CITY_CODE, -2) = '00'
			  and (t1.city_initial_name like concat('%',#{cityChineseName},'%')
						 or  t1.city_chinese_name like concat('%',#{cityChineseName},'%')
			  and t1.province_code=t2.value_code and t2.collection_code='PC00'
			 <![CDATA[  limit 20 ]]>
	</select>

	<select id="getCityCodeByProvinceCityName" resultType="java.lang.String">
		select CITY_CODE
		from city_define
		where PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
		and CITY_CHINESE_NAME = #{cityName,jdbcType=VARCHAR}
		and  substr(CITY_CODE, -2) = '00'
		limit 1
	</select>

	<select id="getCountryCodeByProvinceCityName" resultType="java.lang.String">
		select CITY_CODE
		from city_define
		where PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR}
		and CITY_CHINESE_NAME = #{cityName,jdbcType=VARCHAR}
		and  substr(CITY_CODE, -2) != '00'
		limit 1
	</select>

</mapper>