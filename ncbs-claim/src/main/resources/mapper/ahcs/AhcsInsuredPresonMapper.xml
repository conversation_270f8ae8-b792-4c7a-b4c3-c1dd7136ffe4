<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity">
        <id column="ID_AHCS_INSURED_PERSON" property="idAhcsInsuredPerson" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="SEX_CODE" property="sexCode" jdbcType="VARCHAR"/>
        <result column="BIRTHDAY" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="CERTIFICATE_NO" property="certificateNo" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="VARCHAR"/>
        <result column="PROFESSION_CODE" property="professionCode" jdbcType="VARCHAR"/>
        <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR"/>
        <result column="MOBILE_TELEPHONE" property="mobileTelephone" jdbcType="VARCHAR"/>
        <result column="ADDRESS" property="address" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="CLIENT_NO" property="clientNo" jdbcType="VARCHAR"/>
        <result column="SCHEME_NO" property="schemeNo" jdbcType="VARCHAR"/>
        <result column="SCHEME_NAME" property="schemeName" jdbcType="VARCHAR"/>
        <result column="ACCEPT_NO" property="acceptNo" jdbcType="VARCHAR"/>
        <result column="SUB_POLICYNO" property="subPolicyNo" jdbcType="VARCHAR"/>
        <result column="PERSONNEL_CODE" property="personnelCode" jdbcType="VARCHAR"/>
        <result column="RISK_PERSON_NO" property="riskPersonNo" jdbcType="VARCHAR"/>
        <result column="PLY_CERTIFICATE_TYPE" property="plyCertificateType" jdbcType="VARCHAR"/>
        <result column="EFFECTIVE_DATE" property="effectiveDate" jdbcType="TIMESTAMP"/>
        <result column="INVALIDATE_DATE" property="invalidateDate" jdbcType="TIMESTAMP"/>
        <result column="IS_SOCIA_SECURITY" property="isSociaSecurity" jdbcType="VARCHAR"/>
        <result column="PERSONNEL_NATURE" property="personnelNature" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_INSURED_PERSON, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, SEX_CODE, BIRTHDAY, CERTIFICATE_NO, CERTIFICATE_TYPE, PROFESSION_CODE, TELEPHONE,
        MOBILE_TELEPHONE, ADDRESS, EMAIL, CLIENT_NO, SCHEME_NO, SCHEME_NAME, ACCEPT_NO, SUB_POLICYNO,
        PERSONNEL_CODE, RISK_PERSON_NO, PLY_CERTIFICATE_TYPE, EFFECTIVE_DATE, INVALIDATE_DATE, IS_SOCIA_SECURITY, PERSONNEL_NATURE
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON
        where ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_INSURED_PERSON
        where ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByIdAhcsPolicyInfo" parameterType="java.lang.String">
        delete from CLMS_INSURED_PERSON
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity">
        insert into CLMS_INSURED_PERSON (ID_AHCS_INSURED_PERSON, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, SEX_CODE, BIRTHDAY,
        CERTIFICATE_NO, CERTIFICATE_TYPE, PROFESSION_CODE,
        TELEPHONE, MOBILE_TELEPHONE, ADDRESS,
        EMAIL, CLIENT_NO, SCHEME_NO,
        SCHEME_NAME, ACCEPT_NO, SUB_POLICYNO, PERSONNEL_CODE, RISK_PERSON_NO, PLY_CERTIFICATE_TYPE, EFFECTIVE_DATE,
        INVALIDATE_DATE, IS_SOCIA_SECURITY, RELATIONSHIP_WITH_APPLICANT, PERSONNEL_NATURE)
        values (#{idAhcsInsuredPerson,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR}, #{sexCode,jdbcType=VARCHAR}, #{birthday,jdbcType=TIMESTAMP},
        #{certificateNo,jdbcType=VARCHAR}, #{certificateType,jdbcType=VARCHAR}, #{professionCode,jdbcType=VARCHAR},
        #{telephone,jdbcType=VARCHAR}, #{mobileTelephone,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
        #{email,jdbcType=VARCHAR}, #{clientNo,jdbcType=VARCHAR}, #{schemeNo,jdbcType=VARCHAR},
        #{schemeName,jdbcType=VARCHAR}, #{acceptNo,jdbcType=VARCHAR}, #{subPolicyNo,jdbcType=VARCHAR},
        #{personnelCode,jdbcType=VARCHAR}, #{riskPersonNo,jdbcType=VARCHAR}, #{plyCertificateType,jdbcType=VARCHAR},
        #{effectiveDate,jdbcType=TIMESTAMP}, #{invalidateDate,jdbcType=TIMESTAMP}, #{isSociaSecurity,jdbcType=VARCHAR},
        #{relationshipWithApplicant,jdbcType=VARCHAR}, #{personnelNature,jdbcType=INTEGER})
    </insert>
    
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_INSURED_PERSON (ID_AHCS_INSURED_PERSON, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, ID_AHCS_POLICY_INFO,
        NAME, SEX_CODE, BIRTHDAY,
        CERTIFICATE_NO, CERTIFICATE_TYPE, PROFESSION_CODE,
        TELEPHONE, MOBILE_TELEPHONE, ADDRESS,
        EMAIL, CLIENT_NO, SCHEME_NO,
        SCHEME_NAME, ACCEPT_NO, SUB_POLICYNO, PERSONNEL_CODE,
        RISK_PERSON_NO, PLY_CERTIFICATE_TYPE, EFFECTIVE_DATE, INVALIDATE_DATE, IS_SOCIA_SECURITY, PERSONNEL_NATURE)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsInsuredPerson,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idAhcsPolicyInfo,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR}, #{item.sexCode,jdbcType=VARCHAR},
            #{item.birthday,jdbcType=TIMESTAMP},
            #{item.certificateNo,jdbcType=VARCHAR}, #{item.certificateType,jdbcType=VARCHAR},
            #{item.professionCode,jdbcType=VARCHAR},
            #{item.telephone,jdbcType=VARCHAR}, #{item.mobileTelephone,jdbcType=VARCHAR},
            #{item.address,jdbcType=VARCHAR},
            #{item.email,jdbcType=VARCHAR}, #{item.clientNo,jdbcType=VARCHAR},
            #{item.schemeNo,jdbcType=VARCHAR},
            #{item.schemeName,jdbcType=VARCHAR}, #{item.acceptNo,jdbcType=VARCHAR},
            #{item.subPolicyNo,jdbcType=VARCHAR},
            #{item.personnelCode,jdbcType=VARCHAR}, #{item.riskPersonNo,jdbcType=VARCHAR},
            #{item.plyCertificateType,jdbcType=VARCHAR},
            #{item.effectiveDate,jdbcType=TIMESTAMP}, #{item.invalidateDate,jdbcType=TIMESTAMP},
            #{item.isSociaSecurity,jdbcType=VARCHAR},
            #{item.personnelNature,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity">
        insert into CLMS_INSURED_PERSON
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsInsuredPerson != null">
                ID_AHCS_INSURED_PERSON,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="sexCode != null">
                SEX_CODE,
            </if>
            <if test="birthday != null">
                BIRTHDAY,
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO,
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE,
            </if>
            <if test="professionCode != null">
                PROFESSION_CODE,
            </if>
            <if test="telephone != null">
                TELEPHONE,
            </if>
            <if test="mobileTelephone != null">
                MOBILE_TELEPHONE,
            </if>
            <if test="address != null">
                ADDRESS,
            </if>
            <if test="email != null">
                EMAIL,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="schemeNo != null">
                SCHEME_NO,
            </if>
            <if test="schemeName != null">
                SCHEME_NAME,
            </if>
            <if test="acceptNo != null">
                ACCEPT_NO,
            </if>
            <if test="subPolicyNo != null">
                SUB_POLICYNO,
            </if>
            <if test="personnelCode != null">
                PERSONNEL_CODE,
            </if>
            <if test="riskPersonNo != null">
                RISK_PERSON_NO,
            </if>
            <if test="plyCertificateType != null">
                PLY_CERTIFICATE_TYPE,
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE,
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE,
            </if>
            <if test="isSociaSecurity != null">
                IS_SOCIA_SECURITY,
            </if>
            <if test="personnelNature != null">
                PERSONNEL_NATURE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsInsuredPerson != null">
                #{idAhcsInsuredPerson,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sexCode != null">
                #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="certificateNo != null">
                #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="professionCode != null">
                #{professionCode,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="mobileTelephone != null">
                #{mobileTelephone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="schemeNo != null">
                #{schemeNo,jdbcType=VARCHAR},
            </if>
            <if test="schemeName != null">
                #{schemeName,jdbcType=VARCHAR},
            </if>
            <if test="acceptNo != null">
                #{acceptNo,jdbcType=VARCHAR},
            </if>
            <if test="subPolicyNo != null">
                #{subPolicyNo,jdbcType=VARCHAR},
            </if>
            <if test="personnelCode != null">
                #{personnelCode,jdbcType=VARCHAR},
            </if>
            <if test="rsikPersonNo != null">
                #{rsikPersonNo,jdbcType=VARCHAR},
            </if>
            <if test="plyCertificateType != null">
                #{plyCertificateType,jdbcType=VARCHAR},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="invalidateDate != null">
                #{invalidateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="isSociaSecurity != null">
                #{isSociaSecurity,jdbcType=TIMESTAMP},
            </if>
            <if test="personnelNature != null">
                #{personnelNature,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity">
        update CLMS_INSURED_PERSON
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sexCode != null">
                SEX_CODE = #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="certificateNo != null">
                CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="professionCode != null">
                PROFESSION_CODE = #{professionCode,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="mobileTelephone != null">
                MOBILE_TELEPHONE = #{mobileTelephone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                EMAIL = #{email,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                CLIENT_NO = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="schemeNo != null">
                SCHEME_NO = #{schemeNo,jdbcType=VARCHAR},
            </if>
            <if test="schemeName != null">
                SCHEME_NAME = #{schemeName,jdbcType=VARCHAR},
            </if>
            <if test="acceptNo != null">
                ACCEPT_NO = #{acceptNo,jdbcType=VARCHAR},
            </if>
            <if test="subPolicyNo != null">
                SUB_POLICYNO = #{subPolicyNo,jdbcType=VARCHAR},
            </if>
            <if test="personnelCode != null">
                PERSONNEL_CODE = #{personnelCode,jdbcType=VARCHAR},
            </if>
            <if test="riskPersonNo != null">
                RISK_PERSON_NO = #{riskPersonNo,jdbcType=VARCHAR},
            </if>
            <if test="plyCertificateType != null">
                PLY_CERTIFICATE_TYPE = #{plyCertificateType,jdbcType=VARCHAR},
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE = #{effectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="isSociaSecurity != null">
                IS_SOCIA_SECURITY = #{isSociaSecurity,jdbcType=TIMESTAMP},
            </if>
            <if test="personnelNature != null">
                PERSONNEL_NATURE = #{personnelNature,jdbcType=INTEGER},
            </if>
        </set>
        where ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity">
        update CLMS_INSURED_PERSON
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR},
        NAME = #{name,jdbcType=VARCHAR},
        SEX_CODE = #{sexCode,jdbcType=VARCHAR},
        BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
        CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR},
        CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR},
        PROFESSION_CODE = #{professionCode,jdbcType=VARCHAR},
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
        MOBILE_TELEPHONE = #{mobileTelephone,jdbcType=VARCHAR},
        ADDRESS = #{address,jdbcType=VARCHAR},
        EMAIL = #{email,jdbcType=VARCHAR},
        CLIENT_NO = #{clientNo,jdbcType=VARCHAR},
        SCHEME_NO = #{schemeNo,jdbcType=VARCHAR},
        SCHEME_NAME = #{schemeName,jdbcType=VARCHAR},
        ACCEPT_NO = #{acceptNo,jdbcType=VARCHAR},
        SUB_POLICYNO = #{subPolicyNo,jdbcType=VARCHAR},
        PERSONNEL_CODE = #{personnelCode,jdbcType=VARCHAR},
        RISK_PERSON_NO = #{riskPersonNo,jdbcType=VARCHAR},
        PLY_CERTIFICATE_TYPE = #{plyCertificateType,jdbcType=VARCHAR},
        EFFECTIVE_DATE = #{effectiveDate,jdbcType=TIMESTAMP},
        INVALIDATE_DATE = #{invalidateDate,jdbcType=TIMESTAMP},
        IS_SOCIA_SECURITY = #{isSociaSecurity,jdbcType=TIMESTAMP},
        PERSONNEL_NATURE = #{personnelNature,jdbcType=INTEGER}
        where ID_AHCS_INSURED_PERSON = #{idAhcsInsuredPerson,jdbcType=VARCHAR}
    </update>

    <select id="getAhcsInsuredPersionByPolicyId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <select id="getAhcsInsuredPersionByClientNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON
        where CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        order by CREATED_DATE desc
        limit 1
    </select>

    <select id="getAhcsInsuredPersionByClientNoAndReportNo" parameterType="java.lang.String" resultType="java.lang.String">
        select
        ip.IS_SOCIA_SECURITY
        from CLMS_INSURED_PERSON ip join clms_policy_info pi on ip.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
        where ip.CLIENT_NO = #{clientNo,jdbcType=VARCHAR} and pi.report_no = #{reportNo,jdbcType=VARCHAR}
        order by ip.CREATED_DATE desc
    </select>

    <select id="getAhcsInsuredPersionById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_INSURED_PERSON a
        where a.id_ahcs_policy_info in
        (select b.id_ahcs_policy_info
        from CLMS_Policy_Info b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR})
    </delete>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_INSURED_PERSON
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>

    <select id="getAhcsInsuredPersionNatureByClientNoAndReportNo" parameterType="java.lang.String" resultType="Integer">
        select
        ip.PERSONNEL_NATURE
        from CLMS_INSURED_PERSON ip join clms_policy_info pi on ip.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
        where  pi.report_no = #{reportNo,jdbcType=VARCHAR}
        <if test="clientNo != null">
            and ip.CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        order by ip.CREATED_DATE desc
    </select>

    <select id="getRelToInsuredByReportNo" parameterType="java.lang.String" resultType="String">
        select
        ip.RELATIONSHIP_WITH_APPLICANT
        from CLMS_INSURED_PERSON ip join clms_policy_info pi on ip.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
        where  pi.report_no = #{reportNo,jdbcType=VARCHAR}
        order by ip.CREATED_DATE desc
        limit 1
    </select>
    <select id="getInsuredPersionByClientNoAndReportNo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.ahcs.AhcsInsuredPresonVO">
        select
        ip.ID_AHCS_INSURED_PERSON, ip.CREATED_BY, ip.CREATED_DATE, ip.UPDATED_BY, ip.UPDATED_DATE, ip.ID_AHCS_POLICY_INFO,
        ip.NAME, SEX_CODE, ip.BIRTHDAY, ip.CERTIFICATE_NO, ip.CERTIFICATE_TYPE, ip.PROFESSION_CODE as PROFESSION_CODE, ip.TELEPHONE,
        ip.MOBILE_TELEPHONE, ip.ADDRESS, ip.EMAIL, ip.CLIENT_NO, ip.SCHEME_NO, ip.SCHEME_NAME, ip.ACCEPT_NO, ip.SUB_POLICYNO,
        ip.PERSONNEL_CODE, ip.RISK_PERSON_NO, ip.PLY_CERTIFICATE_TYPE, ip.EFFECTIVE_DATE, ip.INVALIDATE_DATE, ip.IS_SOCIA_SECURITY, ip.PERSONNEL_NATURE
        ,pi.BUSINESS_TYPE
        from CLMS_INSURED_PERSON ip, clms_policy_info pi
        where ip.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO and pi.ID_AHCS_POLICY_INFO =ip.ID_AHCS_POLICY_INFO and pi.report_no = #{reportNo,jdbcType=VARCHAR}
        order by ip.CREATED_DATE desc
    </select>
</mapper>