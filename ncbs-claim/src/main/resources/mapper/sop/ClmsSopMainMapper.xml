<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopMainMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        <id column="id_sop_main" property="idSopMain" />
        <result column="sop_name" property="sopName" />
        <result column="version_no" property="versionNo" />
        <result column="sop_description" property="sopDescription" />
        <result column="is_all_process" property="isAllProcess" />
        <result column="batch_no" property="batchNo" />
        <result column="sop_content" property="sopContent" />
        <result column="publisher_code" property="publisherCode" />
        <result column="publisher_name" property="publisherName" />
        <result column="publish_time" property="publishTime" />
        <result column="valid_flag" property="validFlag" />
        <result column="effective_date" property="effectiveDate" />
        <result column="invalid_date" property="invalidDate" />
        <result column="status" property="status" />
        <result column="file_type" property="fileType" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_main, sop_name, version_no, sop_description, batch_no,
        is_all_process, sop_content, publisher_code, publisher_name, 
        publish_time, valid_flag, effective_date, invalid_date, status, file_type, remark, 
        created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectSopList" resultType="com.paic.ncbs.claim.model.vo.sop.SopMainVO">
        SELECT DISTINCT
            sm.id_sop_main AS idSopMain,
            sm.sop_name AS sopName,
            sm.version_no AS versionNo,
            sm.publisher_code AS publisherCode,
            sm.publisher_name AS publisherName,
            sm.status AS status,
            sm.file_type AS fileType,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate,
            sm.publish_time AS publishTime,
            sm.sys_ctime AS sysCtime,
            sm.sys_utime AS sysUtime,
            sm.batch_no AS batchNo,
            CASE 
                WHEN temp_check.has_temp IS NOT NULL THEN 'Y' 
                ELSE 'N' 
            END AS hasTempData

        FROM clms_sop_main sm
        LEFT JOIN clms_sop_config sc ON sm.id_sop_main = sc.id_sop_main
        LEFT JOIN clms_sop_file sf ON sm.id_sop_main = sf.id_sop_main
        LEFT JOIN (
            SELECT 
                batch_no,
                'Y' as has_temp
            FROM clms_sop_main
            WHERE status = '01'
            GROUP BY batch_no
        ) temp_check ON sm.batch_no = temp_check.batch_no
        WHERE 1=1
        <if test="queryVO.sopName != null and queryVO.sopName != ''">
            AND sm.sop_name LIKE CONCAT('%', #{queryVO.sopName}, '%')
        </if>
        <if test="queryVO.publisherCode != null and queryVO.publisherCode != ''">
            AND sm.publisher_code = #{queryVO.publisherCode}
        </if>
        <if test="queryVO.productCode != null and queryVO.productCode != ''">
            AND (sc.product_code = #{queryVO.productCode})
        </if>
        <if test="queryVO.groupCode != null and queryVO.groupCode != ''">
            AND (sc.group_code = #{queryVO.groupCode})
        </if>
        <if test="queryVO.planCode != null and queryVO.planCode != ''">
            AND (sc.plan_code = #{queryVO.planCode})
        </if>
        <if test="queryVO.taskBpmKey != null and queryVO.taskBpmKey != ''">
            AND (sc.task_bpm_key = #{queryVO.taskBpmKey})
        </if>
        <if test="queryVO.fileType != null and queryVO.fileType != ''">
            AND sm.file_type = #{queryVO.fileType}
        </if>
        <if test="queryVO.status != null and queryVO.status != ''">
            AND sm.status = #{queryVO.status}
        </if>
        <if test="queryVO.effectiveDateStart != null and queryVO.effectiveDateStart != ''">
            AND sm.effective_date >= #{queryVO.effectiveDateStart}
        </if>
        <if test="queryVO.effectiveDateEnd != null and queryVO.effectiveDateEnd != ''">
            AND sm.effective_date &lt;= #{queryVO.effectiveDateEnd}
        </if>
        <if test="queryVO.invalidDateStart != null and queryVO.invalidDateStart != ''">
            AND sm.invalid_date >= #{queryVO.invalidDateStart}
        </if>
        <if test="queryVO.invalidDateEnd != null and queryVO.invalidDateEnd != ''">
            AND sm.invalid_date &lt;= #{queryVO.invalidDateEnd}
        </if>
        GROUP BY sm.id_sop_main, sm.sop_name, sm.version_no, sm.publisher_code, sm.publisher_name,
                 sm.status, sm.file_type, sm.effective_date, sm.invalid_date,
                 sm.publish_time, sm.sys_ctime, sm.sys_utime, sm.batch_no, temp_check.has_temp
        ORDER BY sm.sys_ctime DESC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_main
        WHERE id_sop_main = #{idSopMain}
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        INSERT INTO clms_sop_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopMain}, #{sopName}, #{versionNo}, #{sopDescription}, #{batchNo},
            #{isAllProcess}, #{sopContent}, #{publisherCode}, #{publisherName}, 
            #{publishTime}, #{validFlag}, #{effectiveDate}, #{invalidDate}, #{status}, #{fileType}, 
            #{remark}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        INSERT INTO clms_sop_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="sopName != null">sop_name,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="sopDescription != null">sop_description,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="isAllProcess != null">is_all_process,</if>
            <if test="sopContent != null">sop_content,</if>
            <if test="publisherCode != null">publisher_code,</if>
            <if test="publisherName != null">publisher_name,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="invalidDate != null">invalid_date,</if>
            <if test="status != null">status,</if>
            <if test="fileType != null">file_type,</if>
            <if test="remark != null">remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="sopName != null">#{sopName},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="sopDescription != null">#{sopDescription},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="isAllProcess != null">#{isAllProcess},</if>
            <if test="sopContent != null">#{sopContent},</if>
            <if test="publisherCode != null">#{publisherCode},</if>
            <if test="publisherName != null">#{publisherName},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="invalidDate != null">#{invalidDate},</if>
            <if test="status != null">#{status},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        UPDATE clms_sop_main
        <set>
            <if test="sopName != null">sop_name = #{sopName},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="sopDescription != null">sop_description = #{sopDescription},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="isAllProcess != null">is_all_process = #{isAllProcess},</if>
            <if test="sopContent != null">sop_content = #{sopContent},</if>
            <if test="publisherCode != null">publisher_code = #{publisherCode},</if>
            <if test="publisherName != null">publisher_name = #{publisherName},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="invalidDate != null">invalid_date = #{invalidDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_sop_main = #{idSopMain}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        UPDATE clms_sop_main
        SET sop_name = #{sopName},
            version_no = #{versionNo},
            sop_description = #{sopDescription},
            batch_no = #{batchNo},
            is_all_process = #{isAllProcess},
            sop_content = #{sopContent},
            publisher_code = #{publisherCode},
            publisher_name = #{publisherName},
            publish_time = #{publishTime},
            valid_flag = #{validFlag},
            effective_date = #{effectiveDate},
            invalid_date = #{invalidDate},
            status = #{status},
            file_type = #{fileType},
            remark = #{remark},
            updated_by = #{updatedBy},
            sys_utime = #{sysUtime}
        WHERE id_sop_main = #{idSopMain}
    </update>

    <select id="selectVersionsBySopName" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
        ORDER BY version_no ASC
    </select>
  
    <select id="selectSopDetailById" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.sop.SopMainVO">
        SELECT
            sm.id_sop_main AS idSopMain,
            sm.sop_name AS sopName,
            sm.batch_no AS batchNo,
            sm.version_no AS versionNo,
            sm.sop_description AS sopDescription,
            sm.is_all_process AS isAllProcess,
            sm.sop_content AS sopContent,
            sm.publisher_code AS publisherCode,
            sm.publisher_name AS publisherName,
            sm.publish_time AS publishTime,
            sm.valid_flag AS validFlag,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate,
            sm.status AS status,
            sm.file_type AS fileType,
            sm.remark AS remark,
            sm.created_by AS createdBy,
            sm.sys_ctime AS sysCtime,
            sm.updated_by AS updatedBy,
            sm.sys_utime AS sysUtime,
            GROUP_CONCAT(DISTINCT sc.task_bpm_key ORDER BY sc.task_bpm_key SEPARATOR ',') AS taskBpmKey,
            GROUP_CONCAT(DISTINCT CASE WHEN sc.product_code != 'PUB' THEN sc.product_code END ORDER BY sc.product_code SEPARATOR ',') AS productCode,
            GROUP_CONCAT(DISTINCT CASE WHEN sc.group_code != 'PUB' THEN sc.group_code END ORDER BY sc.group_code SEPARATOR ',') AS groupCode,
            GROUP_CONCAT(DISTINCT CASE WHEN sc.plan_code != 'PUB' THEN sc.plan_code END ORDER BY sc.plan_code SEPARATOR ',') AS planCode
        FROM clms_sop_main sm
        LEFT JOIN clms_sop_config sc ON sm.id_sop_main = sc.id_sop_main
        WHERE sm.id_sop_main = #{idSopMain}
        GROUP BY sm.id_sop_main, sm.sop_name,sm.batch_no, sm.version_no, sm.sop_description, sm.is_all_process,
                 sm.sop_content, sm.publisher_code, sm.publisher_name,
                 sm.publish_time, sm.valid_flag, sm.effective_date, sm.invalid_date, sm.status, sm.file_type,
                 sm.remark, sm.created_by, sm.sys_ctime, sm.updated_by, sm.sys_utime
    </select>

    <select id="selectVersionListByBatchNo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.sop.SopHistoryMainVO">
        SELECT
            sm.id_sop_main AS idSopMain,
            sm.version_no AS versionNo,
            sm.sop_name AS sopName,
            sm.publisher_name AS publisherName,
            sm.status AS status,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate,
            sm.sys_ctime AS sysCtime
        FROM clms_sop_main sm
        WHERE sm.batch_no = #{batchNo}
        ORDER BY sm.version_no ASC
    </select>

    <select id="countBySopName" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
        <if test="excludeId != null and excludeId != ''">
            AND id_sop_main != #{excludeId}
        </if>
    </select>

    <select id="selectBySopName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
        ORDER BY sys_ctime DESC
        LIMIT 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM clms_sop_main
        WHERE id_sop_main = #{idSopMain}
    </delete>

    <select id="selectLatestByBatchNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM clms_sop_main
        WHERE batch_no = #{batchNo}
        ORDER BY sys_ctime DESC
        LIMIT 1
    </select>

    <update id="updateInvalidDateByBatchNo">
        UPDATE clms_sop_main
        SET invalid_date = #{invalidDate},
            updated_by = #{updatedBy},
            sys_utime = #{invalidDate},
            status = '03'
        WHERE batch_no = #{batchNo}
          AND id_sop_main != #{excludeId}
          AND status != '03'
    </update>

    <select id="selectPlanInfoList" resultType="java.util.Map">
        SELECT DISTINCT
            PLAN_CODE as planCode,
            PLAN_CHINESE_NAME as planChineseName
        FROM plan_info
        WHERE status = '1'
        ORDER BY PLAN_CODE
    </select>

    <select id="selectMatchingSopRulesByCase" resultType="com.paic.ncbs.claim.model.vo.sop.SopMainVO">
        SELECT DISTINCT
            sm.id_sop_main AS idSopMain,
            sm.sop_name AS sopName,
            sm.version_no AS versionNo,
            sm.sop_description AS sopDescription,
            sm.sop_content AS sopContent,
            sm.status AS status,
            sm.file_type AS fileType,
            sm.publisher_name AS publisherName,
            sm.publish_time AS publishTime,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate
        FROM clms_sop_main sm
        INNER JOIN clms_sop_config sc ON sm.id_sop_main = sc.id_sop_main
        INNER JOIN (
            SELECT
                prg.COMBINED_PRODUCT_CODE as product_code,
                prg.PRODUCT_PACKAGE_TYPE as group_code,
                plan.plan_code as plan_code
            FROM
                clm_case_base cpi
            LEFT JOIN
                ply_risk_group prg ON cpi.policy_no = prg.POLICY_NO
            LEFT JOIN
                clms_estimate_policy y ON cpi.report_no = y.report_no AND cpi.case_times = y.case_times
            LEFT JOIN
                clms_estimate_plan plan ON y.ID_AHCS_ESTIMATE_POLICY = plan.ID_AHCS_ESTIMATE_POLICY
            WHERE
                cpi.report_no = #{reportNo}
                AND cpi.case_times = #{caseTimes}
        ) case_info ON (
            (sc.product_code = case_info.product_code OR sc.product_code = 'PUB') AND
            (sc.group_code = case_info.group_code OR sc.group_code = 'PUB') AND
            (sc.plan_code = case_info.plan_code OR sc.plan_code = 'PUB')  AND
            (sc.task_bpm_key  = #{taskBpmKey} OR sc.task_bpm_key = 'PUB')
        )
        WHERE sm.status = '02'
          AND sm.valid_flag = 'Y'
          AND sm.effective_date &lt;= NOW()
          AND (sm.invalid_date IS NULL OR sm.invalid_date &gt; NOW())
        ORDER BY sm.sop_name ASC, sm.version_no desc
    </select>

    <select id="selectProductGroupAssociations" resultType="java.util.Map">
        SELECT DISTINCT
            pa.package_code as 'groupCode',
            m.marketproduct_code as 'productCode'
        FROM package_info pa
        LEFT JOIN marketproduct_info m ON m.id_marketproduct_info = pa.id_marketproduct_info
        WHERE pa.status = '1' AND m.status = '1'
        ORDER BY m.marketproduct_code, pa.package_code
    </select>

    <select id="selectProductInfoByProductCodes" resultType="java.util.Map">
        SELECT DISTINCT
            m.marketproduct_code as 'productCode',
            m.marketproduct_name as 'productName'
        FROM marketproduct_info m
        WHERE m.status = '1'
        <if test="productCodes != null and productCodes.size() > 0">
            AND m.marketproduct_code IN
            <foreach collection="productCodes" item="productCode" open="(" separator="," close=")">
                #{productCode}
            </foreach>
        </if>
        ORDER BY m.marketproduct_code
    </select>

    <select id="selectGroupInfoByGroupCodes" resultType="java.util.Map">
        SELECT
            pa.package_code as 'groupCode',
            pa.PACKAGE_NAME as 'groupName'
        FROM package_info pa
        LEFT JOIN marketproduct_info m ON m.id_marketproduct_info = pa.id_marketproduct_info
        WHERE pa.status = '1' AND m.status = '1'
        <if test="groupCodes != null and groupCodes.size() > 0">
            AND pa.package_code IN
            <foreach collection="groupCodes" item="groupCode" open="(" separator="," close=")">
                #{groupCode}
            </foreach>
        </if>
        ORDER BY pa.package_code
    </select>

    <select id="selectPlanInfoByPlanCodes" resultType="java.util.Map">
        SELECT DISTINCT
            pi.plan_code as 'planCode',
            pi.plan_chinese_name as 'planChineseName'
        FROM plan_info pi
        WHERE pi.status = '1'
        <if test="planCodes != null and planCodes.size() > 0">
            AND pi.plan_code IN
            <foreach collection="planCodes" item="planCode" open="(" separator="," close=")">
                #{planCode}
            </foreach>
        </if>
        ORDER BY pi.plan_code
    </select>

</mapper>
