<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopConfigMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig">
        <id column="id_sop_config" property="idSopConfig" />
        <result column="id_sop_main" property="idSopMain" />
        <result column="product_code" property="productCode" />
        <result column="group_code" property="groupCode" />
        <result column="plan_code" property="planCode" />
        <result column="task_bpm_key" property="taskBpmKey" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_config, id_sop_main, product_code, group_code, plan_code, task_bpm_key, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByIdSopMain" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_config
        WHERE id_sop_main = #{idSopMain}
        ORDER BY sys_ctime ASC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_config
        WHERE id_sop_config = #{idSopConfig}
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig">
        INSERT INTO clms_sop_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopConfig}, #{idSopMain}, #{productCode}, #{groupCode}, #{planCode}, #{taskBpmKey}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig">
        INSERT INTO clms_sop_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopConfig != null">id_sop_config,</if>
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="productCode != null">product_code,</if>
            <if test="groupCode != null">group_code,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="taskBpmKey != null">task_bpm_key,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopConfig != null">#{idSopConfig},</if>
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="productCode != null">#{productCode},</if>
            <if test="groupCode != null">#{groupCode},</if>
            <if test="planCode != null">#{planCode},</if>
            <if test="taskBpmKey != null">#{taskBpmKey},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig">
        UPDATE clms_sop_config
        <set>
            <if test="idSopMain != null">id_sop_main = #{idSopMain},</if>
            <if test="productCode != null">product_code = #{productCode},</if>
            <if test="groupCode != null">group_code = #{groupCode},</if>
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="taskBpmKey != null">task_bpm_key = #{taskBpmKey},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_sop_config = #{idSopConfig}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig">
        UPDATE clms_sop_config
        SET id_sop_main = #{idSopMain},
            product_code = #{productCode},
            group_code = #{groupCode},
            plan_code = #{planCode},
            task_bpm_key = #{taskBpmKey},
            updated_by = #{updatedBy},
            sys_utime = #{sysUtime}
        WHERE id_sop_config = #{idSopConfig}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM clms_sop_config
        WHERE id_sop_config = #{idSopConfig}
    </delete>

    <delete id="deleteByIdSopMain" parameterType="java.lang.String">
        DELETE FROM clms_sop_config
        WHERE id_sop_main = #{idSopMain}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO clms_sop_config
        (id_sop_config, id_sop_main, product_code, group_code, plan_code, task_bpm_key, created_by, sys_ctime, updated_by, sys_utime)
        VALUES
        <foreach collection="configList" item="item" separator=",">
            (#{item.idSopConfig}, #{item.idSopMain}, #{item.productCode}, #{item.groupCode}, #{item.planCode}, #{item.taskBpmKey}, #{item.createdBy}, #{item.sysCtime}, #{item.updatedBy}, #{item.sysUtime})
        </foreach>
    </insert>

    <select id="selectAggregatedConfigByIdSopMain" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            GROUP_CONCAT(DISTINCT CASE WHEN task_bpm_key != 'PUB' THEN task_bpm_key END ORDER BY task_bpm_key SEPARATOR ',') AS taskBmpKey,
            GROUP_CONCAT(DISTINCT CASE WHEN product_code != 'PUB' THEN product_code END ORDER BY product_code SEPARATOR ',') AS productCode,
            GROUP_CONCAT(DISTINCT CASE WHEN group_code != 'PUB' THEN group_code END ORDER BY group_code SEPARATOR ',') AS groupCode,
            GROUP_CONCAT(DISTINCT CASE WHEN plan_code != 'PUB' THEN plan_code END ORDER BY plan_code SEPARATOR ',') AS planCode
        FROM clms_sop_config
        WHERE id_sop_main = #{idSopMain}
    </select>

</mapper>