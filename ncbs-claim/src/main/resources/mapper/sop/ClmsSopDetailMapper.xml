<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopDetailMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail">
        <id column="id_sop_detail" property="idSopDetail" />
        <result column="id_sop_main" property="idSopMain" />
        <result column="task_bpm_keys" property="taskBpmKeys" />
        <result column="file_ids" property="fileIds" />
        <result column="sop_content" property="sopContent" />
        <result column="sort_order" property="sortOrder" />
        <result column="valid_flag" property="validFlag" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_detail, id_sop_main, task_bpm_keys, file_ids, sop_content,
        sort_order, valid_flag, remark, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_detail
        WHERE id_sop_detail = #{idSopDetail}
    </select>

    <select id="selectByIdSopMain" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_detail
        WHERE id_sop_main = #{idSopMain}
        AND valid_flag = 'Y'
        ORDER BY sort_order ASC
    </select>

    <select id="selectByIdSopMainAndtaskBpmKey" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_detail
        WHERE id_sop_main = #{idSopMain}
        AND valid_flag = 'Y'
        AND (FIND_IN_SET(#{taskBpmKey}, task_bpm_keys) > 0 OR task_bpm_keys = 'PUB')
        ORDER BY sort_order ASC
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail">
        INSERT INTO clms_sop_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopDetail}, #{idSopMain}, #{taskBpmKeys}, #{fileIds}, #{sopContent},
            #{sortOrder}, #{validFlag}, #{remark}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail">
        INSERT INTO clms_sop_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopDetail != null">id_sop_detail,</if>
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="taskBpmKeys != null">task_bpm_keys,</if>
            <if test="fileIds != null">file_ids,</if>
            <if test="sopContent != null">sop_content,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopDetail != null">#{idSopDetail},</if>
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="taskBpmKeys != null">#{taskBpmKeys},</if>
            <if test="fileIds != null">#{fileIds},</if>
            <if test="sopContent != null">#{sopContent},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO clms_sop_detail
        (id_sop_detail, id_sop_main, task_bpm_keys, file_ids, sop_content, 
         sort_order, valid_flag, remark, created_by, sys_ctime, updated_by, sys_utime)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.idSopDetail}, #{item.idSopMain}, #{item.taskBpmKeys}, #{item.fileIds}, #{item.sopContent},
             #{item.sortOrder}, #{item.validFlag}, #{item.remark}, #{item.createdBy}, #{item.sysCtime},
             #{item.updatedBy}, #{item.sysUtime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail">
        UPDATE clms_sop_detail
        <set>
            <if test="idSopMain != null">id_sop_main = #{idSopMain},</if>
            <if test="taskBpmKeys != null">task_bpm_keys = #{taskBpmKeys},</if>
            <if test="fileIds != null">file_ids = #{fileIds},</if>
            <if test="sopContent != null">sop_content = #{sopContent},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_sop_detail = #{idSopDetail}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDetail">
        UPDATE clms_sop_detail
        SET id_sop_main = #{idSopMain},
            task_bpm_keys = #{taskBpmKeys},
            file_ids = #{fileIds},
            sop_content = #{sopContent},
            sort_order = #{sortOrder},
            valid_flag = #{validFlag},
            remark = #{remark},
            updated_by = #{updatedBy},
            sys_utime = #{sysUtime}
        WHERE id_sop_detail = #{idSopDetail}
    </update>

    <delete id="deleteByIdSopMain" parameterType="java.lang.String">
        DELETE FROM clms_sop_detail
        WHERE id_sop_main = #{idSopMain}
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM clms_sop_detail
        WHERE id_sop_detail = #{idSopDetail}
    </delete>

</mapper>