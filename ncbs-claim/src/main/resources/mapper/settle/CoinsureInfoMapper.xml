<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.CoinsureInfoMapper">

    <select id="getCoinsureCountByReportNo" resultType="java.lang.Integer">
        select count(1)
        from CLMS_COINSURE t
        where t.ID_AHCS_POLICY_INFO in
        (select ID_AHCS_POLICY_INFO
        from CLMS_POLICY_INFO
        where report_no = #{reportNo,jdbcType=VARCHAR})
    </select>

    <select id="getCoinsureDescByReportNo" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select a.POLICY_NO              policyNo,
               b.ACCEPT_INSURANCE_FLAG  acceptInsuranceFlag,
               b.REINSURE_SCALE         reinsureScale
        from clms_policy_info a, clms_coinsure b
        where a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
        and a.DEPARTMENT_CODE = b.REINSURE_COMPANY_CODE
        and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getCoinsureByPolicyNo" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select
            b.COINSURANCE_TYPE       coinsuranceType,
            b.REINSURE_SCALE         reinsureScale,
            b.REINSURE_COMPANY_CODE  reinsureCompanyCode,
            b.REINSURE_COMPANY_NAME  reinsureCompanyName
        from  clms_coinsure b
        where b.ID_AHCS_POLICY_INFO = (select ID_AHCS_POLICY_INFO  from clms_policy_info  where POLICY_NO = #{policyNo,jdbcType=VARCHAR} order by CREATED_DATE  desc limit 1)
    </select>

    <select id="getCoinsureListByReportNo" resultType="com.paic.ncbs.claim.model.dto.settle.CoinsureDTO">
        select a.POLICY_NO       policyNo,
        b.COINSURANCE_TYPE       coinsuranceType,
        b.ACCEPT_INSURANCE_FLAG  acceptInsuranceFlag,
        b.REINSURE_SCALE         reinsureScale,
        b.REINSURE_COMPANY_CODE  reinsureCompanyCode,
        b.REINSURE_COMPANY_NAME  reinsureCompanyName,
        IF(a.DEPARTMENT_CODE = b.REINSURE_COMPANY_CODE, '1', '0') companyFlag
        from clms_policy_info a, clms_coinsure b
        where a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
        and a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

</mapper>