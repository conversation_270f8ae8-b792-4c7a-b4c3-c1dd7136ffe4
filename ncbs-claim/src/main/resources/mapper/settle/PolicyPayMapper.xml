<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyPayBaseInfoDTO" id="policyPayBaseInfo">
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="policyCerNo" column="POLICY_CER_NO"/>
        <result property="policyPay" column="POLICY_PAY"/>
        <result property="idClmBatch" column="ID_CLM_BATCH"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.SpecialPromiseDTO" id="specialPromise">
        <result property="createdBy" column="CREATED_BY" />
        <result property="createdDate" column="CREATED_DATE" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="updatedDate" column="UPDATED_DATE" />
        <result property="idAhcsSpecialPromise" column="ID_AHCS_SPECIAL_PROMISE" />
        <result property="idAhcsPolicyInfo" column="ID_AHCS_POLICY_INFO" />
        <result property="promiseCode" column="PROMISE_CODE" />
        <result property="promiseDesc" column="PROMISE_DESC" />
        <result property="promiseType" column="PROMISE_TYPE" />
        <result property="businessType" column="BUSINESS_TYPE" />
        <result property="contentType" column="CONTENT_TYPE" />
        <result property="riskGroupName" column="RISK_GROUP_NAME" />
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO" id="payInfoFromPolicyCopy">
        <result property="idAhcsPolicyInfo" column="ID_AHCS_POLICY_INFO"/>
        <result property="idAhcsPolicyPay" column="ID_CLM_POLICY_PAY"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="departmentCode" column="DEPARTMENT_CODE"/>
        <result property="departmentChineseName" column="DEPARTMENT_CHINESE_NAME"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="productName" column="PRODUCT_NAME"/>
        <result property="insuranceBeginTime" column="INSURANCE_BEGIN_TIME"/>
        <result property="insuranceEndTime" column="INSURANCE_END_TIME"/>
        <result property="productClass" column="PRODUCT_CLASS"/>
        <result property="policyStatus" column="POLICY_STATUS"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="shareInsuredAmount" column="SHARE_INSURED_AMOUNT"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="indemnityConclusion" column="INDEMNITY_CONCLUSION"/>
        <association
                select="com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper.selectByReportNo"
                javaType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO"
                property="policyClaimCase" column="{caseNo=CASE_NO}"/>

        <!-- 用子查询关联险种信息, 引用sql   selectPlanInfo， 并把当前对象的 idAhcsPolicyPay 属性作为查询条件传给子查询语句-->
        <collection property="planPayArr"
                    ofType="com.paic.ncbs.claim.model.dto.settle.PlanPayDTO"
                    select="selectPlanInfo"
                    column="{idAhcsPolicyInfo = ID_AHCS_POLICY_INFO , riskGroupNo = risk_group_no}">
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO" id="policyPayInfo">
        <id property="idAhcsPolicyPay" column="ID_CLM_POLICY_PAY" />
        <id property="idAhcsPolicyInfo" column="ID_AHCS_POLICY_INFO" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="createdDate" column="CREATED_DATE" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="updatedDate" column="UPDATED_DATE" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="caseNo" column="CASE_NO" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="departmentCode" column="DEPARTMENT_CODE" />
        <result property="departmentChineseName" column="DEPARTMENT_CHINESE_NAME" />
        <result property="settleAmount" column="POLICY_PAY" />
        <result property="policyPrePay" column="POLICY_PRE_PAY" />
        <result property="indemnityMode" column="INDEMNITY_MODE" />
        <result property="indemnityConclusion" column="INDEMNITY_CONCLUSION" />
        <result property="policySumFee" column="POLICY_SUM_FEE" />
        <result property="policyPay" column="POLICY_PAY" />
        <result property="policyPreFee" column="POLICY_PRE_FEE" />
        <result property="policySumPay" column="POLICY_SUM_PAY" />
        <result property="policyDecreaseFee" column="DECREASE_FEE" />
        <result property="productCode" column="PRODUCT_CODE" />
        <result property="insuranceBeginTime" column="INSURANCE_BEGIN_TIME" />
        <result property="insuranceEndTime" column="INSURANCE_END_TIME" />
        <result property="policyStatus" column="POLICY_STATUS" />
        <result property="shareInsuredAmount" column="SHARE_INSURED_AMOUNT" />
        <result property="coinsuranceMark" column="COINSURANCE_MARK" />
        <result property="productPackage" column="PRODUCT_PACKAGE_TYPE" />
        <result property="riskGroupName" column="RISK_GROUP_NAME" />
        <association
                select="com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper.selectByReportNo"
                javaType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO"
                property="policyClaimCase" column="{caseNo=CASE_NO}" />
        <collection property="specialPromises"
                    ofType="com.paic.ncbs.claim.model.dto.settle.SpecialPromiseDTO"
                    select="selectSpecialPromise"
                    column="{idAhcsPolicyPay = ID_AHCS_POLICY_INFO }">
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO" id="prePolicyPay">
        <id property="idAhcsPolicyPay" column="ID_CLM_POLICY_PAY" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="createdDate" column="CREATED_DATE" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="updatedDate" column="UPDATED_DATE" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="caseNo" column="CASE_NO" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="settleAmount" column="POLICY_PAY" />
        <result property="policyPrePay" column="POLICY_PRE_PAY" />
        <result property="policyPreFee" column="POLICY_PRE_FEE" />
        <result property="policySumFee" column="POLICY_SUM_FEE" />
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.settle.EpcisRequestVO" id="epcisRequestVO">
        <result property="insuredCode" column="accept_no" />
        <result property="subpolicyNo" column="sub_policyno" />
    </resultMap>

    <select id="getPolicyPayAmount" resultType="java.math.BigDecimal">
        select POLICY_PAY from clm_policy_pay t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        and t.POLICY_NO = #{policyNo}
    </select>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyPayHisInfo" id="policyPayHisInfo">
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="policyCerNo" column="POLICY_CER_NO"/>
        <collection property="planList" resultMap="planPayHisInfo">
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PlanPayHisInfo" id="planPayHisInfo">
        <result property="caseNo" column="CASE_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="planName" column="PLAN_NAME"/>
        <collection property="dutyList" resultMap="dutyPayHisInfo">
        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.DutyPayHisInfo" id="dutyPayHisInfo">
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
        <result property="dutyFee" column="DUTY_FEE"/>
        <collection property="dutyDetailList" ofType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayHisInfo">
            <result property="settleAmount" column="SETTLE_AMOUNT"/>
            <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
            <result property="dutyDetailName" column="DUTY_DETAIL_NAME"/>
            <result property="dutyDetailBaseAmount" column="DETAIL_BASE_AMOUNT"/>
        </collection>
    </resultMap>

    <select id="queryCaseInfo" resultMap="policyPayHisInfo">
        select case_no,POLICY_CER_NO,policy_no, plan_code, plan_name, duty_code,duty_name,DUTY_FEE,
        duty_detail_code, duty_detail_name,DETAIL_BASE_AMOUNT,SETTLE_AMOUNT from (
        select cb.case_no,pi.POLICY_CER_NO, pi.policy_no, pp.plan_code, pp.plan_name, pd.duty_code,pd.duty_name,
        (select IFNULL(dp.arbitrate_fee, 0) + IFNULL(dp.lawsuit_fee, 0) + IFNULL(dp.lawyer_fee, 0) + IFNULL(dp.check_fee, 0) +
        IFNULL(dp.execute_fee, 0) + IFNULL(dp.evaluation_fee, 0)
        from clm_plan_duty_pay dp
        where dp.case_no = CB.CASE_NO
        and dp.case_times = cb.case_times
        and dp.plan_code = pp.plan_code
        and dp.duty_code = pd.duty_code
        and dp.claim_type != '2') DUTY_FEE,
        dd.duty_detail_code, dd.duty_detail_name,
        dd.duty_amount DETAIL_BASE_AMOUNT,
        (select IF(SETTLE_AMOUNT &gt; 0,SETTLE_AMOUNT,AUTO_SETTLE_AMOUNT) SETTLE_AMOUNT
        from CLMS_duty_detail_pay ddp
         where ddp.case_no =
        CB.CASE_NO
        and ddp.case_times =#{caseTimes}
        AND ddp.IS_EFFECTIVE = 'Y'
        and ddp.plan_code = pp.plan_code
        and ddp.policy_no = pi.policy_no
        and ddp.duty_code = pd.duty_code
        and ddp.duty_detail_code =
        dd.duty_detail_code
        and ddp.claim_type != '2') SETTLE_AMOUNT
        from CLMS_policy_info pi, CLMS_policy_plan pp, CLMS_policy_duty pd, CLMS_policy_duty_detail dd, clm_case_base CB
        where pi.id_ahcs_policy_info = pp.id_ahcs_policy_info
        and pi.case_no = cb.case_no
        and pp.id_ahcs_policy_plan = pd.id_ahcs_policy_plan
        and dd.id_ahcs_policy_duty = pd.id_ahcs_policy_duty
        and CB.POLICY_NO = PI.POLICY_NO
        and CB.REPORT_NO = PI.REPORT_NO
        and cb.case_times = #{caseTimes}
        and pi.report_no =#{reportNo}
        ) temp where IFNULL(temp.DUTY_FEE,0)>0 or temp.SETTLE_AMOUNT is not null
    </select>

    <!-- 根据报案号查询预配金额-->
    <select id="getPrePayAmount" resultType="java.math.BigDecimal">
        select sum(IFNULL(T.POLICY_PRE_PAY,0)) from CLM_POLICY_PAY T
        where T.REPORT_NO = #{reportNo} and T.CASE_TIMES = #{caseTimes}
        AND EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO A
        WHERE A.REPORT_NO = T.REPORT_NO
        AND T.CASE_NO = A.CASE_NO)
    </select>

    <select id="getPolicyPayBaseInfo" resultMap="policyPayBaseInfo">
        select distinct a.report_no,a.case_times,a.case_no,a.policy_no,
        b.POLICY_CER_NO,
        (select pp.policy_pay from clm_policy_pay pp where pp.case_no = a.case_no and pp.case_times=a.case_times)
        policy_pay,
        (select
        a.id_clm_batch from clm_batch a where
        a.report_no=#{reportNo} and
        a.case_times=#{caseTimes} and
        a.batch_settle_type='01') id_clm_batch from CLM_CASE_BASE a,clms_policy_info b where
        a.report_no=#{reportNo} and
        a.case_times=#{caseTimes}
        and a.report_no=b.report_no
        and a.policy_no=b.policy_no
        and a.case_no = b.case_no
    </select>

    <delete id="deletePolicyPays">
        delete from CLM_POLICY_PAY a
        where a.REPORT_NO = #{reportNo}
        and a.CASE_TIMES = #{caseTimes}
        and exists (select 1
        from CLMS_policy_info b
        where a.report_no = b.report_no
        and a.case_no = b.case_no)
    </delete>

    <select id="checkExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM CLM_PLAN_PAY WHERE CLM_PLAN_PAY.CLAIM_TYPE=#{claimType} AND
        CLM_PLAN_PAY.CASE_TIMES=#{caseTimes} AND CLM_PLAN_PAY.CASE_NO IN(
        SELECT CLM_POLICY_PAY.CASE_NO FROM CLM_POLICY_PAY
        WHERE CLM_POLICY_PAY.REPORT_NO=#{reportNo}
        AND CLM_POLICY_PAY.CASE_TIMES=#{caseTimes})
    </select>

    <!--  根据报案号从抄单信息中查询 	返回的 resultMap 中 含有关联查询的结果集，详见 payInfoFromPolicyCopy 的定义	-->
    <select id="selectFromPolicyCopy" resultMap="payInfoFromPolicyCopy">
        select
        pi.created_by,
        pi.updated_by,
        pi.id_ahcs_policy_info,
        pi.id_ahcs_policy_info ID_CLM_POLICY_PAY,
        pi.report_no,
        pi.policy_no,
        pi.POLICY_CER_NO,
        pi.department_code,
        (select department_abbr_name from department_define t where t.department_code = pi.department_code limit 1) as
        department_chinese_name,
        pi.product_code,
        PI.PRODUCT_NAME,
        (SELECT M.PRODUCT_CLASS FROM BASE_MARKETPRODUCT_INFO M WHERE M.MARKETPRODUCT_CODE= PI.product_code AND M.VERSION
        = PI.PRODUCT_VERSION limit 1) PRODUCT_CLASS,
        pi.business_type,
        pi.LAST_POLICY_NO,
        pi.INSURANCE_BEGIN_TIME,
        pi.INSURANCE_END_TIME,
        pi.PRODUCT_PACKAGE_TYPE,
        (SELECT a.sub_policyno
        FROM CLMS_INSURED_PERSON a
        where a.id_ahcs_policy_info = pi.id_ahcs_policy_info
        limit 1) SUBJECT_ID,
        pi.PRODUCT_VERSION,
        pi.POLICY_STATUS,
        pi.ENDORSE_SYSTEM_ID,
        PI.GENERATE_FLAG,
        PI.ORG_PRODUCT_CODE,
        PI.ORG_PRODUCT_NAME,
        PI.SELF_CARD_NO,
        cb.case_times,
        cb.case_no,
        cb.risk_group_no,
        IFNULL(PI.APPLY_NUM, 1) APPLY_NUM,
        (case PI.SHARE_INSURED_AMOUNT when '1' then 'Y' else 'N' end) SHARE_INSURED_AMOUNT,
        (select concat (CB.INDEMNITY_CONCLUSION,CASE WHEN CB.INDEMNITY_MODEL= '1' THEN '' ELSE CB.INDEMNITY_MODEL END) as
                    INDEMNITY_MODE from CLM_WHOLE_CASE_BASE CB where CB.REPORT_NO=#{reportNo} and
             CB.CASE_TIMES=#{caseTimes} limit 1) INDEMNITY_MODE,
        IFNULL(CB.INDEMNITY_CONCLUSION, '1') INDEMNITY_CONCLUSION
        from CLMS_policy_info pi, clm_case_base cb
        where pi.report_no = cb.report_no
        and cb.policy_no = pi.policy_no
        and cb.case_no = pi.case_no
        and cb.report_no=#{reportNo} and cb.case_times =#{caseTimes}
    </select>

    <select id="selectByReportNo" resultMap="policyPayInfo">
        SELECT
        PPAY.POLICY_NO,
        PPAY.CREATED_BY, PPAY.CREATED_DATE, PPAY.UPDATED_BY,
        PPAY.UPDATED_DATE,
        PI.id_ahcs_policy_info id_ahcs_policy_info,
        PI.PRODUCT_CODE ,
        PI.POLICY_STATUS ,
        (SELECT a.sub_policyno
        FROM CLMS_INSURED_PERSON a
        where a.id_ahcs_policy_info = pi.id_ahcs_policy_info
        LIMIT 1) SUBJECT_ID ,
        (SELECT a.SCHEME_NAME
         FROM CLMS_INSURED_PERSON a
         where a.id_ahcs_policy_info = pi.id_ahcs_policy_info
         LIMIT 1) RISK_GROUP_NAME ,
        PI.PRODUCT_PACKAGE_TYPE ,
        PI.POLICY_VALID ,
        PI.PRODUCT_VERSION ,
        PI.INSURANCE_BEGIN_TIME ,
        PI.INSURANCE_END_TIME ,
        PI.BUSINESS_TYPE,
        PI.DEPARTMENT_CODE,
        PI.POLICY_CER_NO,
        PI.COINSURANCE_MARK,
        (case PI.SHARE_INSURED_AMOUNT when '1' then 'Y' else 'N' end) SHARE_INSURED_AMOUNT,
        (SELECT 'Y' FROM CLMS_COINSURE AC WHERE AC.ID_AHCS_POLICY_INFO=PI.ID_AHCS_POLICY_INFO AND
        AC.ACCEPT_INSURANCE_FLAG='0' AND AC.COINSURANCE_TYPE='0' AND AC.REINSURE_COMPANY_CODE='3005' LIMIT 1)
        IS_EXTERNAL_COINSURE,
        (SELECT 'Y' FROM CLM_COMMON_PARAMETER T WHERE T.COLLECTION_CODE='AHCS_PREPAID_PRODUCT' AND
        t.VALUE_CODE=IFNULL(PI.ORG_PRODUCT_CODE,PI.PRODUCT_CODE)) IS_PREPAID,
        PPAY.ID_CLM_POLICY_PAY,
        PPAY.REPORT_NO,
        PPAY.CASE_TIMES,
        PPAY.CASE_NO,
        PPAY.POLICY_NO,
        PPAY.POLICY_PAY,POLICY_PRE_PAY,POLICY_PRE_FEE, PPAY.POLICY_SUM_FEE, PPAY.DECREASE_FEE,PPAY.POLICY_SUM_PAY,
        (SELECT DEPARTMENT_ABBR_NAME FROM DEPARTMENT_DEFINE T WHERE T.DEPARTMENT_CODE = PI.DEPARTMENT_CODE) AS
        DEPARTMENT_CHINESE_NAME,
        (select concat (CB.INDEMNITY_CONCLUSION,CASE WHEN CB.INDEMNITY_MODEL= '1' THEN '' ELSE CB.INDEMNITY_MODEL END) as
        INDEMNITY_MODE from CLM_WHOLE_CASE_BASE CB where CB.REPORT_NO=#{reportNo} and
        CB.CASE_TIMES=#{caseTimes}) INDEMNITY_MODE,
        IFNULL(CB.INDEMNITY_CONCLUSION,'1') INDEMNITY_CONCLUSION
        FROM CLM_POLICY_PAY PPAY,clms_POLICY_INFO PI, CLM_CASE_BASE CB
        WHERE PPAY.REPORT_NO=#{reportNo}
        AND PPAY.CASE_TIMES=#{caseTimes}
        AND PI.CASE_NO = PPAY.CASE_NO
        AND PI.POLICY_NO = PPAY.POLICY_NO
        AND PI.REPORT_NO = PPAY.REPORT_NO
        AND PI.CASE_NO = CB.CASE_NO
        AND PPAY.CASE_TIMES = CB.CASE_TIMES
        AND PI.POLICY_NO = CB.POLICY_NO
        order by PPAY.POLICY_NO
    </select>

    <select id="getPrePolicyPays" resultMap="prePolicyPay">
        select
        a.REPORT_NO,
        a.CASE_TIMES,
        a.CASE_NO,
        a.POLICY_PRE_PAY,
        a.POLICY_PRE_FEE,
        a.POLICY_NO
        from CLM_POLICY_PAY a
        where a.REPORT_NO = #{reportNo}
        and a.CASE_TIMES = #{caseTimes}
        and (a.POLICY_PRE_PAY is not null or a.POLICY_PRE_FEE is not null)
        and exists (select 1
        from CLMS_policy_info b
        where a.report_no = b.report_no
        and a.case_no = b.case_no)
    </select>

    <select id="getEpicsRequest" resultMap="epcisRequestVO" >
        select distinct ip.accept_no, ip.sub_policyno,pi.policy_no,ip.client_no
        from CLMS_insured_person ip, CLMS_policy_info pi
        where pi.id_ahcs_policy_info = ip.id_ahcs_policy_info
        and pi.policy_no = #{policyNo}
        and ip.client_no = #{clientNo}
        <if test="reportNo != null and reportNo != '' ">
            and pi.report_no = #{reportNo}
        </if>
        LIMIT 1
    </select>

    <select id="selectSpecialPromise" resultMap="specialPromise">
        select sp.created_by,
        sp.updated_by,
        sp.created_date,
        sp.updated_date,
        sp.id_ahcs_special_promise,
        sp.id_ahcs_policy_info,
        sp.promise_code,
        sp.promise_desc,
        sp.promise_type,
        sp.business_type,
        sp.content_type,
        sp.risk_group_name
        from CLMS_SPECIAL_PROMISE sp
        where sp.ID_AHCS_POLICY_INFO = #{idAhcsPolicyPay}
    </select>
    <select id="getSpecialPromise" resultType="com.paic.ncbs.claim.model.dto.settle.SpecialPromiseDTO">
        select sp.created_by,
        sp.updated_by,
        sp.created_date,
        sp.updated_date,
        sp.id_ahcs_special_promise,
        sp.id_ahcs_policy_info,
        sp.promise_code,
        sp.promise_desc,
        sp.promise_type,
        sp.business_type,
        sp.content_type
        from CLMS_SPECIAL_PROMISE sp
        where sp.ID_AHCS_POLICY_INFO = #{idAhcsPolicyPay}
    </select>

    <!-- 这是一个关联查询的子查询，入参来自resultMap "payInfoFromPolicyCopy"的属性"planPayArr" ，column="{idAhcsPolicyPay = ID_AHCS_POLICY_INFO }"
    返回的结果集定义详见 ochcs-settle\src\main\resources\mybaPlanPayDaoMapper.xml   中的"planPayInfo"   -->
    <select id="selectPlanInfo" resultMap="com.paic.ncbs.claim.dao.mapper.settle.PlanPayMapper.planPayInfo">
        select  pp.created_by,pp.updated_by,pp.plan_code,pp.org_plan_code,pp.plan_name,pp.id_ahcs_policy_plan,0 plan_no,PP.GROUP_CODE,IFNULL(pp.apply_num, 1) apply_num,
                id_ply_risk_group
        from CLMS_policy_plan pp
        where pp.ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo}
        <if test="riskGroupNo != null and riskGroupNo != '' ">
            and pp.risk_group_no = #{riskGroupNo}
        </if>
        order by PP.GROUP_CODE,pp.PLAN_CODE
    </select>

    <update id="updatePolicyPayInfoList" >
            update CLM_POLICY_PAY
            set
            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="reportNo != null and reportNo != '' ">
                REPORT_NO = #{reportNo},
            </if>
            <if test="caseTimes != null ">
                CASE_TIMES = #{caseTimes},
            </if>
            <if test="caseNo != null and caseNo != '' ">
                CASE_NO = #{caseNo},
            </if>
            <if test="policyNo != null and policyNo != '' ">
                POLICY_NO = #{policyNo},
            </if>
            POLICY_PAY = #{settleAmount},
            POLICY_SUM_FEE = #{policySumFee},
            POLICY_SUM_PAY = #{policySumPay},
            DECREASE_FEE = #{policyDecreaseFee},
            POLICY_SUM_ESTIMATE = #{policySumEstimate},
            POLICY_PRE_PAY = #{policyPrePay},

            <if test="claimType ==2">
                POLICY_PRE_FEE = POLICY_PRE_FEE + #{policyPreFee},
            </if>
            <if test="claimType !=2">
                POLICY_PRE_FEE = #{policyPreFee},
            </if>
            REFUSE_AMOUNT = #{refuseAmount},
            UPDATED_DATE = now()
            where ID_CLM_POLICY_PAY=#{idAhcsPolicyPay}
    </update>

    <!-- 保单赔付 批量插入 -->
    <insert id="insertPolicyPayInfoList" parameterType="java.util.List">
    insert into CLM_POLICY_PAY (CREATED_BY, CREATED_DATE, UPDATED_BY,
    UPDATED_DATE, ID_CLM_POLICY_PAY, REPORT_NO, CASE_TIMES, CASE_NO,
    POLICY_NO, POLICY_PAY, POLICY_SUM_FEE,DECREASE_FEE,POLICY_PRE_PAY,POLICY_SUM_PAY,POLICY_PRE_FEE,
    POLICY_SUM_ESTIMATE,REFUSE_AMOUNT,CURRENCY_CODE,MIGRATE_FROM,ARCHIVE_DATE)
        values
    <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.createdBy},
        now(),
        #{item.updatedBy},
        now(),
        #{item.idAhcsPolicyPay},
        #{item.reportNo},
        #{item.caseTimes},
        #{item.caseNo},
        #{item.policyNo},
        #{item.settleAmount},
        #{item.policySumFee},
        #{item.policyDecreaseFee},
        #{item.policyPrePay},
        #{item.policySumPay},
        #{item.policyPreFee},
        #{item.policySumEstimate},
        #{item.refuseAmount},
        '01',
        'na',
        SYSDATE()
        )
    </foreach>

    </insert>

    <select id="getPolicyPayTotal" resultType="java.math.BigDecimal">
        select sum(t.policy_pay) from clm_policy_pay t where t.report_no = #{reportNo} and t.case_times = #{caseTimes}
    </select>

    <!-- 根据报案号赔付次数查询赔付总金额(包含预赔赔款和预赔费用) -->
    <select id="getSumPayFee" resultType="java.math.BigDecimal">
        select IFNULL(sum(pp.POLICY_SUM_PAY) , 0) amt
        from CLM_POLICY_PAY pp
        where pp.REPORT_NO=#{reportNo}
        and pp.CASE_TIMES=#{caseTimes}
    </select>

    <select id="getPolicyPayListByReportNo" resultType="com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO">
        SELECT
        PPAY.ID_CLM_POLICY_PAY idAhcsPolicyPay,
        PPAY.REPORT_NO reportNo,
        PPAY.CASE_TIMES caseTimes,
        PPAY.CASE_NO caseNo,
        PPAY.POLICY_NO policyNo,
        PPAY.POLICY_PRE_PAY policyPrePay,
        PPAY.POLICY_SUM_FEE policySumFee,
        PPAY.POLICY_PRE_FEE policyPreFee,
        PPAY.POLICY_SUM_PAY policySumPay
        FROM CLM_POLICY_PAY PPAY
        WHERE PPAY.REPORT_NO=#{reportNo,jdbcType=VARCHAR}
        AND PPAY.CASE_TIMES=#{caseTimes,jdbcType=INTEGER}
        AND EXISTS (SELECT 1
        FROM CLMS_POLICY_INFO A
        WHERE PPAY.REPORT_NO = A.REPORT_NO
        AND PPAY.CASE_NO = A.CASE_NO)
    </select>

    <select id="getSimplePolicyDutyList" resultMap="policyPayInfo"  >
        SELECT 	PI.ID_AHCS_POLICY_INFO , PI.REPORT_NO ,  PCC.CASE_NO, PCC.POLICY_NO
        FROM CLMS_POLICY_CLAIM_CASE PCC INNER JOIN CLMS_POLICY_INFO PI
        ON PI.POLICY_NO = PCC.POLICY_NO AND PI.REPORT_NO = PCC.REPORT_NO
        WHERE PCC.REPORT_NO=#{reportNo,jdbcType=VARCHAR}
    </select>

    <!-- 批量更新保单赔付信息 预赔 -->
    <update id="updatePolicyPayListForPrePay" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open=""
                 separator=";" close="">
            update CLM_POLICY_PAY
            set UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
            UPDATED_DATE = now(),
            POLICY_SUM_FEE = #{item.policySumFee,jdbcType=NUMERIC},
            POLICY_SUM_PAY = #{item.policySumPay,jdbcType=NUMERIC},
            POLICY_PRE_PAY = #{item.policyPrePay,jdbcType=NUMERIC},
            POLICY_PRE_FEE = #{item.policyPreFee,jdbcType=NUMERIC}
            where ID_CLM_POLICY_PAY=#{item.idAhcsPolicyPay}
        </foreach>
    </update>

    <!-- 根据报案号赔付次数查询赔付总金额 -->
    <select id="getPolicyPay" resultType="java.math.BigDecimal">
        select IFNULL(sum(pp.POLICY_PAY) , 0)
        from CLM_POLICY_PAY pp
        where pp.REPORT_NO=#{reportNo}
        and pp.CASE_TIMES=#{caseTimes}
    </select>

    <select id="policyHasPrePay" resultType="int">
        select count(1)
        from CLM_POLICY_PAY a
        where a.REPORT_NO = #{reportNo}
          and a.CASE_TIMES = #{caseTimes}
          and a.POLICY_NO = #{policyNo}
          and (a.POLICY_PRE_PAY is not null or a.POLICY_PRE_FEE is not null)
          and exists (select 1
                      from CLMS_policy_info b
                      where a.report_no = b.report_no
                        and a.case_no = b.case_no)
    </select>

    <select id="getPolicyPayByPolicyNo" resultMap="policyPayBaseInfo">
        SELECT POLICY_PAY
        FROM CLM_POLICY_PAY p
        WHERE p.REPORT_NO=#{reportNo}
        AND p.CASE_TIMES=#{caseTimes}
        AND p.POLICY_NO=#{policyNo}
        AND EXISTS (SELECT 1 FROM CLM_WHOLE_CASE_BASE c WHERE c.REPORT_NO = p.REPORT_NO and c.CASE_TIMES = p.CASE_TIMES and c.WHOLE_CASE_STATUS = '0' and c.INDEMNITY_CONCLUSION = '1')
    </select>

    <select id="getPolicyHistoryPayAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(IFNULL(a.policy_pay, 0)), 0)
        FROM clm_policy_pay a, clm_whole_case_base b,
        (
            SELECT MAX(case_times) case_times, report_no
            FROM clm_policy_pay
            WHERE policy_no = #{policyNo}
            GROUP BY report_no
        ) c
        WHERE a.report_no = b.report_no
        AND a.case_times = b.case_times
        AND a.report_no = c.report_no
        AND a.case_times = c.case_times
        AND b.whole_case_status = '0'
        AND a.policy_no = #{policyNo}
        AND b.report_no != #{reportNo}
    </select>

    <select id="getPolicyHistoryPrePayAmount" parameterType="com.paic.ncbs.claim.model.vo.settle.MaxPayParam" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(IFNULL(a.policy_pre_pay, 0)), 0)
        FROM clm_policy_pay a, clm_whole_case_base b,
        (
            SELECT MAX(case_times) case_times, report_no
            FROM clm_policy_pay
            WHERE policy_no = #{policyNo}
            GROUP BY report_no
        ) c
        WHERE a.report_no = b.report_no
        AND a.case_times = b.case_times
        AND a.report_no = c.report_no
        AND a.case_times = c.case_times
        AND b.whole_case_status != '0'
        AND a.policy_no = #{policyNo}
        AND b.report_no != #{reportNo}
    </select>
    <select id="getPolicyProductCode" resultType="java.lang.String">
        select PRODUCT_CODE from clms_policy_info where REPORT_NO = #{reportNo}
    </select>
    <select id="getBackSumPayByProductCode" resultType="com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO" >
        select  distinct  c.REPORT_NO reportNo,c.POLICY_PAY policyPay,c.case_times  caseTimes
        from clms_policy_info a ,clm_whole_case_base b,clm_policy_pay c
        where a.PRODUCT_CODE=#{productCode}
        and a.report_no=b.report_no
        and b.WHOLE_CASE_STATUS='0'
        and b.CASE_TIMES=c.CASE_TIMES
        and b.report_no=c.report_no
        and c.POLICY_PAY>0
        and b.END_CASE_DATE BETWEEN #{startDate} and #{endDate}
    </select>
    <!-- 查询案件总数  包含零注拒赔 等-->
    <select id="getAllContByProductCode" resultType="java.lang.Integer" >
        select   count(distinct b.REPORT_NO )
        from clms_policy_info a ,clm_whole_case_base b
        where a.PRODUCT_CODE=#{productCode}
        and a.report_no=b.report_no
        and b.WHOLE_CASE_STATUS='0'
        and b.CASE_TIMES=1
        and b.END_CASE_DATE BETWEEN #{startDate} and #{endDate}
    </select>
    <select id="getCaseNo" resultType="java.lang.String">
        select CASE_NO from clm_policy_pay where
        REPORT_NO=#{reportNo}
        and CASE_TIMES=#{caseTimes}
    </select>
    <select id="getDutyFeeByCaseNo" resultType="java.math.BigDecimal">
        select sum(IFNULL(dp.arbitrate_fee, 0) + IFNULL(dp.lawsuit_fee, 0)
                + IFNULL(dp.lawyer_fee, 0) + IFNULL(dp.check_fee, 0)
                + IFNULL(dp.execute_fee, 0) + IFNULL(dp.evaluation_fee, 0) )
        from clm_plan_duty_pay dp
        where dp.case_no = #{caseNo}
        and dp.case_times = #{caseTimes}
        and dp.claim_type != '2'
    </select>
    <select id="getSettleAmountByCaseNo" resultType="java.math.BigDecimal">
        select sum(IF(SETTLE_AMOUNT > 0,SETTLE_AMOUNT,AUTO_SETTLE_AMOUNT))
        from CLMS_duty_detail_pay ddp
        where ddp.case_no = #{caseNo}
        and ddp.case_times = #{caseTimes}
        AND ddp.IS_EFFECTIVE = 'Y'
        and ddp.claim_type != '2'
    </select>
    <select id="selectSumFee" resultType="java.math.BigDecimal">
        select sum(POLICY_SUM_FEE) from clm_policy_pay where report_no=#{reportNo};
    </select>
</mapper>